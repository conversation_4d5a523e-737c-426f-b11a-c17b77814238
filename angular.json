{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"bors": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss", "inlineStyle": false, "inlineTemplate": false, "prefix": "app", "skipTests": true, "changeDetection": "OnPush"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "stylePreprocessorOptions": {"includePaths": ["src/scss"]}, "allowedCommonJsDependencies": ["http-proxy-middleware", "https-proxy-agent", "flatpickr"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", {"glob": "**/*", "input": "src/assets/images/favicons", "output": "/"}, {"glob": "manifest.json", "input": "src/assets/json", "output": "/"}, {"glob": "robots.txt", "input": "src", "output": "/"}, {"glob": "ads.txt", "input": "src", "output": "/"}, {"glob": "**/*", "input": "node_modules/@trendency/kesma-ui/assets", "output": "/assets"}], "styles": ["node_modules/swiper/swiper-bundle.css", "src/scss/styles.scss"], "scripts": ["src/assets/vendors/embedly/platform.js", "src/assets/vendors/instagram/instagram.js", "src/assets/vendors/newsletter-script.js"], "preserveSymlinks": true, "server": "src/main.server.ts", "prerender": false, "ssr": {"entry": "src/server.ts"}}, "configurations": {"production": {"optimization": {"fonts": true, "scripts": true, "styles": {"inlineCritical": false, "minify": true}}, "budgets": [{"type": "initial", "maximumWarning": "1mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "30kb", "maximumError": "200kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "test": {"optimization": {"fonts": true, "scripts": true, "styles": {"inlineCritical": false, "minify": true}}, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "200kb", "maximumError": "10mb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.test.ts"}]}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}]}, "local-ssr": {"optimization": false, "extractLicenses": false, "sourceMap": true}, "local": {"optimization": false, "extractLicenses": false, "sourceMap": true, "ssr": false}}, "defaultConfiguration": "local"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "bors:build:production"}, "test": {"buildTarget": "bors:build:test"}, "development": {"buildTarget": "bors:build:development"}, "local": {"buildTarget": "bors:build:local"}}, "defaultConfiguration": "local"}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"analytics": "d350187e-d3c4-43d0-9b8b-d4f4eba16a19", "schematicCollections": ["@angular-eslint/schematics"], "cache": {"enabled": false}}, "schematics": {"@angular-eslint/schematics:application": {"setParserOptionsProject": true}, "@angular-eslint/schematics:library": {"setParserOptionsProject": true}}}