@use 'variables' as *;

@function breakpoint-next($name, $breakpoints: $breakpoints, $breakpoint-names: map-keys($breakpoints)) {
  $n: index($breakpoint-names, $name);
  @return if($n !=null and $n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);
}

@function breakpoint-min($name, $breakpoints: $breakpoints) {
  $min: map-get($breakpoints, $name);
  @return if($min !=0, $min, null);
}

@function breakpoint-max($name, $breakpoints: $breakpoints) {
  $next: breakpoint-next($name, $breakpoints);
  @return if($next, breakpoint-min($next, $breakpoints) - 0.02, null);
}

@function breakpoint-infix($name, $breakpoints: $breakpoints) {
  @return if(breakpoint-min($name, $breakpoints) ==null, '', '-#{$name}');
}

@mixin media-breakpoint-up($name, $breakpoints: $breakpoints) {
  $min: breakpoint-min($name, $breakpoints);

  @if $min {
    @media (min-width: $min) {
      @content;
    }
  } @else {
    @content;
  }
}

@mixin media-breakpoint-down($name, $breakpoints: $breakpoints) {
  $max: breakpoint-max($name, $breakpoints);

  @if $max {
    @media (max-width: $max) {
      @content;
    }
  } @else {
    @content;
  }
}

@mixin media-breakpoint-between($lower, $upper, $breakpoints: $breakpoints) {
  $min: breakpoint-min($lower, $breakpoints);
  $max: breakpoint-max($upper, $breakpoints);

  @if $min !=null and $max !=null {
    @media (min-width: $min) and (max-width: $max) {
      @content;
    }
  } @else if $max==null {
    @include media-breakpoint-up($lower, $breakpoints) {
      @content;
    }
  } @else if $min==null {
    @include media-breakpoint-down($upper, $breakpoints) {
      @content;
    }
  }
}

@mixin media-breakpoint-only($name, $breakpoints: $breakpoints) {
  $min: breakpoint-min($name, $breakpoints);
  $max: breakpoint-max($name, $breakpoints);

  @if $min !=null and $max !=null {
    @media (min-width: $min) and (max-width: $max) {
      @content;
    }
  } @else if $max==null {
    @include media-breakpoint-up($name, $breakpoints) {
      @content;
    }
  } @else if $min==null {
    @include media-breakpoint-down($name, $breakpoints) {
      @content;
    }
  }
}

// Container of at least the minimum breakpoint width. No query for the smallest breakpoint.
// Makes the @content apply to the given breakpoint and wider.
@mixin container-breakpoint-up($name, $breakpoints: $container-breakpoints) {
  $min: breakpoint-min($name, $breakpoints);
  @if $min {
    @container (min-width: #{$min}) {
      @content;
    }
  } @else {
    @content;
  }
}

// Container of at most the maximum breakpoint width. No query for the largest breakpoint.
// Makes the @content apply to the given breakpoint and narrower.
@mixin container-breakpoint-down($name, $breakpoints: $container-breakpoints) {
  $max: breakpoint-max($name, $breakpoints);
  @if $max {
    @container (max-width: #{$max}) {
      @content;
    }
  } @else {
    @content;
  }
}

// Container that spans multiple breakpoint widths.
// Makes the @content apply between the min and max breakpoints
@mixin container-breakpoint-between($lower, $upper, $breakpoints: $container-breakpoints) {
  $min: breakpoint-min($lower, $breakpoints);
  $max: breakpoint-max($upper, $breakpoints);

  @if $min != null and $max != null {
    @container (min-width: #{$min}) and (max-width: #{$max}) {
      @content;
    }
  } @else if $max == null {
    @include container-breakpoint-up($lower, $breakpoints) {
      @content;
    }
  } @else if $min == null {
    @include container-breakpoint-down($upper, $breakpoints) {
      @content;
    }
  }
}

// Container between the breakpoint's minimum and maximum widths.
// No minimum for the smallest breakpoint, and no maximum for the largest one.
// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.
@mixin container-breakpoint-only($name, $breakpoints: $container-breakpoints) {
  $min: breakpoint-min($name, $breakpoints);
  $next: breakpoint-next($name, $breakpoints);
  $max: breakpoint-max($next, $breakpoints);

  @if $min != null and $max != null {
    @container (min-width: #{$min}) and (max-width: #{$max}) {
      @content;
    }
  } @else if $max == null {
    @include container-breakpoint-up($name, $breakpoints) {
      @content;
    }
  } @else if $min == null {
    @include container-breakpoint-down($next, $breakpoints) {
      @content;
    }
  }
}
