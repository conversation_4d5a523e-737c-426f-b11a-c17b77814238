// Firefox only
@mixin ffonly() {
  @-moz-document url-prefix() {
    @content;
  }
}

@mixin icon($name) {
  display: inline-block;
  min-width: 5px;
  min-height: 5px;
  background-position: center;
  background-size: contain;
  background-repeat: no-repeat;
  background-image: url('/assets/images/' + $name);
}

@mixin stickyLayoutElement() {
  position: sticky;
  top: 110px;
}

@mixin layoutMakeExplicitColumnsSticky() {
  /**
    Make sticky elements when a column has an inner row which is the last element in the column!
   */
  .row-element .column-element {
    height: 100%;
    //We need to eliminate rows that are after another row
    .row-element:last-child:not(.row-element ~ .row-element) {
      @include stickyLayoutElement();
    }
  }
}
