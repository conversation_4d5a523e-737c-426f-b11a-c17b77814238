// Wrapper width
$global-wrapper-width: 1312px;
$global-wrapper-width-with-bg: 1376px;

// Images path
$images-path: '/assets/images/';

// Breakpoints
$breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px,
);

$container-breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px,
);

$grid-columns: 12;
$grid-gutter-width: 32px;

$container-max-widths: (
  sm: 540px,
  md: 720px,
  lg: 960px,
  xl: 1140px,
);

// Aside
$aside-width: 416px;
$block-bottom-margin: 30px;

//Transitions
$button-hover-transition: all 400ms cubic-bezier(0.47, 1.64, 0.41, 0.8);

// Variables
$white: #ffffff;
$white-o90: rgba(255, 255, 255, 0.9);
$black: #000000;
$black-200: #ccd3d6;
$black-600: #667a85;
$black-950: #002234; //Figma Bors-Black
$black-1100: #001f2f;
$black-1200: #001b2a;
$gray-50: #f6f6f7;
$gray-100: #f5f5f5;
$gray-150: #e0e3e7;
$gray-200: #e5e9eb;
$gray-250: #f2f4f5;
$gray-300: #ccd3d6;
$gray-200-o20: rgba(0, 34, 52, 0.2);
$gray-600: #434343;
$gray-900: #1a1a1a;
$red-300: #ff5656;
$red-400: #ff0043;
$red-500: #e2003b;
$red-550: #cb0035;
$red-600: #d2122d;
$red-700: #b5002f;
$red-800: #aa002c;
$red-1100: #cb0035;
$purple-100: #b82092;
$purple-200: #795bcd;
$purple-300: #911cae;
$purple-exclusive: #7a5acf;
$purple-exclusive-500: #bcace7;
$blue-400: #1678a9;
$blue-700: #00337a;
$dark-blue-800: #363d59;
$green-100: #45cd64;
$green-200: #178081;
$green-400: #027b0f;
$green-500: #677b02;
$yellow-200: #ffff85;
$yellow-300: #fbff00;
$yellow-400: #d4b37b;
$yellow-500: #ffae00;
$yellow-550: #ffb200;
$orange-100: #ffefe5;
$orange-500: #ff6200;

// Lexicon
$blue-dark: #2b4168;
$grey-1: #eee;
$yellow: #ffbd45;
$grey-100: #d1d1d1;
$dark-red: #b8273b;
$grey-8: #3d3d3d;
