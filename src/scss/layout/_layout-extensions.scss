@use 'shared' as *;

section {
  .wrapper.with-aside {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-top: 40px;

    > .left-column {
      display: block;
      width: calc(100% - #{$aside-width} - 40px);

      @include media-breakpoint-down(md) {
        width: 100%;
      }
    }

    > aside {
      display: block;
      width: $aside-width;
      margin-bottom: 30px;

      @include media-breakpoint-down(md) {
        width: 100%;
      }

      > * {
        margin-bottom: $block-bottom-margin;
      }

      .aside-box {
        app-article-card {
          display: block;
          margin-bottom: 30px;
        }
      }

      app-advertisement {
        display: block;

        @include media-breakpoint-down(md) {
          margin-left: -15px;
          margin-right: -15px;
          width: calc(100% + 30px);
        }
      }
    }
  }
}
