<!doctype html>
<html lang="hu">
  <head>
    <base href="/" />
    <title>BorsOnline</title>

    <meta charset="utf-8" />
    <meta name="robots" content="index, follow, max-image-preview:large" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#e2003b" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link href="/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180" />
    <link href="/favicon-32x32.png" rel="icon" sizes="32x32" type="image/png" />
    <link href="/favicon-16x16.png" rel="icon" sizes="16x16" type="image/png" />
    <link rel="manifest" href="/manifest.json" />
    <meta content="/browserconfig.xml" name="msapplication-config" />
    <meta content="#e2003b" name="msapplication-TileColor" />
    <meta content="#ffffff" name="theme-color" />
    <link rel="alternate" type="application/rss+xml" title="Friss hírek" href="https://www.borsonline.hu/publicapi/hu/rss/bors/articles" />

    <script class="structured-data" type="application/ld+json"></script>

    <script src="/assets/scripts/inmobi.js"></script>
    <!-- InMobi Choice. Consent Manager Tag v3.0 (for TCF 2.2) -->
    <script type="text/javascript" async>
      // Make sure we call gemius init only after InMobi is loaded and inited
      console.log(' >> initializing InMobi ready callback for AdOcean');
      const inMobiReadyCallback = () => {
        console.log(' >> InMobi ready');

        if (!initAdOcean) {
          console.warn(' >> << no adocean init found');
        }
        !adOceanInited && initAdOcean && initAdOcean();
      };

      InMobiHandler.init('gq2uc_c-uMyQL', 'www.borsonline.hu', inMobiReadyCallback);
    </script>
    <!-- End InMobi Choice. Consent Manager Tag v3.0 (for TCF 2.2) -->

    <script type="text/javascript">
      var pp_gemius_identifier = 'nXblnbOxpYU_uhguHmC_VZZW3yiFlPtF0fxOn20TBQT.p7';
      var pp_gemius_use_cmp = true;
      var pp_gemius_cmp_timeout = Infinity;

      function gemius_pending(i) {
        window[i] =
          window[i] ||
          function () {
            var x = (window[i + '_pdata'] = window[i + '_pdata'] || []);
            x[x.length] = arguments;
          };
      }

      gemius_pending('gemius_hit');
      gemius_pending('gemius_event');
      gemius_pending('pp_gemius_hit');
      gemius_pending('pp_gemius_event');
      (function (d, t) {
        try {
          var gt = d.createElement(t),
            s = d.getElementsByTagName(t)[0],
            l = 'http' + (location.protocol === 'https:' ? 's' : '');
          gt.setAttribute('async', 'async');
          gt.setAttribute('defer', 'defer');
          gt.src = l + '://gahu.hit.gemius.pl/xgemius.js';
          s.parentNode.insertBefore(gt, s);
        } catch (e) {}
      })(document, 'script');
    </script>

    <script>
      window.adocf = {
        useDOMContentLoaded: true,
      };

      window.PRE_adocean_queue = [];
      window.adOceanInited = false;

      window.adoQueueFn = (fn, args) => {
        console.log(' <> queuing ado call: %s', fn, args);
        PRE_adocean_queue.push({ fn, args });
      };
    </script>

    <!-- AD OCEAN GEMIUS -->
    <script type="text/javascript" src="https://hu.adocean.pl/files/js/ado.js"></script>
    <script type="text/javascript">
      /* (c)AdOcean 2003-2020 */

      window.initAdOcean = () => {
        console.log(' >> init AdOcean');
        if (typeof window.ado !== 'object') {
          window.ado = {};
          window.ado.config = window.ado.preview = window.ado.placement = window.ado.master = window.ado.slave = function () {};
        }

        window.ado.config({
          mode: 'new',
          xml: false,
          consent: true,
          characterEncoding: true,
          attachReferrer: true,
          fpc: 'auto',
          defaultServer: 'hu.adocean.pl',
          cookieDomain: 'SLD',
        });

        window.ado.preview({ enabled: true });

        window.adOceanInited = true;
        window.PRE_adocean_queue.forEach(({ fn, args }) => {
          console.log(' >>> replaying adOcean queue: %s:', fn, args);
          window.ado[fn](...args);
        });
      };

      window.setTimeout(() => {
        if (!window.adOceanInited && window.Ado) {
          console.warn(' >> GFC initialization timeout: activating AdOcean');
          window.initAdOcean();
        } else if (!window.Ado) {
          console.error(' <!> GFC initialization timeout: AdOcean is not loaded! Aborting');
        }
      }, 30000);
    </script>

    <!-- AD OCEAN GEMIUS -->
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-8559195417632426" crossorigin="anonymous"></script>
    <script async src="https://adat.borsonline.hu/script/index.min.v2.js"></script>
  </head>
  <body>
    <div id="init-loader" class="fullscreen-loader"></div>
    <app-root></app-root>
    <script src="/assets/scripts/init-loader.js"></script>
    <script async defer src="/assets/scripts/version.js"></script>
  </body>
</html>
