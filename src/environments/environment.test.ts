import { Environment } from '@trendency/kesma-core';

// UAT teszt környezet
export const environment: Environment = {
  production: true,
  type: 'beta',
  apiUrl: 'http://borsfe.apptest.content.private/publicapi/hu',
  secureApiUrl: 'http://borsfe.apptest.content.private/secureapi/hu',
  personalizedRecommendationApiUrl: 'https://terelo.app.content.private/api',
  facebookAppId: '287208751435588',
  translation: {
    defaultLocale: 'hu',
    locales: ['hu', 'en'],
  },
  siteUrl: 'http://borsfe.apptest.content.private',
  googleSiteKey: '6LdOdtgaAAAAADOpTzcEuDkf-oSP16hxYrVwhHR1',
  googleTagManager: 'GTM-TF2DM9H',
  gemiusId: 'nXblnbOxpYU_uhguHmC_VZZW3yiFlPtF0fxOn20TBQT.p7',
  httpReqTimeout: 30, // second
  sentry: {
    dsn: 'https://<EMAIL>/4',
    tracingOrigins: ['http://borsfe.apptest.content.private'],
    sampleRate: 0.1,
    tracesSampleRate: 0.1,
    profilesSampleRate: 0.1,
  },
};
