import { Environment } from '@trendency/kesma-core';

// <PERSON><PERSON>
export const environment: Environment = {
  production: true,
  type: 'prod',
  apiUrl: {
    clientApiUrl: 'https://www.borsonline.hu/publicapi/hu',
    serverApiUrl: 'http://borsfeapi.app.content.private/publicapi/hu',
  },
  secureApiUrl: {
    clientApiUrl: 'https://www.borsonline.hu/secureapi/hu',
    serverApiUrl: 'http://borsfeapi.app.content.private/secureapi/hu',
  },
  personalizedRecommendationApiUrl: {
    clientApiUrl: 'https://terelo.mediaworks.hu/api',
    serverApiUrl: 'https://terelo.app.content.private/api',
  },
  facebookAppId: '287208751435588',
  translation: {
    defaultLocale: 'hu',
    locales: ['hu', 'en'],
  },
  siteUrl: 'https://www.borsonline.hu',
  googleSiteKey: '6LfpEdocAAAAAOqFmT9xMPEVCB2KbCaDuUPgkvWX',
  googleTagManager: 'GTM-TF2DM9H',
  gemiusId: 'nXblnbOxpYU_uhguHmC_VZZW3yiFlPtF0fxOn20TBQT.p7',
  httpReqTimeout: 30, // second
  sentry: {
    dsn: 'https://<EMAIL>/4',
    tracingOrigins: ['https://www.borsonline.hu'],
    sampleRate: 0.1,
    tracesSampleRate: 0.1,
    profilesSampleRate: 0.1,
  },
};
