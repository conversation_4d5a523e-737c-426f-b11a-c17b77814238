import { Environment } from '@trendency/kesma-core';

// Deves környezet
export const environment: Environment = {
  production: false,
  type: 'dev',
  apiUrl: 'https://kozponti-api.dev.trendency.hu/publicapi/hu',
  secureApiUrl: 'https://kozponti-api.dev.trendency.hu/secureapi/hu',
  personalizedRecommendationApiUrl: 'https://terelo.dev.trendency.hu/api',
  facebookAppId: '',
  translation: {
    defaultLocale: 'hu',
    locales: ['hu', 'en'],
  },
  siteUrl: 'http://bors.dev.trendency.hu',
  withHttps: false,
  googleSiteKey: '6LdOdtgaAAAAADOpTzcEuDkf-oSP16hxYrVwhHR1', // use this key on all site in dev mode
  googleTagManager: 'GTM-TF2DM9H',
  gemiusId: 'nXblnbOxpYU_uhguHmC_VZZW3yiFlPtF0fxOn20TBQT.p7',
  httpReqTimeout: 30, // second
  sentry: {
    dsn: 'https://<EMAIL>/4',
    tracingOrigins: ['http://borsv2.dev.trendency.hu'],
    sampleRate: 0.1,
    tracesSampleRate: 0.1,
    profilesSampleRate: 0.1,
  },
};
