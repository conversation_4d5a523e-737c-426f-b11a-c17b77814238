$root: '/assets/fonts/proxima/';


@font-face {
    font-family: ProximaNova;
    src: url($root + 'ProximaNova-Regular.woff2') format('woff2'),
        url($root + 'ProximaNova-Regular.woff') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}


@font-face {
    font-family: ProximaNova;
    src: url($root + 'ProximaNova-Bold.woff2') format('woff2'),
        url($root + 'ProximaNova-Bold.woff') format('woff');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

@font-face {
  font-family: ProximaNova;
  src: url($root + 'ProximaNovaExtra-Bold.woff2') format('woff2'), url($root + 'ProximaNovaExtra-Bold.woff') format('woff');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

/*
@font-face {
    font-family: 'Proxima Nova ScOsf Cn Rg';
    src: url($root + 'ProximaNovaSCond-Bold.woff2') format('woff2'),
        url($root + 'ProximaNovaSCond-Bold.woff') format('woff');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Proxima Nova ScOsf Rg';
    src: url($root + 'ProximaNovaS-Bold.woff2') format('woff2'),
        url($root + 'ProximaNovaS-Bold.woff') format('woff');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Proxima Nova ScOsf ExCn Rg';
    src: url($root + 'ProximaNovaSExCn-Bold.woff2') format('woff2'),
        url($root + 'ProximaNovaSExCn-Bold.woff') format('woff');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Proxima Nova Alt ExCn Rg';
    src: url($root + 'ProximaNovaAExCn-Bold.woff2') format('woff2'),
        url($root + 'ProximaNovaAExCn-Bold.woff') format('woff');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Proxima Nova Cn Rg';
    src: url($root + 'ProximaNovaCond-Bold.woff2') format('woff2'),
        url($root + 'ProximaNovaCond-Bold.woff') format('woff');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Proxima Nova Alt Rg';
    src: url($root + 'ProximaNovaA-Bold.woff2') format('woff2'),
        url($root + 'ProximaNovaA-Bold.woff') format('woff');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Proxima Nova ExCn Rg';
    src: url($root + 'ProximaNovaExCn-Bold.woff2') format('woff2'),
        url($root + 'ProximaNovaExCn-Bold.woff') format('woff');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}


@font-face {
    font-family: 'Proxima Nova Alt Cn Rg';
    src: url($root + 'ProximaNovaACond-Bold.woff2') format('woff2'),
        url($root + 'ProximaNovaACond-Bold.woff') format('woff');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}
*/
