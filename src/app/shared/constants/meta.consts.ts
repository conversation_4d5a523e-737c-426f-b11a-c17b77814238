export const defaultMetaInfo = {
  title: 'BorsOnline - Aktu<PERSON><PERSON> – Celeb – Sport – Test és Lélek',
  robots: 'index, follow, max-image-preview:large',
  description: '<PERSON><PERSON> bulv<PERSON><PERSON> h<PERSON><PERSON> me<PERSON>, n<PERSON><PERSON><PERSON> megtal<PERSON>l mindent ami <PERSON> - https://borsonline.hu',
  ogTitle: 'BorsOnline - Aktuális – Celeb – Sport – Test és Lélek',
  ogImageWidth: '1200',
  ogImageHeight: '600',
  ogLocale: 'hu_HU',
  ogDescription: 'BorsOnline',
  ogSiteName: 'BorsOnline',
  ogType: 'website',
};
