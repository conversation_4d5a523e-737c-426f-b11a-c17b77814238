import { ArticleCard } from '@trendency/kesma-ui';

export const getNewestArticleThumbnail = (articles: ArticleCard[], currentUrl: string): string => {
  const newestArticle = articles?.[0];
  return (
    (newestArticle?.thumbnail
      ? typeof newestArticle?.thumbnail === 'string'
        ? newestArticle?.thumbnail
        : newestArticle?.thumbnail?.url
      : typeof newestArticle?.secondaryThumbnail === 'string'
        ? newestArticle?.secondaryThumbnail
        : newestArticle?.secondaryThumbnail?.url) || `${currentUrl}/assets/images/placeholder.svg`
  );
};
