import {
  BackendUserLoginRequest,
  BackendUserRegisterRequest,
  BackendUserRequestPasswordResetRequest,
  BackendUserResetPasswordRequest,
  LoginFormData,
  PortalUser,
  RegistrationFormData,
} from '../definitions';
import { User } from '@trendency/kesma-ui';

export const loginFormDataToBackendRequest = (formData: LoginFormData, recaptchaToken: string): BackendUserLoginRequest => ({
  emailOrUserName: formData.emailOrUsername,
  password: formData.password,
  rememberMe: formData.rememberMe,
  recaptcha: recaptchaToken,
});

export const registrationFormDataToBackendRequest = (formData: RegistrationFormData, recaptchaToken?: string): BackendUserRegisterRequest => ({
  lastName: formData.lastName,
  firstName: formData.firstName,
  userName: formData.username,
  email: formData.email,
  plainPassword: formData.password,
  newsletter: formData.newsletter,
  acceptTerms: formData.terms,
  marketingLetter: formData.marketing,
  recaptcha: recaptchaToken,
});

export const backendPortalUserDataToUser = (portalUser: PortalUser): User => ({
  uid: portalUser.id,
  email: portalUser.email || '',
  lastName: portalUser.lastName,
  firstName: portalUser.firstName,
  username: portalUser.userName,
  acceptTerms: portalUser.acceptTerms,
  avatar: portalUser.avatar,
  additionalDetails: portalUser.additionalDetails,
  avatarColor: portalUser.avatarColor,
  badges: portalUser.badges,
});

export const requestPasswordResetDataToBackendRequest = (email: string, recaptchaToken: string): BackendUserRequestPasswordResetRequest => ({
  email,
  recaptcha: recaptchaToken,
});

export const resetPasswordDataToBackendRequest = (email: string, password: string, resetPasswordToken: string): BackendUserResetPasswordRequest => ({
  email,
  passwordNew: password,
  passwordNewConfirm: password,
  token: resetPasswordToken,
});
