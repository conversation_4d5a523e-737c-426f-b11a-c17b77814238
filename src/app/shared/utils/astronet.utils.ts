import { AstrologyData, BackendAstrologyResponse } from '../definitions';

export function getAstronomyCardData(astrologyResponse: BackendAstrologyResponse): AstrologyData {
  const { jegy_linkek, jegy_szovegek } = astrologyResponse;

  return {
    list: [
      {
        icon: 'kos',
        name: '<PERSON><PERSON>',
        signRange: 'III. 21. - IV. 20.',
        text: jegy_szovegek?.kos,
        signSlug: jegy_linkek?.kos,
      },
      {
        icon: 'bika',
        name: '<PERSON><PERSON><PERSON>',
        signRange: 'IV. 20. - V. 21.',
        text: jegy_szovegek?.bika,
        signSlug: jegy_linkek?.bika,
      },
      {
        icon: 'ik<PERSON>',
        name: '<PERSON><PERSON><PERSON><PERSON>',
        signRange: 'V. 21. - VI. 21.',
        text: jegy_szovegek?.ikrek,
        signSlug: jegy_linkek?.ikrek,
      },
      {
        icon: 'rak',
        name: '<PERSON><PERSON><PERSON>',
        signRange: 'VI. 21. - VII. 23.',
        text: jegy_szovegek?.rak,
        signSlug: jegy_linkek?.rak,
      },
      {
        icon: 'oros<PERSON><PERSON>',
        name: 'OROS<PERSON><PERSON>Á<PERSON>',
        signRange: 'VII. 23. - VIII. 23.',
        text: jegy_szovegek?.oroszlan,
        signSlug: jegy_linkek?.oroszlan,
      },
      {
        icon: 'szuz',
        name: 'SZŰZ',
        signRange: 'VIII. 23. - IX. 23.',
        text: jegy_szovegek?.szuz,
        signSlug: jegy_linkek?.szuz,
      },
      {
        icon: 'merleg',
        name: 'MÉRLEG',
        signRange: 'IX. 23. - X. 23.',
        text: jegy_szovegek?.merleg,
        signSlug: jegy_linkek?.merleg,
      },
      {
        icon: 'skorpio',
        name: 'SKORPIÓ',
        signRange: 'X. 23. - XI. 22.',
        text: jegy_szovegek?.skorpio,
        signSlug: jegy_linkek?.skorpio,
      },
      {
        icon: 'nyilas',
        name: 'NYILAS',
        signRange: 'XI. 22. - XII. 21.',
        text: jegy_szovegek?.nyilas,
        signSlug: jegy_linkek?.nyilas,
      },
      {
        icon: 'bak',
        name: 'BAK',
        signRange: 'XII. 21. - I. 20.',
        text: jegy_szovegek?.bak,
        signSlug: jegy_linkek?.bak,
      },
      {
        icon: 'vizonto',
        name: 'VÍZÖNTŐ',
        signRange: 'I. 20. - II. 19.',
        text: jegy_szovegek?.vizonto,
        signSlug: jegy_linkek?.vizonto,
      },
      {
        icon: 'halak',
        name: 'HALAK',
        signRange: 'II. 19. - III. 21.',
        text: jegy_szovegek?.halak,
        signSlug: jegy_linkek?.halak,
      },
    ],
  };
}
