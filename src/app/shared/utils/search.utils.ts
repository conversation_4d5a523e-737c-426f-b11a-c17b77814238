import { ArticleCard, ArticleSearchResult, BackendArticleSearchResult, backendDateToDate, toBool } from '@trendency/kesma-ui';

export function backendArticlesSearchResultsToArticleSearchResArticles(article: BackendArticleSearchResult): ArticleSearchResult {
  const [year, month] = article.publishDate.split('-');
  return {
    ...article,
    length: parseInt(article.length, 10),
    year,
    month,
    columnTitleColor: '',
  } as ArticleSearchResult;
}

export const searchResultToArticleCard = ({
  id,
  title,
  slug,
  columnTitle,
  columnSlug,
  publishDate,
  tag,
  lead,
  thumbnail: thumbnailUrl,
  author: authorName,
  authorSlug,
  year: publishYear,
  month: publishMonth,
  contentType,
  preTitle,
  tags,
  regions,
  isAdultsOnly,
  firstTagId,
  publicAuthorM2M,
  hasGallery,
  isVideo,
  minuteToMinute,
  foundationTagSlug,
  foundationTagTitle,
  podcastGuestName,
  podcastGuestNameTitle,
  length,
  columnEmphasizeOnArticleCard,
  isPodcastType,
}: ArticleSearchResult): ArticleCard =>
  ({
    id,
    title,
    preTitle,
    slug,
    category: {
      name: columnTitle,
      slug: columnSlug,
    },
    publishDate: backendDateToDate(publishDate as string),
    publishYear,
    publishMonth,
    lead,
    thumbnail: {
      url: thumbnailUrl,
    },
    author: {
      name: authorName,
      slug: authorSlug,
    },
    tags,
    regions,
    contentType,
    columnSlug,
    columnTitle,
    tag,
    isAdultsOnly,
    firstTagId,
    publicAuthorM2M,
    hasGallery,
    isVideoType: isVideo,
    minuteToMinute,
    foundationTagSlug,
    foundationTagTitle,
    isOpinion: contentType === 'opinion',
    podcastGuestName,
    podcastGuestNameTitle,
    length,
    columnEmphasizeOnArticleCard: toBool(columnEmphasizeOnArticleCard),
    isPodcastType: toBool(isPodcastType) || contentType === 'articlePodcast',
  }) as ArticleCard;

export function calculateFilters(filters: Record<string, string>): Record<string, string | string[]> {
  // if from_date doesn't exist then to_date should be deleted
  if (!filters['from_date'] && filters['to_date']) {
    filters = Object.fromEntries(Object.entries(filters).filter(([key]) => key !== 'to_date'));
  }

  return filters;
}
