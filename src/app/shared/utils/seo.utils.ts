import { defaultMetaInfo } from '../constants';
import { computed, inject, Signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { toSignal } from '@angular/core/rxjs-interop';
import { map } from 'rxjs/operators';

export function createBorsOnlineTitle(title: string): string {
  if (!title) {
    return defaultMetaInfo.title;
  }
  return `${title} | BorsOnline`;
}

export function createTitlePagination(): Signal<string> {
  const route = inject(ActivatedRoute);
  const currentPage = toSignal(route.queryParamMap.pipe(map((params) => parseInt(params.get('page') || '1'))), { initialValue: 1 });
  return computed(() => (currentPage() > 1 ? `${currentPage()}. oldal - ` : ''));
}
