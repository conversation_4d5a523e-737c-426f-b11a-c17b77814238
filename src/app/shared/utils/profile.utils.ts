import { BackendCommentWithArticle } from '@trendency/kesma-ui';
import { BackendProfileEditRequest, ProfileEditFormData } from '../definitions/profil.definitions';

export function profileEditFormDataToBackendRequest(formData: ProfileEditFormData): BackendProfileEditRequest {
  return {
    userName: formData.username,
    passwordNew: formData.newPassword,
    passwordOld: formData.oldPassword,
  };
}

export function profileComments(article: BackendCommentWithArticle): any {
  return {
    ...article,
    article: {
      ...article.article,
      commentCount: article.article.commentsCount,
      thumbnail: {
        url: article.article.thumbnailUrl,
      },
      columnMainColor: article.article.primaryColumn.mainColor,
    },
  };
}
