import isObject from 'lodash-es/isObject';
import {
  Article,
  ArticleBody,
  ArticleBodyType,
  BackendArticle,
  backendDateToDate,
  backendVotingDataToVotingData,
  getPrimaryColumnColorComboByColumnTitleColor,
} from '@trendency/kesma-ui';

export const mapBackendArticleToArticle = (article: BackendArticle): Article => {
  const { lastUpdated, publishDate: pubDate, dossier } = article;
  const last = lastUpdated ? (backendDateToDate(lastUpdated) ?? undefined) : undefined;
  const publishDate = pubDate ? (backendDateToDate(isObject(pubDate) ? pubDate?.date : pubDate) as Date) : undefined;

  const body: ArticleBody[] = article.body.map((element: ArticleBody) =>
    element.type === ArticleBodyType.Voting && element.details[0]
      ? {
          ...element,
          details: [{ ...element.details[0], value: backendVotingDataToVotingData(element.details[0]?.value) }],
        }
      : element
  );

  return {
    ...article,
    publicAuthor: article?.publicAuthor ?? 'BorsOnline',
    dossier: dossier?.[0],
    lastUpdated: last,
    publishDate,
    year: publishDate ? publishDate?.getUTCFullYear() : 0,
    month: publishDate ? publishDate?.getUTCMonth() : 0,
    preTitle: article?.preTitle,
    tag: article?.tags?.[0],
    columnSlug: article.primaryColumn?.slug,
    columnTitle: article?.primaryColumn?.title,
    primaryColumnColorCombo: article?.primaryColumn?.titleColor ? getPrimaryColumnColorComboByColumnTitleColor(article?.primaryColumn?.titleColor) : undefined,
    body,
    isPodcastType: article?.podcastType,
    isVideoType: article?.videoType,
  };
};

export const mapAdvertsToBody = (body: ArticleBody[]): ArticleBody[] => {
  let advertIndex = 0;
  return body.map((bodyPart) => {
    if (bodyPart.type === ArticleBodyType.Advert) {
      return {
        ...bodyPart,
        adverts: {
          mobile: `mobilinterrupter_${++advertIndex}`,
          desktop: `desktopinterrupter_${advertIndex}`,
        },
      };
    }
    return bodyPart;
  });
};
