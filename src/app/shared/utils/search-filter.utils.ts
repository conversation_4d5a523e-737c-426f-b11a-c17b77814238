import { format, subDays, subMonths, subWeeks, subYears } from 'date-fns';
import { LabelValue } from '../definitions';

export const SEARCH_FILTER_DATE_FORMAT = 'yyyy-MM-dd';

export const publishDateFilters: LabelValue<string>[] = [
  {
    label: 'Dátum tartomány',
    value: '',
  },
  {
    label: 'Az utóbbi 24 órában',
    value: format(subDays(new Date(), 1), SEARCH_FILTER_DATE_FORMAT),
  },
  {
    label: 'Az utóbbi egy héten',
    value: format(subWeeks(new Date(), 1), SEARCH_FILTER_DATE_FORMAT),
  },
  {
    label: 'Az utóbbi egy hónapban',
    value: format(subMonths(new Date(), 1), SEARCH_FILTER_DATE_FORMAT),
  },
  {
    label: 'Az utóbbi egy évben',
    value: format(subYears(new Date(), 1), SEARCH_FILTER_DATE_FORMAT),
  },
];

export const contentTypeFilters: LabelValue<string>[] = [
  {
    label: 'Cikk típusa',
    value: '',
  },
  {
    label: 'Vélemény',
    value: 'opinion',
  },
  {
    label: 'Videós tartalom',
    value: 'articleVideo',
  },
  {
    label: 'Rövid hír',
    value: 'shortNews',
  },
  {
    label: 'Podcast tartalom',
    value: 'articlePodcast',
  },
];

export const columnFilters = [
  {
    title: 'Rovatok',
    slug: '',
  },
];

export const authorFilters = [
  {
    public_author_name: 'Szerző szerint',
    slug: '',
  },
];
