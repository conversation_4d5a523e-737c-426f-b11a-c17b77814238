/**
 * Creates string form a given query params object. This is used to create a query string that already contains
 * the params in the url. This is need for the requestService, as it makes a key only from the provided url, ignoring
 * the other params.
 * @param params
 * @private
 */
export const paramsToString = (params: Record<string, string | string[] | undefined | null>): string => {
  const paramsPrepared: Record<string, string>[] = [];
  if (params) {
    Object.keys(params).map((key) => {
      const value = params[key];
      if (value === undefined || value === null) {
        return;
      }
      if (Array.isArray(value)) {
        value.map((item) => {
          // Add [] to end of array params, but only if it is not already there.
          const itemKey = key.endsWith(']') ? key : `${key}[]`;
          paramsPrepared.push({ [itemKey]: encodeURIComponent(item) });
        });
        return;
      }
      paramsPrepared.push({ [key]: encodeURIComponent(value) });
    });
  }
  const paramsString = paramsPrepared
    .map((item) =>
      Object.keys(item)
        .map((key) => `${key}=${item[key]}`)
        .join('&')
    )
    .join('&');
  return paramsString;
};
