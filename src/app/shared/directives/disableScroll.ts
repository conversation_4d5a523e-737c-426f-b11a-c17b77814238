import { Directive, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit, Renderer2 } from '@angular/core';
import { UtilService } from '@trendency/kesma-core';

@Directive({
  selector: '[appDisableScroll]',
})
export class DisableScrollDirective implements On<PERSON>nit, OnD<PERSON>roy {
  private readonly renderer = inject(Renderer2);
  private readonly utilService = inject(UtilService);

  ngOnInit(): void {
    if (this.utilService.isBrowser()) {
      this.renderer.addClass(document.body, 'modal-open');
    }
  }

  ngOnDestroy(): void {
    if (this.utilService.isBrowser()) {
      this.renderer.removeClass(document.body, 'modal-open');
    }
  }
}
