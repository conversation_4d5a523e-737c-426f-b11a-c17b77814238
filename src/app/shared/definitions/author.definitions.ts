import { LimitableMeta, ArticleCard } from '@trendency/kesma-ui';

export type AuthorData = Readonly<{
  readonly facebook: string;
  readonly instagram: string;
  readonly publicAuthorDescription: string;
  readonly publicAuthorName: string;
  readonly tiktok: string;
  readonly avatar: AuthorDataAvatar;
  readonly title?: string;
  readonly rank?: string;
  readonly slug?: string;
  readonly name?: string;
  readonly id: string;
  readonly public_author_name?: string;
  readonly public_author_description?: string;
}>;

export type AuthorDataAvatar = Readonly<{
  readonly fullSizeUrl: string;
  readonly thumbnailUrl: string;
  readonly bigUrl?: string;
  readonly variantId: number;
  readonly altText?: string;
}>;

export interface AuthorPageData {
  readonly author: AuthorData;
  readonly articles: ArticleCard[];
  readonly limitable: LimitableMeta;
}
