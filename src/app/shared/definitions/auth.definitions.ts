import { UserAdditionalDetails, UserGender, UserProfileAvatarColors, UserProfileBadge } from '@trendency/kesma-ui';

export type RegistrationFormData = Readonly<{
  lastName: string;
  firstName: string;
  username: string;
  email?: string;
  password?: string;
  newsletter: boolean;
  terms: boolean;
  marketing: boolean;
}>;

export type BackendUserRegisterRequest = Readonly<{
  lastName: string;
  firstName: string;
  userName: string;
  email?: string;
  plainPassword?: string;
  acceptTerms: boolean;
  newsletter: boolean;
  marketingLetter: boolean;
  recaptcha?: string;
}>;

export type BackendUserLoginResponse = Readonly<{
  token: string;
}>;

export type LoginFormData = Readonly<{
  emailOrUsername: string;
  password: string;
  rememberMe: boolean;
}>;

export type BackendUserLoginRequest = Readonly<{
  emailOrUserName: string;
  password: string;
  rememberMe: boolean;
  recaptcha: string;
}>;

export type BackendAllowedLoginMethodsResponse = Readonly<{
  email: boolean;
  [SocialProvider.FACEBOOK]: boolean;
  [SocialProvider.GOOGLE]: boolean;
  [SocialProvider.APPLE]: boolean;
}>;

export enum SocialProvider {
  FACEBOOK = 'facebook',
  GOOGLE = 'google',
  APPLE = 'apple',
}

export type UserProfileDetails = Readonly<{
  userName?: string;
  selectedImage?: string;
  uploadedRecipeCount?: number;
  additionalDetails?: BackendAdditionalDetails;
  membership?: string;
  follower?: number;
  followed?: number;
}>;

export type BackendAdditionalDetails = Readonly<{
  dateOfBirth?: string;
  gender?: UserGender;
  introduction?: string;
  website?: string;
  instagram?: string;
  facebook?: string;
}>;

export type PortalUser = Readonly<{
  id: string;
  email?: string;
  userName?: string;
  firstName?: string;
  lastName?: string;
  acceptTerms?: Date | null;
  avatar?: string;
  avatarColor?: UserProfileAvatarColors;
  additionalDetails?: UserAdditionalDetails;
  badges?: UserProfileBadge[];
}>;

export type BackendUserResetPasswordRequest = Readonly<{
  email: string;
  passwordNew: string;
  passwordNewConfirm: string;
  token: string;
}>;

export type BackendUserRequestPasswordResetRequest = Readonly<{
  email: string;
  recaptcha: string;
}>;
