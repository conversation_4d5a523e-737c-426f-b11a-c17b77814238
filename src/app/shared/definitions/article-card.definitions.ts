export enum ArticleCardType {
  MainArticle = 'MainArticle',
  TopSlantImgTagTitle = 'TopSlantImgTagTitle',
  TopSlantImgTagTitlePadding = 'TopSlantImgTagTitlePadding',
  TopImgTagTitle = 'TopImgTagTitle',
  NoImgTagTitle = 'NoImgTagTitle',
  SideImgDateTitleLead = 'SideImgDateTitleLead',
  HighlightedSideImgDateTitleLead = 'HighlightedSideImgDateTitleLead',
  MostReadNews = 'MostReadNews',
  MostRecentOrMostRead = 'MostRecentOrMostRead',
  MinuteToMinute = 'MinuteToMinute',
  ExternalRecommendation = 'ExternalRecommendation',
  ArticleRecommendationTopImg = 'ArticleRecommendationTopImg',
  ArticleRecommendationSideImg = 'ArticleRecommendationSideImg',
  ArticleRecommendationNoImg = 'ArticleRecommendationNoImg',
  Weather = 'Weather',
}
