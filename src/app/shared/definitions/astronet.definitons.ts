export type BackendAstrologyInfo = Readonly<{
  bak: string;
  bika: string;
  halak: string;
  ikrek: string;
  kos: string;
  merleg: string;
  nyilas: string;
  oroszlan: string;
  rak: string;
  skorpio: string;
  szuz: string;
  vizonto: string;
}>;

export type BackendAstrologyResponse = Readonly<{
  jegy_linkek: BackendAstrologyInfo;
  jegy_szovegek: BackendAstrologyInfo;
}>;

export type AstrologyBlock = Readonly<{
  name: string;
  icon: string;
  signRange: string;
  text: string;
  signSlug: string;
}>;

export type AstrologyData = Readonly<{
  list: readonly AstrologyBlock[];
}>;
