import { Pipe, PipeTransform } from '@angular/core';
import { format, isToday, isYesterday } from 'date-fns';
import { hu } from 'date-fns/locale';

@Pipe({
  name: 'archiveArticlePublishDatePipe',
})
export class FormatArticlePublishDatePipe implements PipeTransform {
  transform(value: string | Date | undefined): string {
    if (!value) {
      return '';
    }

    value = new Date(value);
    if (isToday(value)) {
      return `ma, ${format(value, 'HH:mm', { locale: hu })}`;
    } else if (isYesterday(value)) {
      return `tegnap, ${(format(value, 'HH:mm'), { locale: hu })}`;
    } else {
      return format(value, 'eeee, HH:mm', { locale: hu });
    }
  }
}
