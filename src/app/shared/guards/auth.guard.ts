import { inject, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { map, Observable, of } from 'rxjs';
import { AuthService } from '../services';
import { UtilService } from '@trendency/kesma-core';

@Injectable({ providedIn: 'root' })
export class AuthGuard {
  private readonly router = inject(Router);
  private readonly authService = inject(AuthService);
  private readonly utilService = inject(UtilService);

  canActivate(): Observable<boolean> {
    if (!this.utilService.isBrowser()) {
      return of(false);
    }

    return this.authService.isAuthenticated().pipe(
      map((response: boolean) => {
        if (!response) {
          this.router.navigate([`/bejelentkezes`]);
        }
        return response;
      })
    );
  }
}
