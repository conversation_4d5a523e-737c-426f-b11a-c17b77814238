import { CanActivateFn } from '@angular/router';
import { RedirectService } from '@trendency/kesma-ui';
import { inject } from '@angular/core';
import { ReqService, SeoService } from '@trendency/kesma-core';
import { catchError, map } from 'rxjs/operators';
import { of } from 'rxjs';

export const CheckRedirectBefore404: CanActivateFn = () => {
  const redirectService = inject(RedirectService);
  const reqService = inject(ReqService);
  const seoService = inject(SeoService);
  return reqService.get<{ url: string }>(`/portal/redirection?url=${seoService.currentUrl}`).pipe(
    map(({ url }) => {
      if (url) {
        redirectService.redirectOldUrl(url, true);
        return false;
      }
      return true;
    }),
    catchError(() => {
      return of(true);
    })
  );
};
