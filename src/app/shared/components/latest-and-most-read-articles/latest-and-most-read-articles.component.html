<div class="tab-list">
  @for (tab of tabs; track tab) {
    <div class="tab" [class.active]="selectedTab() === tab" (click)="selectedTab.set(tab)">{{ tab }}</div>
  }
</div>
@if (data?.length) {
  <div class="articles">
    @for (article of articles(); track $index) {
      <app-article-card [data]="article" [styleId]="ArticleCardType.MostRecentOrMostRead">
        <div class="article-date" [class.count]="!isLatestNews">
          @if (isLatestNews) {
            {{ article.publishDate | dfnsFormat: 'H:mm' }}
          } @else {
            {{ $index + 1 }}
          }
        </div>
      </app-article-card>
      <div class="divider"></div>
    }
  </div>
}
<div class="bottom">
  @if (isLatestNews) {
    <app-simple-button class="link" routerLink="/friss-hirek">Összes legfrissebb hír</app-simple-button>
  } @else {
    <app-simple-button class="link" routerLink="/legolvasottabb-hirek">Összes legolvasottabb hír</app-simple-button>
  }
</div>
