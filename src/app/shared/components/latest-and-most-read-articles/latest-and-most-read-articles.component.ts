import { ChangeDetectionStrategy, Component, computed, inject, signal } from '@angular/core';
import { Api<PERSON><PERSON>ult, ArticleCard, ArticleSearchResult, backendDateToDate, BaseComponent, searchResultToArticleCard } from '@trendency/kesma-ui';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { ArticleCardType } from '../../definitions';
import { toSignal } from '@angular/core/rxjs-interop';
import { map } from 'rxjs/operators';
import { ReqService } from '@trendency/kesma-core';
import { BorsSimpleButtonComponent } from '../simple-button/simple-button.component';
import { RouterLink } from '@angular/router';
import { publishDateFilters } from '../../utils';
import { FormatPipeModule } from 'ngx-date-fns';

@Component({
  selector: 'app-latest-and-most-read-articles',
  templateUrl: './latest-and-most-read-articles.component.html',
  styleUrl: './latest-and-most-read-articles.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ArticleCardComponent, BorsSimpleButtonComponent, RouterLink, FormatPipeModule],
})
export class LatestAndMostReadArticlesComponent extends BaseComponent<ArticleCard[]> {
  private readonly reqService = inject(ReqService);

  readonly ArticleCardType = ArticleCardType;
  readonly tabs: string[] = ['Legfrissebb', 'Legolvasottabb'];
  readonly dateFilter = publishDateFilters[1].value;

  readonly selectedTab = signal<string>('Legfrissebb');

  readonly latestNews = toSignal(
    this.reqService.get<ApiResult<(ArticleSearchResult & { recommendedTitle?: string })[]>>('/content-page/search', { params: { rowCount_limit: '5' } }).pipe(
      map(({ data }) =>
        data.map((article) => ({
          ...searchResultToArticleCard(article),
          title: article?.recommendedTitle || article?.title,
          publishDate: article.publishDate instanceof Date ? article.publishDate : backendDateToDate(article.publishDate as string),
        }))
      )
    )
  );

  readonly articles = computed(() => (this.selectedTab() === 'Legfrissebb' ? this.latestNews() : this.data));

  get isLatestNews(): boolean {
    return this.selectedTab() === 'Legfrissebb';
  }
}
