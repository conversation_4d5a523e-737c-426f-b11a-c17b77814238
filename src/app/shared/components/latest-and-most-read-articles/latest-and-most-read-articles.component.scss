@use 'shared' as *;

:host {
  display: block;
  color: var(--kui-black-950);
  container-type: inline-size;
  .tab {
    font-size: 24px;
    font-weight: 800;
    line-height: 20px;
    cursor: pointer;
    background-color: var(--kui-gray-200);
    clip-path: polygon(0 10px, 101% 0, 100% 100%, 0 100%);
    padding: 30px 32px 20px 32px;
    flex: 1;
    @include container-breakpoint-down(sm) {
      font-size: 18px;
      line-height: 20px;
    }
    @container (max-width: 375px) {
      padding-inline: 16px;
    }
    &-list {
      display: flex;
      // 1/4 column
      @container (max-width: 300px) {
        flex-wrap: wrap;
        .tab:last-of-type {
          margin-top: -10px;
        }
      }
    }
  }
  .active {
    font-size: 32px;
    color: var(--kui-red-500);
    background-color: var(--kui-gray-250);
    @include container-breakpoint-down(sm) {
      font-size: 20px;
    }
  }
  .articles {
    padding: 32px 32px 0;
    background-color: var(--kui-gray-250);
    max-height: 645px;
    overflow-y: auto;
    @container (max-width: 375px) {
      padding: 16px 16px 0;
    }
  }
  .divider {
    height: 1px;
    background-color: var(--kui-gray-200);
    margin-block: 8px;
    width: 100%;
  }
  .bottom {
    background-color: var(--kui-gray-250);
    padding: 16px 32px 32px;
    display: flex;
    justify-content: flex-end;
    @container (max-width: 375px) {
      padding: 16px;
    }
  }
  .link {
    margin: 0;
    min-width: 192px;
    @include container-breakpoint-down(xs) {
      width: 100%;
    }
  }
  .article-date {
    background-color: var(--kui-red-500);
    font-size: 14px;
    color: var(--kui-white);
    flex-shrink: 0;
    font-weight: 600;
    line-height: 20px;
    width: 48px;
    text-align: center;
    padding-block: 2px;
    &.count {
      width: 32px;
      height: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 24px;
      font-weight: 700;
      line-height: 36px;
      background-color: var(--kui-gray-250);
      color: var(--kui-red-400);
      border: 2px solid var(--kui-red-400);
    }
  }
}
