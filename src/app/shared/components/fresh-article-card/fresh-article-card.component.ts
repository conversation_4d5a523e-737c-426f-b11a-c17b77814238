import { ChangeDetectionStrategy, Component } from '@angular/core';
import { BaseComponent, buildArticleUrl, FreshBlockCard } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-fresh-article-card',
  templateUrl: './fresh-article-card.component.html',
  styleUrl: './fresh-article-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink],
})
export class FreshArticleCardComponent extends BaseComponent<FreshBlockCard> {
  articleLink?: string[];

  protected override setProperties(): void {
    this.articleLink = this.data && buildArticleUrl(this.data);
  }
}
