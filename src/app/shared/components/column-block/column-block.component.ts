import { ChangeDetectionStrategy, Component, HostBinding, input, OnChanges } from '@angular/core';
import { ArticleCard, BaseComponent, IconComponent } from '@trendency/kesma-ui';
import { ArticleCardType } from '../../definitions';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { getContrastRatio } from '../../utils/color';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-column-block',
  templateUrl: './column-block.component.html',
  styleUrls: ['./column-block.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ArticleCardComponent, IconComponent, RouterLink],
})
export class ColumnBlockComponent extends BaseComponent<ArticleCard[]> implements OnChanges {
  readonly columnTitle = input();
  @HostBinding('style.background-color') get backgroundColor(): string {
    return this.data?.[0]?.columnMainColor || '#000';
  }
  @HostBinding('class.big') get isBig(): boolean {
    return this.desktopWidth() === 12;
  }
  @HostBinding('class.white-text') get isWhiteText(): boolean {
    const whiteContrast = getContrastRatio(this.backgroundColor, '#fff');
    const blackContrast = getContrastRatio(this.backgroundColor, '#000');
    return whiteContrast > blackContrast;
  }
  readonly desktopWidth = input<number>(12);
  protected readonly ArticleCardType = ArticleCardType;
  mappedArticles: ArticleCard[] = [];
  mapAritcles(): void {
    if (!this.data) {
      return;
    }
    this.mappedArticles = this.data?.map((article) => ({
      ...article,
      ...(this.isWhiteText ? { foregroundColor: 'var(--kui-white)' } : {}),
    }));
  }
  ngOnChanges(): void {
    this.mapAritcles();
  }
}
