@use 'shared' as *;
:host {
  display: block;
  container-type: inline-size;
  width: 100%;
  @include media-breakpoint-down(md) {
    margin-inline: -15px;
    width: calc(100% + 30px);
  }
  .wrapper {
    padding: 32px;
    @include container-breakpoint-down(xs) {
      padding: 16px;
    }
  }
  &.big {
    app-article-card {
      width: calc(1 / 3 * 100% - 22px);
    }
    @include media-breakpoint-down(sm) {
      app-article-card {
        width: 100%;
      }
    }
  }
  &:not(.big) {
    padding-top: 20px;
    clip-path: polygon(0 20px, 101% 0, 100% 100%, 0 100%);
    app-article-card {
      width: calc(1 / 2 * 100% - 16px);
      @include container-breakpoint-down(xs) {
        width: calc(1 / 2 * 100% - 8px);
      }
      @container (max-width: 320px) {
        width: 100%;
      }
    }
    @include container-breakpoint-down(xs) {
      clip-path: polygon(0 10px, 101% 0, 100% 100%, 0 100%);
    }
  }
  &.white-text {
    color: var(--kui-white);
    .meta {
      .title,
      .more-button {
        color: var(--kui-white);
        fill: var(--kui-white);
      }
    }
  }
}
.meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  flex-wrap: wrap;
  gap: 16px;
  .title {
    font-size: 48px;
    font-weight: 800;
    line-height: 42px; /* 87.5% */
    text-transform: uppercase;
    overflow-wrap: anywhere;
    color: var(--kui-black);

    @include media-breakpoint-down(sm) {
      font-size: 28px;
      line-height: 28px;
    }
  }
  .more-button {
    display: flex;
    color: var(--kui-black);
    font-size: 16px;
    font-weight: 700;
    line-height: 20px; /* 125% */
    gap: 10px;
  }
}
.articles {
  display: flex;
  flex-wrap: wrap;
  column-gap: 32px;
  row-gap: 16px;
  @include container-breakpoint-down(xs) {
    column-gap: 16px;
  }
  app-article-card {
    margin-bottom: 0;
  }
}
