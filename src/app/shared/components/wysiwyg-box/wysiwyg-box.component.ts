import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ArticleFileLinkDirective, ImageLightboxComponent, WysiwygBoxComponent as KesmaWysiwygBoxComponent } from '@trendency/kesma-ui';
import { BypassPipe, RunScriptsDirective } from '@trendency/kesma-core';
import { NgFor, NgIf } from '@angular/common';

@Component({
  selector: 'app-wysiwyg-box',
  templateUrl: '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/wysiwyg-box/wysiwyg-box.component.html',
  styleUrls: ['../../../../../node_modules/@trendency/kesma-ui/src/lib/components/wysiwyg-box/wysiwyg-box.component.scss', './wysiwyg-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgI<PERSON>, NgFor, ArticleFileLinkDirective, RunScriptsDirective, ImageLightboxComponent, BypassPipe],
})
export class WysiwygBoxComponent extends KesmaWysiwygBoxComponent {}
