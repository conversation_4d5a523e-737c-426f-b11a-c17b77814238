@use 'shared' as *;

.articles-container {
  position: relative;

  .navigation {
    height: 100%;
    position: absolute;
    z-index: 11;
    width: 32px;

    &.prev {
      top: 0;
      left: 0;
      background: linear-gradient(90deg, #fff 0%, rgba(255, 255, 255, 0) 77.5%);
    }

    &.next {
      top: 0;
      right: 0;
      background: linear-gradient(270deg, #fff 0%, rgba(255, 255, 255, 0) 77.5%);
    }

    button {
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 32px;
      width: 32px;
      background-color: var(--kui-red-500);
      color: var(--kui-white);
      z-index: 12;

      &.nav-prev {
        top: 50%;
        left: 0;
        rotate: 180deg;
      }

      &.nav-next {
        top: 50%;
        right: 0;
      }
    }
  }
}

.articles-with-image {
  margin: 32px 0;
}
