import { AfterViewInit, ChangeDetectionStrategy, Component, input, viewChild } from '@angular/core';
import { ArticleCard, IconComponent, KesmaSwipeComponent, SwipeBreakpoints } from '@trendency/kesma-ui';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { ArticleCardType } from '../../definitions';

@Component({
  selector: 'app-ten-article-recommender',
  imports: [ArticleCardComponent, KesmaSwipeComponent, IconComponent],
  templateUrl: './ten-article-recommender.component.html',
  styleUrl: './ten-article-recommender.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TenArticleRecommenderComponent implements AfterViewInit {
  swipeComponent = viewChild(KesmaSwipeComponent);
  articles = input<ArticleCard[]>([]);
  withImage = input<boolean>(false);
  startIndex = 0;

  protected readonly ArticleCardType = ArticleCardType;

  readonly breakpoints: SwipeBreakpoints = {
    default: {
      itemCount: 1.7,
      gap: '16px',
    },
    768: {
      itemCount: 4,
      gap: '32px',
    },
  };

  ngAfterViewInit(): void {
    if (this.swipeComponent()?.currentIndex() !== this.startIndex) {
      this.swipeComponent()?.swipeTo(this.startIndex);
    }
  }

  slideNext(): void {
    this.swipeComponent()?.swipePageForward();
  }

  slidePrev(): void {
    this.swipeComponent()?.swipePageBack();
  }

  get canSlidePrev(): boolean {
    return !(this.swipeComponent()?.isAtStart() ?? true);
  }

  get canSlideNext(): boolean {
    return !(this.swipeComponent()?.isAtEnd() ?? true);
  }
}
