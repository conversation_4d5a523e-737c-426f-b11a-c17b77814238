<div class="mobile-menu-list">
  @for (menuItem of menuItems(); track menuItem.id) {
    <div class="menu-item">
      <div class="menu-item-title">
        @if (menuItem.isCustomUrl) {
          <a (click)="onItemClicked()" [href]="menuItem.link" class="menu-item-link">
            {{ menuItem.title }}
            @if (menuItem.relatedType === RelatedType.COLUMN && menuItem?.related?.sponsorship; as sponsorship) {
              <img [src]="sponsorship?.logo" alt="Szponzor logo" loading="lazy" />
            }
          </a>
          @if (menuItem?.hasSubItems) {
            <kesma-icon (click)="activateItem(menuItem)" [name]="'chevron'" [ngClass]="{ active: activeItem?.id === menuItem.id }"></kesma-icon>
          }
        } @else {
          <a (click)="onItemClicked()" [routerLink]="menuItem.link" class="menu-item-link">
            {{ menuItem.title }}
            @if (menuItem.relatedType === RelatedType.COLUMN && menuItem?.related?.sponsorship; as sponsorship) {
              <img [src]="sponsorship?.logo" alt="Szponzor logo" loading="lazy" />
            }
          </a>
          @if (menuItem?.hasSubItems) {
            <kesma-icon (click)="activateItem(menuItem)" [name]="'chevron'" [ngClass]="{ active: activeItem?.id === menuItem.id }"></kesma-icon>
          }
        }
      </div>
      @if (menuItem?.hasSubItems) {
        @if (activeItem?.id === menuItem.id) {
          <div class="child-list">
            @for (childItem of menuItem.children; track childItem.id) {
              @if (childItem.isCustomUrl) {
                <a (click)="onItemClicked()" [href]="childItem.link" class="child-list-item">
                  {{ childItem.title }}
                  @if (childItem.relatedType === RelatedType.COLUMN && childItem?.related?.sponsorship; as sponsorship) {
                    <img [src]="sponsorship?.logo" alt="Szponzor logo" loading="lazy" />
                  }
                </a>
              } @else {
                <a (click)="onItemClicked()" [routerLink]="childItem.link" class="child-list-item">
                  {{ childItem.title }}
                  @if (childItem.relatedType === RelatedType.COLUMN && childItem?.related?.sponsorship; as sponsorship) {
                    <img [src]="sponsorship?.logo" alt="Szponzor logo" loading="lazy" />
                  }
                </a>
              }
            }
          </div>
        }
      }
    </div>
  }
</div>
