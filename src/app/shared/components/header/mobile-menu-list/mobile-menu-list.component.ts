import { ChangeDetectionStrategy, Component, EventEmitter, input, Output } from '@angular/core';
import { IconComponent, RelatedType, SimplifiedMenuItem } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { NgClass } from '@angular/common';

@Component({
  selector: 'app-mobile-menu-list',
  imports: [RouterLink, IconComponent, NgClass],
  templateUrl: './mobile-menu-list.component.html',
  styleUrl: './mobile-menu-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MobileMenuListComponent {
  @Output() itemClicked = new EventEmitter<void>();
  readonly RelatedType = RelatedType;
  menuItems = input<SimplifiedMenuItem[]>([]);
  activeItem: SimplifiedMenuItem | undefined = undefined;

  onItemClicked(): void {
    this.itemClicked.emit();
  }

  activateItem(item: SimplifiedMenuItem | undefined): void {
    if (this.activeItem === item) {
      this.activeItem = undefined;
      return;
    }
    this.activeItem = item;
  }
}
