@use 'shared' as *;

.mobile-menu-list {
  display: flex;
  flex-direction: column;
  margin-bottom: 32px;

  .menu-item {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;

    &-title {
      width: 100%;
      padding: 16px 0;
      border-bottom: 1px solid var(--kui-white);
      display: flex;
      justify-content: space-between;
    }

    &-link {
      font-size: 16px;
      font-weight: 700;
      letter-spacing: 1px;
      text-transform: uppercase;
      color: var(--kui-white);
      display: flex;
      align-items: center;
      gap: 5px;

      img {
        width: 24px;
      }
    }

    kesma-icon {
      display: inline-block;
      width: 12px;
      transform: rotate(180deg);

      &.active {
        transform: rotate(0deg);
      }
    }
  }

  .child-list {
    display: flex;
    flex-direction: column;
    flex-basis: 100%;
    padding: 16px 0 4px 0;

    &-item {
      padding: 10px 30px 10px 0;
      font-size: 16px;
      font-weight: 500;
      color: var(--kui-white);
      text-transform: none;
      display: flex;
      align-items: center;
      gap: 5px;

      img {
        width: 24px;
      }
    }
  }
}
