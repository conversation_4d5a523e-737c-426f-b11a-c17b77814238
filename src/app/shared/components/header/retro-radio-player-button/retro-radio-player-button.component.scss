@use 'shared' as *;

:host {
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  justify-content: center;
  background: #d2122d;
  color: #f7eedb;
  font-weight: 700;
  height: 40px;
  font-size: 14px;
  border-radius: 6px;
  padding: 8px 16px;
  cursor: pointer;
  transition: 0.2s all;

  &:hover {
    background: #e1374e;

    .icon {
      .retro-logo,
      .bars {
        opacity: 0;
      }

      .play-btn,
      .pause-btn {
        opacity: 1;
      }
    }
  }
}

.icon {
  position: relative;
  margin-right: 5px;

  .retro-logo {
    width: 42px;
    height: 35px;
  }

  .play-btn,
  .pause-btn {
    position: absolute;
    left: 5px;
    top: 2px;
    opacity: 0;

    svg {
      fill: #f7eedb;
    }
  }

  .retro-logo,
  .play-btn,
  .pause-btn {
    transition: 0.2s all;
  }
}
