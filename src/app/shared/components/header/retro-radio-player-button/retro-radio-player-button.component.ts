import { ChangeDetectionStrategy, Component, HostListener, inject, Input, OnInit } from '@angular/core';
import { RadioStoreService } from '@trendency/kesma-ui';
import { toSignal } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-retro-radio-player-button',
  templateUrl: './retro-radio-player-button.component.html',
  styleUrls: ['./retro-radio-player-button.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [],
})
export class RetroRadioPlayerButtonComponent implements OnInit {
  @Input() streamUrl = 'https://icast.connectmedia.hu/5001/live.mp3';
  private readonly radioStore = inject(RadioStoreService);

  isPlaying = toSignal(this.radioStore.createPlayer('retro'), { initialValue: false });

  @Input() set playerVolume(volume: number) {
    if (volume >= 0 && volume <= 1) {
      this.radioStore.setPlayerVolume('retro', volume);
    }
  }

  @HostListener('click') elementClicked(): void {
    if (this.isPlaying()) {
      this.pause();
    } else {
      this.play();
    }
  }

  ngOnInit(): void {
    this.playerVolume = 0.6; //Lower the volume as it's quite loud by default
  }

  play(): void {
    this.radioStore.playPlayer('retro', this.streamUrl); //Pass the stream url to start the playing.
  }

  pause(): void {
    this.radioStore.pausePlayer('retro');
  }
}
