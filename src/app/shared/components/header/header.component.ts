import { ChangeDetectionStrategy, Component, computed, HostListener, inject, input, signal } from '@angular/core';
import { ClickOutsideDirective, IconComponent, RelatedType, SimplifiedMenuItem } from '@trendency/kesma-ui';
import { HeaderService, NamedayService, WeatherService } from '../../services';
import { toSignal } from '@angular/core/rxjs-interop';
import { Router, RouterLink } from '@angular/router';
import { NgClass } from '@angular/common';
import { map } from 'rxjs/operators';
import { RetroRadioPlayerButtonComponent } from './retro-radio-player-button/retro-radio-player-button.component';
import { MobileMenuListComponent } from './mobile-menu-list/mobile-menu-list.component';
import { PopupComponent } from '../popup/popup.component';
import { FormsModule } from '@angular/forms';

declare let __tcfapi: (command: string, version?: number, callback?: (response: unknown, success: boolean) => void, param?: unknown) => void;

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [IconComponent, RouterLink, NgClass, RetroRadioPlayerButtonComponent, ClickOutsideDirective, MobileMenuListComponent, PopupComponent, FormsModule],
  templateUrl: './header.component.html',
  styleUrl: './header.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HeaderComponent {
  @HostListener('document:keydown', ['$event']) handleKeydown(event: KeyboardEvent): void {
    if (event.key === 'Escape') {
      this.isSearchOpen.set(false);
    }
    if (event.key === 'Enter' && this.isSearchOpen()) {
      this.onSearch();
    }
  }

  private readonly namedayService = inject(NamedayService);
  private readonly weatherService = inject(WeatherService);
  private readonly headerService = inject(HeaderService);
  private readonly router = inject(Router);
  readonly RelatedType = RelatedType;

  readonly topMenu = input<SimplifiedMenuItem[]>([]);
  readonly mainMenu = input<SimplifiedMenuItem[]>([]);
  readonly namedays = toSignal(
    this.namedayService.getTodayNamedays().pipe(
      map((namedays) =>
        namedays
          .filter((nameday) => nameday.isMain)
          .map((nameday) => nameday.name)
          .join(', ')
      )
    )
  );
  readonly weather = toSignal(this.weatherService.getWeatherByCity());
  readonly isHomePage = computed(() => this.headerService.isHomePage());
  readonly backgroundColor = computed(() => this.headerService.backgroundColor());

  isHamburgerOpen = signal(false);
  isSearchOpen = signal(false);
  hasSearchError = signal(false);
  searchInput = '';
  parentMenuItems = computed(() => this.mainMenu().filter((item) => item.hasSubItems));
  standaloneMenuItems = computed(() => this.mainMenu().filter((item) => !item.hasSubItems));

  readonly hoveredParentMenuItemIndex = signal<number | null>(null);
  readonly hoveredParentMenuItemSubItems = computed(() => {
    const itemIndex = this.hoveredParentMenuItemIndex() as number;
    return isNaN(itemIndex) ? [] : this.mainMenu()[itemIndex]?.children || [];
  });

  readonly topMenuFixItems: SimplifiedMenuItem[] = [
    {
      id: '1',
      title: 'Magazinok',
      link: '/bors-napilap-elofizetes',
      target: '_self',
    },
    {
      id: '2',
      title: 'Játék',
      link: '/jatekok',
      target: '_self',
    },
  ];

  toggleHamburgerMenu(): void {
    this.isHamburgerOpen.set(!this.isHamburgerOpen());
  }

  openCookieSettings(): void {
    this.closeHamburger();
    __tcfapi('displayConsentUi', 2, () => {}, true);
  }

  closeHamburger(): void {
    this.isHamburgerOpen.set(false);
    this.hoveredParentMenuItemIndex.set(null);
  }

  toggleSearch(): void {
    this.closeHamburger();
    this.isSearchOpen.set(true);
  }

  onSearch(): void {
    if (this.searchInput.length < 3) {
      this.hasSearchError.set(true);
      return;
    }
    this.hasSearchError.set(false);
    this.isSearchOpen.set(false);
    this.router.navigate(['/kereses'], { queryParams: { global_filter: this.searchInput } }).then(() => (this.searchInput = ''));
  }
}
