import { ChangeDetectionStrategy, Component, computed, DestroyRef, effect, inject, input, signal } from '@angular/core';
import { BrandingBoxService } from '../../services';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { IconComponent, PersonalizedRecommendationArticle } from '@trendency/kesma-ui';
import { TitleCasePipe } from '@angular/common';

const ARTICLE_LIMIT = 4;

@Component({
  selector: 'app-branding-box-ex',
  templateUrl: './branding-box-ex.component.html',
  styleUrl: './branding-box-ex.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: { '[class]': 'brand()' },
  imports: [TitleCasePipe, IconComponent],
})
export class BrandingBoxExComponent {
  private readonly brandingBoxService = inject(BrandingBoxService);
  private readonly destroyRef = inject(DestroyRef);

  readonly brand = input.required<string>();

  readonly articles = signal<PersonalizedRecommendationArticle[]>([]);
  readonly traffickingPlatform = computed(() => {
    switch (this.brand()) {
      case 'mindmegette':
        return 'MME for Bors';
      case 'life':
        return 'Life for Bors';
    }
    return '';
  });

  readonly headerData = computed(() => {
    switch (this.brand()) {
      case 'mindmegette':
        return {
          logo: './assets/images/mme-logo.svg',
          link: 'https://www.mindmegette.hu',
          linkText: 'Tovább a Mindmegette.hu-ra',
        };
      case 'life':
        return {
          logo: './assets/images/life-logo.svg',
          link: 'https://www.life.hu',
          linkText: 'Tovább a Life.hu-ra',
        };
    }
    return undefined;
  });

  constructor() {
    effect(() => {
      const platform = this.traffickingPlatform();
      if (!platform) {
        return;
      }
      this.brandingBoxService
        .getTrafficDeflectorData(platform)
        .pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe((articles) => {
          this.articles.set(articles.slice(0, ARTICLE_LIMIT));
        });
    });
  }
}
