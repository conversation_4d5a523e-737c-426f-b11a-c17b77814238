@if (articles()?.length) {
  <div class="branding-box">
    @if (headerData(); as header) {
      <div class="branding-box-header">
        <div class="branding-box-logo-box">
          <img class="branding-box-logo" [src]="header.logo" alt="{{ traffickingPlatform() | titlecase }} logó" />
        </div>
        <a class="branding-box-link" [href]="header.link" target="_blank">
          {{ header.linkText }}
          <kesma-icon name="arrow-right-long" size="16" />
        </a>
      </div>
    }
    <div class="branding-box-data">
      @for (article of articles(); track article.id) {
        <article class="article">
          <a [href]="article.url" target="_blank" class="article-link">
            @switch (brand()) {
              @case ('life') {
                <div class="article-thumbnail-box">
                  <img [src]="article.image" [alt]="article.image" loading="lazy" class="article-thumbnail" />
                  @if (article?.is_adult) {
                    <kesma-icon class="adult-icon" name="life-adult" [size]="72" />
                  }
                </div>
                @if (article?.category) {
                  <div class="article-category">{{ article.category }}</div>
                }
                <h2 class="article-title">{{ article.title }}</h2>
              }
              @case ('mindmegette') {
                <div class="article-thumbnail-box">
                  <img [src]="article.image" [alt]="article.image" loading="lazy" class="article-thumbnail" />
                </div>
                <div class="article-data">
                  <div class="article-badges">
                    @if (article?.category) {
                      <div class="article-category">{{ article.category }}</div>
                    }
                    @if (article?.is_adult) {
                      <kesma-icon class="adult-icon" name="mme-adult" [size]="20" />
                    }
                    @if (article?.is_video) {
                      <kesma-icon class="video-icon" name="mme-video" [size]="20" />
                    }
                  </div>
                  <h2 class="article-title">{{ article.title }}</h2>
                  @if (article.author) {
                    <div class="article-author">
                      <img
                        class="article-author-avatar"
                        src="./assets/images/mme-avatar-placeholder.svg"
                        alt="{{ article.author }} profilképe"
                        loading="lazy"
                      />
                      {{ article.author }}
                    </div>
                  }
                </div>
              }
            }
          </a>
        </article>
      }
    </div>
  </div>
}
