@use 'shared' as *;

:host {
  display: flex;
  flex-direction: column;
  row-gap: 8px;
  padding: 32px 20px;
  border-top: 4px solid var(--kui-red-500);
  border-bottom: 4px solid var(--kui-red-500);
  text-align: center;

  @include media-breakpoint-down(sm) {
    padding: 16px 20px;
  }

  .information {
    font-size: 18px;
    font-weight: 700;
    line-height: 26px;
  }

  .disclaimer {
    font-size: 16px;
    line-height: 24px;
  }
}
