@use 'shared' as *;

:host {
  display: block;
  container-type: inline-size;
  color: var(--kui-black-950);
  width: 100%;
  .branding-box {
    display: flex;
    flex-direction: column;
    position: relative;
    gap: 32px;
    @include container-breakpoint-down(sm) {
      gap: 16px;
    }
  }
  .title {
    font-size: 48px;
    font-weight: 800;
    line-height: 42px;
    text-transform: uppercase;
    color: var(--kui-astronet-500);
    @include container-breakpoint-down(sm) {
      font-size: 28px;
      line-height: 28px;
    }
  }
  .description {
    font-size: 18px;
    line-height: 26px;
    @include container-breakpoint-down(sm) {
      font-size: 16px;
      line-height: 24px;
    }
  }
  .navigation {
    color: var(--kui-astronet-500);
    &.prev {
      rotate: -180deg;
    }
  }
  .link {
    display: flex;
    color: var(--kui-astronet-500);
    fill: var(--kui-astronet-500);
    align-items: center;
    gap: 10px;
    font-size: 16px;
    font-weight: 700;
    line-height: 20px;
    width: fit-content;
    &:hover {
      color: var(--kui-red-500);
      fill: var(--kui-red-500);
    }
  }
  .swipe {
    position: static;
  }
  .divider {
    background-color: #edebf1;
    height: 1px;
  }
  .article {
    &-thumbnail {
      object-fit: cover;
      transition: scale 300ms;
      aspect-ratio: 3/2;
      width: 100%;
      &-box {
        position: relative;
        overflow: hidden;
        margin-bottom: 16px;
      }
    }
    &-title {
      font-size: 16px;
      line-height: 18px;
      overflow-wrap: anywhere;
      @include container-breakpoint-down(sm) {
        font-size: 14px;
        line-height: 16px;
      }
    }
    &:hover {
      .article-thumbnail {
        scale: 1.1;
      }
      .article-title {
        color: var(--kui-astronet-500);
      }
    }
  }
  .badges {
    position: absolute;
    bottom: 10px;
    display: flex;
    gap: 8px;
    height: 24px;
    background-color: var(--kui-black-950);
    color: var(--kui-white);
    font-size: 12px;
    font-weight: 600;
    font-variant: all-small-caps;
    line-height: normal;
    align-items: center;
    z-index: 10;
    fill: var(--kui-white);
    kesma-icon {
      align-items: unset;
    }
    .with-icon {
      display: flex;
      gap: 4px;
      &:first-of-type {
        padding-left: 8px;
      }
      &:last-of-type {
        padding-right: 8px;
      }
    }
    .adult {
      padding: 4px 8px;
      background-color: var(--kui-red-400);
      height: 24px;
      border: 1px solid var(--kui-black-950);
      width: 34px;
    }
  }
  ::ng-deep .bottom-navigation {
    position: absolute;
    display: flex;
    gap: 16px;
    right: 0;
    top: 0;
    @include container-breakpoint-down(sm) {
      top: -5px;
    }
  }
}
