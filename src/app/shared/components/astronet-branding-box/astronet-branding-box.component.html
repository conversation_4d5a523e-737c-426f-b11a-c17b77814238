@if (articles()?.length && astronetDetails(); as astronetDetails) {
  <div class="branding-box">
    <h2 class="title">{{ astronetDetails.title }}</h2>

    @if (description()) {
      <div class="description">{{ description() }}</div>
    }

    <div
      kesma-swipe
      class="swipe"
      [data]="articles()"
      [itemTemplate]="itemTemplate"
      [breakpoints]="breakpoints"
      [previousNavigationTemplate]="previousNavigation"
      [nextNavigationTemplate]="nextNavigation"
      [useNavigation]="true"
    ></div>

    <ng-template #previousNavigation>
      <kesma-icon class="navigation prev" name="arrow-right" [size]="40" />
    </ng-template>

    <ng-template #nextNavigation>
      <kesma-icon class="navigation" name="arrow-right" [size]="40" />
    </ng-template>

    <ng-template #itemTemplate let-data="data">
      <article class="article">
        <a class="article-link" [href]="data.url" target="_blank">
          @if (data?.image) {
            <div class="article-thumbnail-box">
              <img class="article-thumbnail" [src]="data.image" [alt]="data.title" loading="lazy" />
              @if (data?.is_adult || data?.is_video) {
                <div class="badges">
                  @if (data?.is_video) {
                    <div class="with-icon">
                      <kesma-icon size="16" name="video" />
                      Videó
                    </div>
                  }
                  @if (data?.is_adult) {
                    <div class="adult">18+</div>
                  }
                </div>
              }
            </div>
          }
          <h2 class="article-title">{{ data.title }}</h2>
        </a>
      </article>
    </ng-template>

    <a class="link" [href]="astronetDetails.link" target="_blank">
      {{ astronetDetails.linkText }}
      <kesma-icon class="link-icon" name="arrow-right-long" [size]="16" />
    </a>

    <div class="divider"></div>
  </div>
}
