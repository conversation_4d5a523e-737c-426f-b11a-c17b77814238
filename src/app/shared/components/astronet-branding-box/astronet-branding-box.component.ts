import { ChangeDetectionStrategy, Component, computed, DestroyRef, effect, inject, input, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BrandingBoxService } from '../../services';
import { IconComponent, KesmaSwipeComponent, PersonalizedRecommendationArticle, SwipeBreakpoints } from '@trendency/kesma-ui';

@Component({
  selector: 'app-astronet-branding-box',
  templateUrl: './astronet-branding-box.component.html',
  styleUrl: './astronet-branding-box.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [IconComponent, KesmaSwipeComponent],
})
export class AstronetBrandingBoxComponent {
  private readonly brandingBoxService = inject(BrandingBoxService);
  private readonly destroyRef = inject(DestroyRef);

  readonly breakpoints: SwipeBreakpoints = {
    default: {
      itemCount: 2,
      gap: '16px',
    },
    576: {
      itemCount: 3.2,
    },
    700: {
      itemCount: 4.2,
    },
  };

  readonly brand = input.required<string>();
  readonly description = input<string | null>(null);

  readonly articles = signal<PersonalizedRecommendationArticle[]>([]);

  readonly traffickingPlatform = computed(() => {
    switch (this.brand()) {
      case 'asztrologia':
        return 'Astronet Asztrológia for Bors';
      case 'ezoteria':
        return 'Astronet Ezotéria for Bors';
      case 'test-es-lelek':
        return 'Astronet Test és Lélek for Bors';
      case 'szammisztika':
        return 'Astronet Számmisztika for Bors';
    }
    return '';
  });

  readonly astronetDetails = computed(() => {
    switch (this.brand()) {
      case 'asztrologia':
        return {
          title: 'Asztrológia',
          link: 'https://astronet.borsonline.hu/asztrologia/',
          linkText: 'Még Asztrológia',
        };
      case 'ezoteria':
        return {
          title: 'Ezotéria',
          link: 'https://astronet.borsonline.hu/ezoteria/',
          linkText: 'Még Ezotéria',
        };
      case 'test-es-lelek':
        return {
          title: 'Test és lélek',
          link: 'https://astronet.borsonline.hu/test-es-lelek/',
          linkText: 'Még Test és lélek',
        };
      case 'szammisztika':
        return {
          title: 'Számmisztika',
          link: 'https://astronet.borsonline.hu/tenyek-talanyok/szammisztika/',
          linkText: 'Még Számmisztika',
        };
    }
    return undefined;
  });

  constructor() {
    effect(() => {
      const platform = this.traffickingPlatform();
      if (!platform) {
        return;
      }
      this.brandingBoxService
        .getTrafficDeflectorData(platform)
        .pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe((articles) => {
          this.articles.set(articles.filter((article) => article?.id));
        });
    });
  }
}
