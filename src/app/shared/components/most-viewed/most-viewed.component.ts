import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ArticleCard, BaseComponent } from '@trendency/kesma-ui';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { ArticleCardType } from '../../definitions';

@Component({
  selector: 'app-most-viewed',
  templateUrl: './most-viewed.component.html',
  styleUrl: './most-viewed.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ArticleCardComponent],
})
export class MostViewedComponent extends BaseComponent<ArticleCard[]> {
  readonly ArticleCardType = ArticleCardType;
}
