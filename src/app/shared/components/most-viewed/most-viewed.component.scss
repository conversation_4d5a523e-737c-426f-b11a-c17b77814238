@use 'shared' as *;

:host {
  color: var(--kui-black-950);
  display: block;

  .article-list-wrapper {
    margin-bottom: 32px;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 32px;
    @include media-breakpoint-down(md) {
      margin-top: 16px;
      margin-bottom: 16px;
      gap: 16px;
    }
    .item-wrapper {
      width: 100%;
      display: flex;
      align-items: flex-start;
      border-bottom: 1px solid rgba($black, 0.2);
      gap: 32px;
      @include media-breakpoint-down(md) {
        gap: 16px;
      }
      .number-wrapper {
        border: 2px solid var(--kui-red-400);
        color: var(--kui-red-400);
        display: flex;
        width: 40px;
        height: 40px;
        font-family: var(--kui-font-primary);
        justify-content: center;
        align-items: center;
        font-size: 24px;
        font-style: normal;
        font-weight: 700;
        line-height: 36px;
      }
      ::ng-deep {
        .article-tag {
          color: var(--kui-red-400);
        }
      }
    }
  }
}
