@use 'shared' as *;

:host {
  display: block;
  container-type: inline-size;
  color: var(--kui-black-950);
  width: 100%;
  .horoscope {
    display: flex;
    flex-direction: column;
    position: relative;
    gap: 32px;
    @include container-breakpoint-down(sm) {
      gap: 16px;
    }
  }
  .title {
    font-size: 48px;
    font-weight: 800;
    line-height: 42px;
    text-transform: uppercase;
    color: var(--kui-astronet-500);
    @include container-breakpoint-down(sm) {
      font-size: 28px;
      line-height: 28px;
    }
  }
  .description {
    font-size: 18px;
    line-height: 26px;
    @include container-breakpoint-down(sm) {
      font-size: 16px;
      line-height: 24px;
    }
  }
  .navigation {
    color: var(--kui-astronet-500);
    &.prev {
      rotate: -180deg;
    }
  }
  .link {
    display: flex;
    color: var(--kui-astronet-500);
    fill: var(--kui-astronet-500);
    align-items: center;
    gap: 10px;
    font-size: 16px;
    font-weight: 700;
    line-height: 20px;
    width: fit-content;
    &:hover {
      color: var(--kui-red-500);
      fill: var(--kui-red-500);
    }
  }
  .swipe {
    position: static;
    &::ng-deep {
      .item-container {
        align-items: stretch;
      }
    }
  }
  .divider {
    background-color: #edebf1;
    height: 1px;
  }
  .article {
    background-color: #edebf1;
    height: 100%;
    &-thumbnail {
      object-fit: cover;
      transition: scale 300ms;
      aspect-ratio: 3/2;
      width: 100%;
      &-box {
        position: relative;
        overflow: hidden;
      }
    }
    &-data {
      padding: 16px;
      color: var(--kui-astronet-500);
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    &-title {
      font-size: 20px;
      line-height: 24px;
      overflow-wrap: anywhere;
      @include container-breakpoint-down(sm) {
        font-size: 18px;
        line-height: 22px;
      }
    }
    &-column {
      font-size: 12px;
      font-weight: 500;
      line-height: 18px;
      margin-block: 3px;
    }
    &:hover {
      .article-thumbnail {
        scale: 1.1;
      }
    }
  }
  ::ng-deep .bottom-navigation {
    position: absolute;
    display: flex;
    gap: 16px;
    right: 0;
    top: 0;
    @include container-breakpoint-down(sm) {
      top: -5px;
    }
  }
}
