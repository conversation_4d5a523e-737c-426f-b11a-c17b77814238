<div class="horoscope">
  <h2 class="title">Horoszkóp</h2>

  @if (description()) {
    <div class="description">{{ description() }}</div>
  }

  <div
    kesma-swipe
    class="swipe"
    [data]="data"
    [itemTemplate]="itemTemplate"
    [breakpoints]="breakpoints"
    [previousNavigationTemplate]="previousNavigation"
    [nextNavigationTemplate]="nextNavigation"
    [useNavigation]="true"
  ></div>

  <ng-template #previousNavigation>
    <kesma-icon class="navigation prev" name="arrow-right" [size]="40" />
  </ng-template>

  <ng-template #nextNavigation>
    <kesma-icon class="navigation" name="arrow-right" [size]="40" />
  </ng-template>

  <ng-template #itemTemplate let-data="data">
    <article class="article">
      <a class="article-link" [href]="data.url" target="_blank">
        <div class="article-thumbnail-box">
          <img class="article-thumbnail" [src]="data.image" [alt]="data.title" loading="lazy" />
        </div>
        <div class="article-data">
          <h2 class="article-title">{{ data.title }}</h2>
          <div class="article-column">HOROSZKÓP</div>
        </div>
      </a>
    </article>
  </ng-template>

  <a class="link" href="https://astronet.borsonline.hu/horoszkop/" target="_blank">
    Még Horoszkóp
    <kesma-icon class="link-icon" name="arrow-right-long" [size]="16" />
  </a>

  <div class="divider"></div>
</div>
