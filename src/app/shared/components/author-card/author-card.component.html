<div class="author-card-avatar-wrapper">
  <img
    class="author-card-avatar"
    [class.is-placeholder]="!data?.avatar?.bigUrl"
    [src]="data?.avatar?.bigUrl || PlaceholderImg"
    [alt]="data?.public_author_name"
    [attr.loading]="isLazy() ? 'lazy' : 'eager'"
  />
</div>
<div class="author-card-data">
  <h2 class="author-card-title">{{ data?.public_author_name }}</h2>
  @if (data?.rank) {
    <div class="author-card-rank">{{ data?.rank }}</div>
  }
  @if (data?.facebook || data?.instagram || data?.tiktok) {
    <div class="author-card-social">
      @if (data?.tiktok) {
        <a class="hover-scale" [href]="data?.tiktok" target="_blank">
          <kesma-icon name="tiktok" size="30"></kesma-icon>
        </a>
      }
      @if (data?.instagram) {
        <a class="hover-scale" [href]="data?.instagram" target="_blank">
          <kesma-icon name="instagram" size="30"></kesma-icon>
        </a>
      }
      @if (data?.facebook) {
        <a class="hover-scale" [href]="data?.facebook" target="_blank">
          <kesma-icon name="facebook" size="30"></kesma-icon>
        </a>
      }
    </div>
  }
  @if (data?.public_author_description) {
    <div class="author-card-description">{{ data?.public_author_description }}</div>
  }
  @if (showArticlesLink() && data?.slug) {
    <a class="author-card-link" [routerLink]="['/szerzo', data?.slug]">
      A szerző cikkei
      <kesma-icon class="arrow-right" name="arrow-right-long" size="14" height="16"></kesma-icon>
    </a>
  }
</div>
