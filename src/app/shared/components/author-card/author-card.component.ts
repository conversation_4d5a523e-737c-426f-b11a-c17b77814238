import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { BaseComponent, IconComponent } from '@trendency/kesma-ui';
import { AuthorData } from '../../definitions';
import { RouterLink } from '@angular/router';
import { PlaceholderImg } from '../../constants';

@Component({
  selector: 'app-author-card',
  templateUrl: './author-card.component.html',
  styleUrl: './author-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, IconComponent],
})
export class AuthorCardComponent extends BaseComponent<AuthorData> {
  isLazy = input<boolean>(false);
  showArticlesLink = input<boolean>(true);

  readonly PlaceholderImg = PlaceholderImg;
}
