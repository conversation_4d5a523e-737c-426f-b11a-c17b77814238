import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';
import { PagerComponent as KesmaPagerComponent } from '@trendency/kesma-ui';
import { NgClass, NgIf } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-pager',
  templateUrl: '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/pager/pager.component.html',
  styleUrls: ['../../../../../node_modules/@trendency/kesma-ui/src/lib/components/pager/pager.component.scss', './pager.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgClass, RouterLink, NgIf],
})
export class PagerComponent extends KesmaPagerComponent implements OnInit {
  @Input() override hasSkipButton: boolean = true;
  @Input() override isListPager: boolean = true;

  override ngOnInit(): void {
    super.ngOnInit();
    super.maxDisplayedPages = 4;
  }
}
