@use 'shared' as *;

:host {
  display: block;
  .pager {
    gap: 10px;
    flex-wrap: wrap;
    margin-bottom: 0; // Remove inherited kesma-pager margin
    .list-pager {
      gap: 10px;
      .button-num {
        color: var(--kui-black-950);
        font-size: 16px;
        padding: 0 6px;
        &.active {
          background: var(--kui-red-500);
          color: var(--kui-white);
          font-weight: 700;
        }
      }
    }
    .button-num {
      border: 1px solid rgba($black-950, 0.2);
      line-height: normal;
      font-weight: 600;
      @extend %flex-container;

      &:hover {
        border: 1px solid var(--kui-red-500);
      }
    }
    .button-arrow {
      border: 1px solid rgba($black-950, 0.2);
      opacity: 1;
      @extend %flex-container;

      &:hover {
        border: 1px solid var(--kui-red-500);
        width: 42px;
        .icon {
          background-image: url('/assets/images/icons/arrow-right-red.svg');
          width: 14px;
          height: 14px;
        }
      }
      .icon-prev {
        transform: rotate(180deg);
      }
      &.next,
      &.prev {
        background-color: transparent;
      }
      .icon {
        width: 40px;
        height: 40px;
        background-image: url('/assets/images/icons/arrow-right.svg');
      }
      &.disabled {
        .icon-prev,
        .icon-next {
          opacity: 0.3;
        }
      }
    }
  }
}
%flex-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 40px;
  height: 40px;
}
