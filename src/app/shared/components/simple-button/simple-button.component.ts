import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconComponent, SimpleButtonComponent } from '@trendency/kesma-ui';
import { Ng<PERSON><PERSON>, NgI<PERSON>, <PERSON><PERSON><PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'app-simple-button',
  imports: [NgTemplateOutlet, IconComponent, NgClass, NgStyle, NgIf],
  templateUrl: '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/simple-button/simple-button.component.html',
  styleUrls: [
    '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/simple-button/simple-button.component.scss',
    './simple-button.component.scss',
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BorsSimpleButtonComponent extends SimpleButtonComponent {}
