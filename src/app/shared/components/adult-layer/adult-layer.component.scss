@use 'shared' as *;

:host {
  width: 100%;
  text-align: center;
  position: fixed;
  z-index: 1500;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  .adult-lead ::ng-deep {
    .adult-link {
      color: var(--kui-gray-150);
      text-decoration: underline;
    }
  }
}

.adult {
  height: 100%;
  background: var(--kui-black-950);
  display: flex;
  justify-content: center;
  align-items: center;

  &-wrapper {
    width: 720px;
    flex-direction: column;
    align-items: center;
    row-gap: 20px;

    &-left {
      width: 120px;

      @include media-breakpoint-down(sm) {
        width: 72px;
        height: 72px;
        margin-bottom: 0;
      }
    }

    &-right {
      margin-left: 0;
      width: 100%;
    }
  }

  &-warning-circle {
    border: 10px solid var(--kui-red-400);
    color: var(--kui-white);
    font-size: 60px;
    font-weight: 700;
    line-height: 80px;

    @include media-breakpoint-down(sm) {
      font-size: 32px;
      line-height: 36px;
      border: 6px solid var(--kui-red-400);
      width: 72px;
      height: 72px;
    }
  }

  &-title {
    margin-top: 0;

    &:after {
      display: none;
    }

    &-item {
      font-size: 32px;
      font-weight: 800;
      line-height: 36px;
      color: var(--kui-gray-50);
      text-transform: none;

      @include media-breakpoint-down(sm) {
        font-size: 24px;
        line-height: 28px;
      }
    }
  }

  &-lead,
  &-text {
    color: var(--kui-gray-150);
    font-size: 16px;
    line-height: 20px;
    letter-spacing: 0.08px;

    @include media-breakpoint-down(sm) {
      font-size: 14px;
      line-height: 18px;
    }
  }

  &-lead {
    font-weight: 700;
    margin-top: 20px;
    margin-bottom: 20px;

    @include media-breakpoint-down(sm) {
      margin-top: 12px;
      margin-bottom: 12px;
    }
  }

  &-text {
    font-weight: 400;
  }

  &-bottom {
    width: 100%;
    justify-content: center;
    margin: 40px 0 0;

    @include media-breakpoint-down(sm) {
      margin: 32px 0 0;
    }
  }

  &-button {
    width: 200px;
    background: var(--kui-black-950);
    border: 2px solid var(--kui-red-500);
    border-radius: 0;
    font-size: 16px;
    font-weight: 700;
    line-height: 20px;
    cursor: pointer;

    &-light {
      background: var(--kui-red-500);
    }
  }
}
