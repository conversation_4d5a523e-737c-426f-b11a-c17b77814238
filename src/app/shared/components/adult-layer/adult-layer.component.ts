import { ChangeDetectionStrategy, Component } from '@angular/core';
import { AdultComponent } from '@trendency/kesma-ui';

@Component({
  selector: 'app-adult-layer',
  imports: [],
  templateUrl: '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/adult/adult.component.html',
  styleUrls: ['../../../../../node_modules/@trendency/kesma-ui/src/lib/components/adult/adult.component.scss', './adult-layer.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AdultLayerComponent extends AdultComponent {
  override adultLead = `<p>Ez a tartalom olyan elemeket tartalmazhat, amely<PERSON> a hatályos jogszabályok kategóriái szerint kiskorúakra károsak lehetnek.
   Ha azt szeretné, hogy az ilyen tartalmakhoz erről a számítógépről kiskorú ne férhessen hozzá, használjon szűrőprogramot!
   A javasolt szűrőprogram elérhető <a class="adult-link" href="https://mte.hu/gyermekbarat-internet/" target="_blank">ide kattintva.</a></p>`;

  override adultText = `Ha ön elmúlt 18 éves, kattintson az "Elmúltam 18 éves" gombra és a tartalom az ön számára elérhető lesz.
    Ha ön nem múlt el 18 éves, kattintson a "Nem múltam el 18 éves" gombra; ez a tartalom az ön számára nem lesz elérhető.`;

  override acceptedText = 'Elmúltam 18 éves';
  override declinedText = 'Nem múltam el 18 éves';
}
