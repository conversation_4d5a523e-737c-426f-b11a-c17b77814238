@use 'shared' as *;

:host {
  transform: scale(1);
  position: fixed;
  max-width: $global-wrapper-width-with-bg;
  width: 100%;
  top: 70%;
  z-index: 10;
  @include media-breakpoint-up(lg) {
    margin-inline: -32px;
  }
  .breaking-news {
    position: fixed;
    right: 0;
    background-color: var(--kui-yellow-200);
    padding: 20px 16px;
    display: flex;
    flex-direction: column;
    max-width: 448px;
    width: 100%;
    gap: 8px;
    @include media-breakpoint-down(md) {
      padding: 16px;
      max-width: 343px;
    }
    &-top {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
    }
    &-text {
      color: var(--kui-red-500);
      font-size: 24px;
      font-weight: 800;
      line-height: 24px;
    }
    &-title {
      font-size: 20px;
      line-height: 24px;
      color: var(--kui-dark-blue-800);
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      overflow: hidden;
      overflow-wrap: anywhere;
      &:hover {
        color: var(--kui-black-950);
      }
      @include media-breakpoint-up(lg) {
        margin-right: 20px;
      }
    }
    kesma-icon {
      fill: var(--kui-dark-blue-800);
      flex-shrink: 0;
      cursor: pointer;
      &:hover {
        fill: var(--kui-red-500);
      }
    }
  }
}
