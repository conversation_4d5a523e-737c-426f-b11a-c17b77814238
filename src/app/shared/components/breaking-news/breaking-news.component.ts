import { ChangeDetectionStrategy, Component, signal } from '@angular/core';
import { BaseComponent, BreakingBlock, IconComponent, buildArticleUrl, ArticleCard } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-breaking-news',
  templateUrl: './breaking-news.component.html',
  styleUrl: './breaking-news.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [IconComponent, RouterLink],
})
export class BreakingNewsComponent extends BaseComponent<BreakingBlock> {
  isShown = signal<boolean>(true);
  articleLink = signal<string[]>([]);

  constructor() {
    super();
    //It should be automatically hidden after 5 seconds.
    setTimeout(() => {
      this.isShown.set(false);
    }, 5_000);
  }

  protected override setProperties(): void {
    this.articleLink.set(this.data ? buildArticleUrl(this.data as ArticleCard) : []);
  }
}
