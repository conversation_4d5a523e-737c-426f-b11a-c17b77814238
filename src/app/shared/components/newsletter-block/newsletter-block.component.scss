@use 'shared' as *;

.newsletter-block {
  display: flex;
  flex-direction: column;
  gap: 32px;
  background-color: var(--kui-black-950);
  padding: 20px 32px 32px 32px;
  clip-path: polygon(0 20px, 101% 0, 100% 100%, 0 100%);

  @include media-breakpoint-down(md) {
    padding: 10px 16px 32px 16px;
    gap: 16px;
    clip-path: polygon(0 10px, 101% 0, 100% 100%, 0 100%);
  }

  &.small {
    padding: 10px 16px 32px 16px;
    gap: 16px;
    clip-path: polygon(0 10px, 101% 0, 100% 100%, 0 100%);
  }

  &-title {
    color: var(--kui-white);
    font-size: 48px;
    line-height: 42px;
    font-weight: 800;
    text-transform: uppercase;
    padding-top: 32px;

    @include media-breakpoint-down(md) {
      font-size: 28px;
      line-height: 28px;
    }

    &.small {
      font-size: 28px;
      line-height: 28px;
    }
  }

  &-text {
    color: var(--kui-white);
    font-size: 24px;
    font-weight: 500;
    line-height: 30px;
    letter-spacing: 0.12px;

    span {
      font-weight: 700;
    }

    @include media-breakpoint-down(md) {
      font-size: 16px;
      letter-spacing: 0.08px;
      line-height: 24px;
    }

    &.small {
      font-size: 16px;
      letter-spacing: 0.08px;
      line-height: 24px;
    }
  }

  &-link {
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--kui-white);
    font-weight: 700;
    line-height: 20px;
    background-color: var(--kui-red-500);
    padding: 0 16px;
    height: 40px;
    max-width: max-content;

    &:hover {
      background-color: var(--kui-red-1100);
    }
  }
}
