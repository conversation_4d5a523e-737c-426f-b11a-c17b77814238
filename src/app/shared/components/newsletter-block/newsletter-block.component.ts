import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';
import { RouterLink } from '@angular/router';
import { NgClass } from '@angular/common';

@Component({
  selector: 'app-newsletter-block',
  imports: [RouterLink, NgClass],
  templateUrl: './newsletter-block.component.html',
  styleUrl: './newsletter-block.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NewsletterBlockComponent {
  isSidebar = input(false);
  desktopWidth = input(12);
  componentWidth = computed(() => (this.isSidebar() ? 1 : this.desktopWidth()));
}
