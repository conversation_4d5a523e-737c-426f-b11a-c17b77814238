import { ChangeDetectionStrategy, Component, computed, DestroyRef, effect, inject, input, InputSignal, signal } from '@angular/core';
import { ApiResult, Article, ArticleCard, buildArticleUrl, FocusPointDirective, IconComponent, MinuteToMinuteBlock } from '@trendency/kesma-ui';
import { ReqService } from '@trendency/kesma-core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { PageTitleComponent } from '../page-title/page-title.component';
import { PlaceholderImgWithBg } from '../../constants';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { ArticleCardType } from '../../definitions';
import { FormatPipeModule } from 'ngx-date-fns';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-minute-by-minute',
  templateUrl: './minute-by-minute.component.html',
  styleUrl: './minute-by-minute.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [PageTitleComponent, IconComponent, FocusPointDirective, ArticleCardComponent, FormatPipeModule, RouterLink],
})
export class MinuteByMinuteComponent {
  private readonly reqService = inject(ReqService);
  private readonly destroyRef = inject(DestroyRef);

  readonly data: InputSignal<ArticleCard> = input.required();
  readonly articleUrl = computed<string>(() => `/content-page/article/${buildArticleUrl(this.data()).slice(1).join('/')}`);

  readonly minuteByMinuteBlocks = signal<MinuteToMinuteBlock[]>([]);

  constructor() {
    effect(() => {
      const url = this.articleUrl();
      const timestamp = Math.floor(new Date().getTime()); // For avoid SSR cache.
      this.reqService
        .get<ApiResult<Article>>(`${url}?t=${timestamp}`)
        .pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe(({ data: { minuteToMinuteBlocks } }) => {
          if (minuteToMinuteBlocks?.length) {
            this.minuteByMinuteBlocks.set(minuteToMinuteBlocks);
          }
        });
    });
  }

  readonly PlaceholderImgWithBg = PlaceholderImgWithBg;
  readonly ArticleCardType = ArticleCardType;
  readonly buildArticleUrl = buildArticleUrl;
}
