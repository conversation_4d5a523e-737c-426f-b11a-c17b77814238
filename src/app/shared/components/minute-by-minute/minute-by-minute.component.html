<div class="minute-by-minute">
  <div class="header">
    <kesma-icon class="mbm-icon" name="live" [size]="36" />
    <app-page-title title="Percről percre" />
  </div>
  <div class="body">
    <div class="article">
      <app-article-card class="article-card" [data]="data()" [styleId]="ArticleCardType.MinuteToMinute"></app-article-card>
      @for (post of minuteByMinuteBlocks(); track post.id) {
        <div class="divider"></div>
        <section class="mbm">
          <div class="mbm-date">{{ post.date | dfnsFormat: 'H:mm' }}</div>
          <a [routerLink]="buildArticleUrl(data())" [fragment]="'pp-' + post.id">
            <h2 class="mbm-title">{{ post.title }}</h2>
          </a>
        </section>
        @if ($last) {
          <div class="divider"></div>
        }
      }
    </div>
    <a class="thumbnail-wrapper" [routerLink]="buildArticleUrl(data())">
      <img
        class="thumbnail"
        withFocusPoint
        [data]="data()?.thumbnailFocusedImages"
        [displayedAspectRatio]="{ desktop: '3:2' }"
        [displayedUrl]="data()?.thumbnail?.url || PlaceholderImgWithBg"
        [alt]="data()?.thumbnail?.alt || data().title"
        loading="lazy"
      />
    </a>
  </div>
</div>
