import { Component } from '@angular/core';
import { BaseComponent, IconComponent, RelatedType, SimplifiedMenuItem } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';

declare let __tcfapi: (command: string, version?: number, callback?: (response: unknown, success: boolean) => void, param?: unknown) => void;

@Component({
  selector: 'app-footer',
  templateUrl: './footer.component.html',
  styleUrl: './footer.component.scss',
  imports: [IconComponent, RouterLink],
})
export class FooterComponent extends BaseComponent<SimplifiedMenuItem[]> {
  readonly currentYear: number = new Date().getFullYear();
  readonly RelatedType = RelatedType;

  openCookieSettings(): void {
    __tcfapi('displayConsentUi', 2, () => {}, true);
  }
}
