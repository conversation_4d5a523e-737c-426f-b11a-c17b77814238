import { AfterViewInit, ChangeDetectionStrategy, Component, EventEmitter, Input, Output, ViewEncapsulation } from '@angular/core';
import { DateTimePickerComponent } from '@trendency/kesma-ui';
import { addDays, format, isBefore } from 'date-fns';
import { FormatDatePipe } from '@trendency/kesma-core';

@Component({
  selector: 'app-archive-date-picker',
  templateUrl: './archive-date-picker.component.html',
  styleUrls: ['../../../../../node_modules/flatpickr/dist/flatpickr.min.css', './archive-date-picker.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [FormatDatePipe],
})
export class ArchiveDatePickerComponent extends DateTimePickerComponent implements AfterViewInit {
  override enableTime: boolean = false;
  override showDropdowns: boolean = true;
  override inline: boolean = true;
  override useElementRef: boolean = true;
  override valueFormat: string = 'yyyy-MM-dd';
  override id: string = 'archiveDatePicker';
  override minDateTime: string | Date = '1998-01-01';
  override maxDateTime: string | Date = new Date();

  @Input() selectedDate: string | Date = new Date();
  @Output() onSelectedDateChange: EventEmitter<Date> = new EventEmitter<Date>();

  override ngAfterViewInit(): void {
    super.ngAfterViewInit();

    this.flatpickrInstance?.setDate(format(new Date(this.selectedDate), this.valueFormat));
    this.flatpickrInstance?.config?.onChange?.push((date: Date[]) => {
      if ((date?.length ?? 0) === 1) {
        this.selectedDate = date?.[0];
        this.onSelectedDateChange.emit(this.selectedDate);
        this.changeDetectorRef.detectChanges();
      }
    });
  }

  handlePreviousButtonClick(): void {
    const newDate: Date = addDays(new Date(this.selectedDate), -1);

    if (!this.minDateTime || (this.minDateTime && !isBefore(newDate, new Date(this.minDateTime)))) {
      this.flatpickrInstance?.setDate(format(newDate, this.valueFormat), true);
    }
  }

  handleNextButtonClick(): void {
    const newDate: Date = addDays(new Date(this.selectedDate), 1);

    if (isBefore(newDate, new Date(this.maxDateTime))) {
      this.flatpickrInstance?.setDate(format(newDate, this.valueFormat), true);
    }
  }
}
