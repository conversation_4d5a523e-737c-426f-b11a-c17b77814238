@use 'shared' as *;

:host {
  width: 100%;
  display: block;
}
.flatpickr-months {
  margin-top: 0px !important;
  padding: 2px;
}
.flatpickr-calendar.inline {
  background-color: var(--kui-red-100);
  border-radius: 0px;
}
.archive-date-picker {
  position: relative;
  width: 100%;
  background-color: var(--kui-red-100);

  .day-selector {
    display: flex;
    justify-content: space-between;
    padding: 10px;

    &-day {
      font-weight: 500;
    }

    .button-arrow {
      color: var(--kui-white);
      background-color: var(--kui-red-500);
      width: 36px;
      height: 36px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background-color 0.3s ease;

      i {
        width: 24px;
        height: 24px;
      }

      &.prev i {
        transform: rotate(-180deg);
      }

      &:hover {
        background-color: var(--kui-red-800);
      }
    }
  }
}

.flatpickr-calendar {
  top: 0 !important;
  left: 0;
  border: none;
  box-shadow: none;
  width: 100%;
  background: transparent;

  &:before,
  &:after {
    display: none;
  }

  .flatpickr-months {
    display: flex;
    justify-content: space-between;
    margin: 10px;

    @media screen and (max-width: 400px) {
      height: 70px;
      align-items: center;
    }

    .flatpickr-month {
      height: initial;

      @media screen and (max-width: 400px) {
        height: 100%;
      }
    }

    &.noDropdowns {
      .flatpickr-month {
        display: none;
      }
    }

    .flatpickr-prev-month,
    .flatpickr-next-month {
      position: relative;
      height: 36px;
      width: 36px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: var(--kui-white);
      background-color: var(--kui-red-500);
      border-radius: 4px;
      transition: background-color 0.3s ease;

      &:hover {
        background-color: var(--kui-red-800);
      }

      svg {
        fill: none;
        width: 24px;
        height: 24px;
        background-size: 24px 24px;
        background-position: center;
        background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIj4KICA8cGF0aCBkPSJNMTUuODcwOCAxMS4yODk1TDEwLjIxMDggNS42Mzk1NUMxMC4xMTc4IDUuNTQ1ODIgMTAuMDA3MiA1LjQ3MTQzIDkuODg1MzcgNS40MjA2NkM5Ljc2MzUxIDUuMzY5ODkgOS42MzI4IDUuMzQzNzUgOS41MDA3OSA1LjM0Mzc1QzkuMzY4NzggNS4zNDM3NSA5LjIzODA3IDUuMzY5ODkgOS4xMTYyMiA1LjQyMDY2QzguOTk0MzYgNS40NzE0MyA4Ljg4Mzc2IDUuNTQ1ODIgOC43OTA3OSA1LjYzOTU1QzguNjA0NTQgNS44MjY5MSA4LjUgNi4wODAzNiA4LjUgNi4zNDQ1NUM4LjUgNi42MDg3MyA4LjYwNDU0IDYuODYyMTkgOC43OTA3OSA3LjA0OTU1TDEzLjc0MDggMTIuMDQ5NUw4Ljc5MDc5IDE2Ljk5OTVDOC42MDQ1NCAxNy4xODY5IDguNSAxNy40NDA0IDguNSAxNy43MDQ1QzguNSAxNy45Njg3IDguNjA0NTQgMTguMjIyMiA4Ljc5MDc5IDE4LjQwOTVDOC44ODM0MSAxOC41MDQgOC45OTM4NSAxOC41NzkyIDkuMTE1NzMgMTguNjMwN0M5LjIzNzYgMTguNjgyMiA5LjM2ODQ5IDE4LjcwOSA5LjUwMDc5IDE4LjcwOTVDOS42MzMxIDE4LjcwOSA5Ljc2Mzk4IDE4LjY4MjIgOS44ODU4NiAxOC42MzA3QzEwLjAwNzcgMTguNTc5MiAxMC4xMTgyIDE4LjUwNCAxMC4yMTA4IDE4LjQwOTVMMTUuODcwOCAxMi43NTk1QzE1Ljk3MjMgMTIuNjY1OSAxNi4wNTMzIDEyLjU1MjMgMTYuMTA4NyAxMi40MjU4QzE2LjE2NDEgMTIuMjk5MyAxNi4xOTI3IDEyLjE2MjcgMTYuMTkyNyAxMi4wMjQ1QzE2LjE5MjcgMTEuODg2NCAxNi4xNjQxIDExLjc0OTggMTYuMTA4NyAxMS42MjMzQzE2LjA1MzMgMTEuNDk2OCAxNS45NzIzIDExLjM4MzIgMTUuODcwOCAxMS4yODk1WiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+');
      }
    }

    .flatpickr-prev-month {
      svg {
        transform: rotate(-180deg);
      }
    }
  }

  .flatpickr-current-month {
    display: flex;
    justify-content: center;
    width: initial;
    position: relative;
    left: initial;
    margin: 0 8px;
    padding: 0;
    gap: 8px;
    height: 36px;

    @media screen and (max-width: 400px) {
      flex-direction: column;
      height: 100%;
      align-items: center;
    }

    .flatpickr-monthDropdown-months {
      background-color: var(--kui-white);
      border: 1px solid var(--kui-red-500);
      border-radius: 4px;
      color: var(--kui-gray-800);
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
      margin: 0;
      padding: 0 8px;
      order: 2;
      appearance: none;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 20 20' fill='none'%3E%3Cpath d='M14.1671 7.64174C14.0109 7.48653 13.7997 7.39941 13.5796 7.39941C13.3594 7.39941 13.1482 7.48653 12.9921 7.64174L10.0004 10.5917L7.0504 7.64174C6.89427 7.48653 6.68306 7.39941 6.4629 7.39941C6.24275 7.39941 6.03154 7.48653 5.8754 7.64174C5.7973 7.71921 5.7353 7.81138 5.693 7.91293C5.65069 8.01448 5.62891 8.1234 5.62891 8.23341C5.62891 8.34342 5.65069 8.45234 5.693 8.55389C5.7353 8.65544 5.7973 8.74761 5.8754 8.82507L9.40874 12.3584C9.48621 12.4365 9.57837 12.4985 9.67992 12.5408C9.78147 12.5831 9.89039 12.6049 10.0004 12.6049C10.1104 12.6049 10.2193 12.5831 10.3209 12.5408C10.4224 12.4985 10.5146 12.4365 10.5921 12.3584L14.1671 8.82507C14.2452 8.74761 14.3072 8.65544 14.3495 8.55389C14.3918 8.45234 14.4136 8.34342 14.4136 8.23341C14.4136 8.1234 14.3918 8.01448 14.3495 7.91293C14.3072 7.81138 14.2452 7.71921 14.1671 7.64174Z' fill='%230519d2'/%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: right 4px center;
      background-size: 20px 20px;
      min-width: 120px;

      &:hover {
        background-color: var(--kui-white);
      }
    }

    .numInputWrapper {
      border: 1px solid var(--kui-red-500);
      background-color: var(--kui-white);
      border-radius: 4px;
      margin: 0;
      padding: 0 8px;
      width: 80px;
      order: 1;

      &:hover {
        background: var(--kui-white);
      }

      input.cur-year {
        height: 33px;
        padding: 0;
        color: var(--kui-gray-800);
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
      }

      span {
        border-color: var(--kui-red-500);
        opacity: 1;

        &:hover {
          background: initial;
        }

        &.arrowUp {
          border-top: 0;
          border-right: 0;

          &:after {
            border-bottom-color: var(--kui-red-500);
          }
        }

        &.arrowDown {
          border-right: 0;
          border-bottom: 0;

          &:after {
            border-top-color: var(--kui-red-500);
          }
        }
      }
    }
  }

  .flatpickr-innerContainer {
    margin: 0;
    padding: 10px;

    .flatpickr-rContainer {
      width: 100%;

      .flatpickr-weekdays {
        .flatpickr-weekdaycontainer {
          .flatpickr-weekday {
            color: var(--kui-gray-800);
          }
        }
      }

      .flatpickr-days {
        width: 100%;

        .dayContainer {
          width: 100%;
          min-width: 100%;
          max-width: 100%;
          gap: 5px;

          .flatpickr-day {
            max-width: calc((100% - 6 * 5px) / 7);
            color: var(--kui-gray-800);
            border-radius: 4px;

            &.selected {
              background-color: var(--kui-red-500);
              border-color: var(--kui-red-500);
              color: var(--kui-white);
              border-radius: 4px;
            }

            &.today {
              border-color: transparent;

              &:hover {
                border-color: var(--kui-red-500);
              }
            }

            &:hover:not(.selected) {
              background-color: transparent;
              border-color: var(--kui-red-500);
              color: var(--kui-gray-800);
              border-radius: 4px;
            }

            &.prevMonthDay,
            &.nextMonthDay {
              border-radius: 4px;
              color: var(--kui-gray-400);

              &:hover {
                color: var(--kui-gray-400);
              }

              &.selected {
                color: var(--kui-white);
              }
            }

            &.flatpickr-disabled {
              color: var(--kui-gray-200);

              &:hover {
                background-color: transparent;
                border-color: transparent;
                color: var(--kui-gray-200);
              }
            }
          }
        }
      }
    }
  }
}
.icon {
  display: inline-block;
  min-width: 5px;
  min-height: 5px;
  background-position: center;
  background-size: contain;
  background-repeat: no-repeat;
  &.icon-mno-chevron-right {
    @include icon('icons/icon-mno-chevron-right.svg');
  }
}
