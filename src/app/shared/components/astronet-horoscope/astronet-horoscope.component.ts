import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { AstronetService } from '../../services';
import { toSignal } from '@angular/core/rxjs-interop';
import { IconComponent, KesmaSwipeComponent, SwipeBreakpoints } from '@trendency/kesma-ui';
import { TitleCasePipe } from '@angular/common';

const BREAKPOINTS: SwipeBreakpoints = {
  default: {
    itemCount: 1,
    gap: '32px',
  },
  600: {
    itemCount: 2,
    gap: '32px',
  },
  760: {
    itemCount: 3,
    gap: '32px',
  },
  992: {
    itemCount: 4,
    gap: '32px',
  },
};

@Component({
  selector: 'app-astronet-horoscope',
  templateUrl: './astronet-horoscope.component.html',
  styleUrl: './astronet-horoscope.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [KesmaSwipeComponent, IconComponent, TitleCasePipe],
})
export class AstronetHoroscopeComponent {
  private readonly astronetService = inject(AstronetService);
  data = toSignal(this.astronetService.getAstronomy());
  breakpoints = BREAKPOINTS;
}
