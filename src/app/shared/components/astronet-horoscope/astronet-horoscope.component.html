<div class="horoscope">
  <div class="horoscope-header">
    <div class="horoscope-header-left">
      <img class="astronet-logo" src="assets/images/astronet-logo.png" alt="Astronet" />
      <h2 class="horoscope-header-left-title">HOROSZKÓP</h2>
    </div>
  </div>
  <div class="divider"></div>
  <ng-template #itemTemplate let-element="data" let-index="index">
    <div class="horoscope-card">
      <div class="horoscope-card-header">
        <div class="horoscope-image">
          <kesma-icon class="horoscope-icon" [name]="element.icon" />
        </div>
        <div>
          <div class="horoscope-card-header-name">{{ element.name | titlecase }}</div>
          <div class="horoscope-card-header-date">{{ element.signRange }}</div>
        </div>
      </div>
      <div class="horoscope-card-text">{{ element.text }}</div>
      <a [href]="'https://astronet.hu' + element.signSlug" target="_blank" class="horoscope-card-link">
        <span class="astronet-fortune-card-name">További részletek</span>
        <kesma-icon class="horoscope-card-link-icon" name="arrow-right-long" size="24" />
      </a>
    </div>
  </ng-template>
  <ng-template #previousNavigation><kesma-icon name="chevron-left" /></ng-template>
  <ng-template #nextNavigation><kesma-icon name="chevron-right" /></ng-template>
  @if (data()?.list) {
    <div
      kesma-swipe
      [itemTemplate]="itemTemplate"
      [previousNavigationTemplate]="previousNavigation"
      [nextNavigationTemplate]="nextNavigation"
      [useNavigation]="true"
      [usePagination]="true"
      [data]="data()?.list"
      [breakpoints]="breakpoints"
      class="horoscope-card-swipe"
    ></div>
  }
</div>
