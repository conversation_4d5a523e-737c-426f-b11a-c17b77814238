@use 'shared' as *;

$asztronet-300: #c9c2d6;
$asztronet-400: #b7aec8;
$asztronet-500: #4b3575;
$asztronet-1000: #4b3575;

:host {
  position: relative;
  display: block;
  .horoscope-card {
    display: flex;
    flex-direction: column;
    gap: 8px;
    &-swipe {
      position: static;
      ::ng-deep {
        .item-container {
          align-items: stretch;
        }

        .bottom-navigation {
          position: absolute;
          right: 32px;
          top: 40px;
          color: $asztronet-1000;
          fill: $asztronet-1000;

          @include media-breakpoint-down(sm) {
            right: 16px;
            top: 50px;
          }

          .navigation-button kesma-icon {
            width: 40px;
            color: $asztronet-1000;
          }
        }
      }
    }
  }
}

.astronet-logo {
  @include media-breakpoint-down(sm) {
    width: 98px;
    height: auto;
  }
}

.horoscope {
  background-color: $asztronet-300;
  padding: 32px;
  display: flex;
  flex-direction: column;
  row-gap: 32px;

  @include media-breakpoint-down(sm) {
    padding: 16px;
  }

  &-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;

    &-left {
      display: flex;
      flex-direction: row;
      align-items: center;
      column-gap: 16px;

      @include media-breakpoint-down(sm) {
        flex-direction: column;
        align-items: flex-start;
      }

      &-title {
        font-size: 48px;
        font-style: normal;
        font-weight: 800;
        line-height: 42px;
        text-transform: uppercase;

        @include media-breakpoint-down(sm) {
          font-size: 28px;
          line-height: 28px;
        }
      }
    }
  }

  &-card {
    display: flex;
    flex-direction: column;
    flex: 1;
    height: 100%;

    &-header {
      display: flex;
      flex-direction: row;
      column-gap: 16px;
      margin-bottom: 32px;

      &-name {
        font-size: 32px;
        font-weight: 800;
        line-height: 42px;

        @include media-breakpoint-down(sm) {
          font-size: 24px;
          line-height: 28px;
        }
      }

      &-date {
        font-size: 14px;
        font-weight: 500;
        line-height: normal;
        color: $asztronet-1000;
      }
    }

    &-text {
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
      margin-bottom: 16px;
    }

    &-link {
      display: flex;
      flex-direction: row;
      align-items: center;
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
      line-height: 20px;
      column-gap: 10px;
      margin-top: auto;
      color: $asztronet-500;

      &-icon {
        color: $asztronet-500;
        fill: $asztronet-500;
      }
    }
  }

  &-image {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60px;
    width: 60px;
    border-radius: 50%;
    background-color: var(--kui-white);
  }

  &-icon {
    color: $asztronet-500;
    height: 30px;
  }
}

.divider {
  height: 1px;
  background-color: $asztronet-400;

  @include media-breakpoint-down(sm) {
    margin-top: -16px;
  }
}
