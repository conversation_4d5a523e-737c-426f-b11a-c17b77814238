import { ChangeDetectionStrategy, Component, computed, inject, input, Signal } from '@angular/core';
import { RouterLink } from '@angular/router';
import { HeaderService } from '../../services';

@Component({
  selector: 'app-sub-columns-box',
  imports: [RouterLink],
  templateUrl: './sub-columns-box.component.html',
  styleUrl: './sub-columns-box.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SubColumnsBoxComponent {
  headerService = inject(HeaderService);
  data = input.required({
    transform: (
      data: {
        id: string;
        title: string;
        slug: string;
      }[]
    ) => (data?.length ? data.filter((item) => !!item) : []),
  });

  columnColor: Signal<string> = computed(() => this.headerService.backgroundColor());
}
