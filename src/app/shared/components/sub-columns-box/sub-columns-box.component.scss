@use 'shared' as *;

:host {
  .wrapper {
    padding: 32px;
    display: flex;
    flex-direction: column;
    gap: 32px;
    clip-path: polygon(0 10px, 101% 0, 100% 100%, 0 100%);

    .title {
      font-size: 28px;
      font-weight: 800;
      line-height: 28px;
      padding-top: 10px;
      color: var(--kui-white);
      text-transform: uppercase;
    }

    .items {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;

      .item {
        display: flex;
        align-items: center;
        color: var(--kui-white);
        height: 40px;
        padding: 0px 16px;
        border: 1px solid var(--kui-white);
        background-color: transparent;
        font-size: 16px;
        font-weight: 400;
        line-height: 20px;

        &:hover {
          border-width: 2px;
        }
      }
    }
  }
}
