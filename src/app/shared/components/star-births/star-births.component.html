<div class="star-births">
  @if (starsBornToday()?.length) {
    <h2 class="title">Ezen a napon született Sztárok</h2>
  }
  <div class="star-card-column">
    <div class="star-card-row">
      @if (starsBornToday()?.[0]; as star) {
        <ng-container *ngTemplateOutlet="StarTemplate; context: { star }"></ng-container>
      }
      @if (starsBornToday()?.[1]; as star) {
        <ng-container *ngTemplateOutlet="StarTemplate; context: { star }"></ng-container>
      }
    </div>
    <div class="star-card-row">
      @if (data?.[0]; as article) {
        <app-article-card [data]="article" [hasLeftBorder]="true" [hasBlockBackground]="true" [styleId]="ArticleCardType.NoImgTagTitle"></app-article-card>
      }
      @if (data?.[1]; as article) {
        <app-article-card [data]="article" [hasLeftBorder]="true" [hasBlockBackground]="true" [styleId]="ArticleCardType.NoImgTagTitle"></app-article-card>
      }
    </div>
  </div>
</div>
<ng-template #StarTemplate let-star="star">
  <div class="star-card-wrapper">
    <a class="star-card-avatar-box" [routerLink]="['/lexikon/sztar', star.slug]">
      <img class="star-card-avatar" [src]="star.profileImage.thumbnail" [alt]="star[star.displayContextName]" />
    </a>
    <a class="star-card-link" [routerLink]="['/lexikon/sztar', star.slug]">
      <h2 class="star-card-title">{{ star[star.displayContextName] }}</h2>
    </a>
    @if (star?.occupations?.[0]; as occupation) {
      <a class="star-card-occupation" [routerLink]="['/lexikon/sztar/kereso']" [queryParams]="{ 'occupations_filter[]': occupation.slug }">
        {{ occupation.title }}
      </a>
    }
  </div>
</ng-template>
