@use 'shared' as *;

:host {
  --clip-path-height: 20px;
  display: block;
  color: var(--kui-white);
  container-type: inline-size;
  width: 100%;
  @include media-breakpoint-down(md) {
    margin-inline: -15px;
    width: calc(100% + 30px);
  }
  .title {
    font-size: 48px;
    font-weight: 800;
    line-height: 42px;
    text-transform: uppercase;
    @include container-breakpoint-down(xs) {
      font-size: 28px;
      line-height: 28px;
    }
  }
  .star-births {
    display: flex;
    flex-direction: column;
    background-color: var(--kui-red-500);
    clip-path: polygon(0 var(--clip-path-height), 101% 0, 100% 100%, 0 100%);
    padding: calc(32px + #{var(--clip-path-height)}) 32px 32px;
    gap: 32px;
    @include container-breakpoint-down(xs) {
      --clip-path-height: 10px;
      padding: calc(16px + #{var(--clip-path-height)}) 16px 16px;
      gap: 16px;
    }
  }
  .star-card {
    &-row {
      display: flex;
      gap: 32px;
      width: 100%;
      @include container-breakpoint-down(xs) {
        gap: 16px;
        app-article-card::ng-deep {
          .article-data {
            margin-inline: 0px;
          }
        }
      }
    }
    &-column,
    &-wrapper {
      display: flex;
      flex-direction: column;
      gap: 8px;
      width: 100%;
    }
    &-avatar {
      object-fit: cover;
      aspect-ratio: 272/363;
      clip-path: polygon(0 0, 100% 0, 100% 100%, 0 calc(100% - #{var(--clip-path-height)}), 0 50%);
      width: 100%;
      @include container-breakpoint-down(xs) {
        aspect-ratio: 164/219;
      }
      &-box {
        display: block;
      }
    }
    &-link {
      width: fit-content;
    }
    &-title {
      color: var(--kui-white);
      font-size: 24px;
      font-weight: 700;
      line-height: 32px;
      letter-spacing: 0.72px;
      text-transform: uppercase;
      display: inline-block;
      &:hover {
        color: var(--kui-yellow-500);
      }
      @include container-breakpoint-down(xs) {
        font-size: 18px;
        line-height: 22px;
        letter-spacing: 0.54px;
      }
    }
    &-occupation {
      background-color: var(--kui-black-950);
      padding: 4px 8px;
      color: var(--kui-white);
      font-size: 12px;
      font-weight: 600;
      line-height: normal;
      font-variant: all-small-caps;
      width: fit-content;
      min-height: 24px;
      margin-bottom: 8px;
      &:hover {
        background-color: var(--kui-yellow-500);
        color: var(--kui-black-950);
      }
    }
  }

  app-article-card {
    margin-bottom: 0;
    --theme-color: var(--kui-white);
  }
}
