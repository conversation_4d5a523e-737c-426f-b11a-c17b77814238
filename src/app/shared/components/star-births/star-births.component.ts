import { ChangeDetectionStrategy, Component, inject, Signal } from '@angular/core';
import { IStarCard } from '../../../feature/lexicon/star/definitions/star.definitions';
import { toSignal } from '@angular/core/rxjs-interop';
import { map } from 'rxjs/operators';
import { NgTemplateOutlet } from '@angular/common';
import shuffle from 'lodash-es/shuffle';
import { RouterLink } from '@angular/router';
import { ArticleCardType } from '../../definitions';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { ArticleCard, BaseComponent } from '@trendency/kesma-ui';
import { StarService } from 'src/app/feature/lexicon/star/services/star.service';

@Component({
  selector: 'app-star-births',
  templateUrl: './star-births.component.html',
  styleUrl: './star-births.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgTemplateOutlet, RouterLink, ArticleCardComponent],
})
export class StarBirthsComponent extends BaseComponent<ArticleCard[]> {
  private readonly starService = inject(StarService);

  readonly starsBornToday: Signal<IStarCard[]> = toSignal(
    this.starService.search({ is_today_date_of_birth_filter: 1 }).pipe(map(({ data }) => shuffle(data))),
    {
      initialValue: [],
    }
  );

  readonly ArticleCardType = ArticleCardType;
}
