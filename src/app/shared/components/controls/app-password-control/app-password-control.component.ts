import { ChangeDetectionStrategy, Component, computed, Input, signal } from '@angular/core';
import { ControlContainer, FormGroupDirective, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IconComponent, KesmaFormControlComponent } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-password-control',
  templateUrl: './app-password-control.component.html',
  styleUrls: ['../app-input-control/app-input-control.component.scss', './app-password-control.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [FormsModule, KesmaFormControlComponent, ReactiveFormsModule, IconComponent, RouterLink],
  viewProviders: [
    {
      provide: ControlContainer,
      useExisting: FormGroupDirective,
    },
  ],
})
export class AppPasswordControlComponent {
  @Input() isRequired = false;
  @Input() showRecoveryLink = false;
  @Input() label = 'Jelszó';
  @Input() placeholder = 'Adja meg a jelszavát';
  @Input() formControlNamee = 'password';
  @Input() hideErrors = false;

  readonly showPassword = signal<boolean>(false);
  readonly passwordDetails = computed(() => ({
    type: this.showPassword() ? 'text' : 'password',
    icon: this.showPassword() ? 'no-eye' : 'eye',
  }));
}
