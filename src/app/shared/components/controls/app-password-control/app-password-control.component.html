<kesma-form-control [hideErrors]="hideErrors">
  <div class="label">
    <label class="bors-form-label" for="password"
      >{{ label }}
      @if (isRequired) {
        (kötelező)
      }
    </label>
    @if (showRecoveryLink) {
      <a class="password-recovery" routerLink="/elfelejtett-jels<PERSON>">Elfelejtette a jelszavát?</a>
    }
  </div>
  <div class="bors-form-input-password">
    <input class="bors-form-input" [formControlName]="formControlNamee" id="password" [type]="passwordDetails().type" [placeholder]="placeholder" />
    <kesma-icon class="icon-eye" [name]="passwordDetails().icon" [size]="24" (click)="showPassword.set(!showPassword())"></kesma-icon>
  </div>
  <ng-content></ng-content>
</kesma-form-control>
