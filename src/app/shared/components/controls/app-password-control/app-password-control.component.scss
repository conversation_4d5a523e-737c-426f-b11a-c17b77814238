:host {
  display: block;
  width: 100%;
  .bors-form-input-password {
    position: relative;
    background-color: inherit;
  }
  .icon-eye {
    position: absolute;
    right: 5px;
    top: 50%;
    translate: 0 -25%;
    cursor: pointer;
  }

  kesma-form-control::ng-deep {
    .bors-form-label {
      margin-bottom: 0;
    }

    .label {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 26px;
      margin-bottom: 8px;

      .password-recovery {
        color: var(--kui-red-500);
        font-size: 12px;
        font-weight: 400;
        line-height: 160%;
        text-decoration: underline;
      }
    }

    kesma-form-control-error {
      .form-error {
        position: static;
      }
    }
  }
}
