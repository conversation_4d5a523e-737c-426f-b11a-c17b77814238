:host {
  display: block;
  width: 100%;
  color: var(--kui-black-950);
  .bors-form-input {
    width: 100%;
    display: block;
    border: 1px solid var(--kui-black-950);
    padding: 8px 16px;
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    background-color: inherit;
    &.is-invalid.ng-touched {
      border-color: var(--kui-error);
    }
  }
  .bors-form-label {
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    margin-bottom: 8px;
    height: 26px;
  }

  kesma-form-control::ng-deep {
    input::placeholder {
      color: var(--kui-black-950);
    }
    small {
      display: block;
      font-size: 12px;
      font-weight: 400;
      line-height: 18px;
      margin-top: 8px;
    }

    kesma-form-control-error {
      .form-error {
        position: relative;
      }
    }
  }

  ::ng-deep {
    kesma-form-control-error {
      margin-top: 8px;
      display: block;
      .form-error {
        font-size: 12px;
        font-weight: 600;
        line-height: 18px;
      }
    }
  }
}
