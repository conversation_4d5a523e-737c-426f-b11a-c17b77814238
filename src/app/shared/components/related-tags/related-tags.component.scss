@use 'shared' as *;

:host {
  display: flex;
  flex-direction: column;
  background-color: var(--kui-red-500);
  color: var(--kui-white);
  clip-path: polygon(0 10px, 101% 0, 100% 100%, 0 100%);
  gap: 32px;
  width: 100%;
  padding: 32px;
  .title {
    font-size: 28px;
    line-height: 28px;
    font-weight: 800;
    padding-top: 10px;
  }
  .tags {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
  }
  .tag {
    color: inherit;
    font-size: 16px;
    line-height: 20px;
    outline: 1px solid var(--kui-white);
    padding: 10px 16px;
    overflow-wrap: anywhere;
    &:hover {
      color: var(--kui-yellow-550);
      outline-color: var(--kui-yellow-550);
    }
  }
}
