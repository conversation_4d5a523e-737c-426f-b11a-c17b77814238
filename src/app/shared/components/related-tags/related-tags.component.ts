import { ChangeDetectionStrategy, Component } from '@angular/core';
import { BaseComponent, Tag } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-related-tags',
  templateUrl: './related-tags.component.html',
  styleUrl: './related-tags.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink],
})
export class RelatedTagsComponent extends BaseComponent<Tag[]> {}
