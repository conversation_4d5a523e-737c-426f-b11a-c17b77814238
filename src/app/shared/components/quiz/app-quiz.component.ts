import { ChangeDetectionStrategy, Component, inject, OnInit } from '@angular/core';
import { IconComponent, Quiz, QuizAnswer, QuizComponent } from '@trendency/kesma-ui';
import { PageTitleComponent } from '../page-title/page-title.component';
import { PlaceholderImgWithBg } from '../../constants';
import { BorsSimpleButtonComponent } from '../simple-button/simple-button.component';
import { AuthService } from '../../services';
import { Router } from '@angular/router';
import { StorageService } from '@trendency/kesma-core';

const QUIZ_STORAGE_KEY_PREFIX = 'quiz_';

@Component({
  selector: 'app-quiz',
  templateUrl: './app-quiz.component.html',
  styleUrls: ['./app-quiz.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [PageTitleComponent, BorsSimpleButtonComponent, IconComponent],
})
export class AppQuizComponent extends QuizComponent implements OnInit {
  private readonly router = inject(Router);
  private readonly storage = inject(StorageService);
  readonly currentUser = inject(AuthService).currentUser;

  readonly PlaceholderImgWithBg = PlaceholderImgWithBg;

  currentQuestionIndex = 0;
  selectedAnswer?: QuizAnswer;
  isShowOnlyLoggedUsers?: boolean;

  override ngOnInit(): void {
    super.ngOnInit();
    this.isShowOnlyLoggedUsers = (this.data as Quiz & { isShowOnlyLoggedUsers?: boolean })?.isShowOnlyLoggedUsers;
    this.loadStoredData();
  }

  get isNextButtonVisible(): boolean {
    return !!this.data?.questions?.length && this.data.questions.length > this.currentQuestionIndex;
  }

  onPickAnswer(answer: QuizAnswer): void {
    this.selectedAnswer = answer;
  }

  onGetNextQuestion(): void {
    if ((this.data?.questions?.length && this.data.questions.length <= this.currentQuestionIndex) || !this.selectedAnswer) {
      return;
    }
    this.selectedAnswer = undefined;
    this.currentQuestionIndex++;
    this.currentQuestion = this.data?.questions[this.currentQuestionIndex];
  }

  onLogin(): void {
    this.storage.setSessionStorageData(`${QUIZ_STORAGE_KEY_PREFIX}${this.data?.id}`, this.givenAnswers);
    this.router
      .navigate(['/', 'bejelentkezes'], {
        queryParams: { redirect: this.router.routerState.snapshot.url },
      })
      .then();
  }

  private loadStoredData(): void {
    const storedData = this.storage.getSessionStorageData(`${QUIZ_STORAGE_KEY_PREFIX}${this.data?.id}`);
    if (!storedData) {
      return;
    }

    this.givenAnswers = storedData;
    this.currentQuestionIndex = this.data?.questions?.length ?? 0;
    this.checkFinished();
  }
}
