import { ChangeDetectionStrategy, Component, input, output } from '@angular/core';
import { NgSelectModule } from '@ng-select/ng-select';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-search-select',
  templateUrl: './search-select.component.html',
  styleUrl: './search-select.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgSelectModule, FormsModule],
})
export class SearchSelectComponent<T, V = string> {
  value = input.required<T>();
  items = input.required<T[]>();
  bindLabel = input<string>('title');
  bindValue = input<string>('slug');
  clearable = input<boolean>(false);
  valueChanged = output<V>();
  searchable = input<boolean>(true);
  compareFn = (a: any, b: any): boolean => {
    if (a === b) return true;

    if ((a === null || typeof a !== 'object') && (b === null || typeof b !== 'object')) {
      return a === b;
    }

    if (this.bindValue() && typeof this.bindValue() === 'string') {
      const key = this.bindValue();
      return a?.[key] === b?.[key];
    }

    return false;
  };
}
