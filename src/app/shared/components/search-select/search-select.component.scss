@use 'shared' as *;

:host ::ng-deep ng-select {
  .ng-dropdown-panel,
  .ng-select-container {
    border-color: var(--kui-black-950) !important;
    border-radius: 0;
  }
  .ng-value {
    color: var(--kui-black-950);
  }
  .ng-select-container {
    gap: 16px;
    border-radius: 0;
    font-weight: 600;
  }
  .ng-arrow-wrapper {
    background-image: url(/assets/images/icons/arrow-right.svg);
    transition: rotate 300ms;
    border: none;
    width: 40px;
    height: 40px;
    right: 10px;
    rotate: 90deg;
  }
  .ng-arrow {
    border: none !important;
  }
  .ng-value-container {
    padding-left: 16px !important;
  }
  .ng-option {
    padding-left: 16px !important;
  }

  .ng-option-marked,
  .ng-option-selected {
    background-color: var(--kui-gray-250) !important;
  }

  .ng-input {
    input {
      font-weight: 600;
      font-size: 16px;
      margin-top: 4px;
      margin-left: 4px;
    }
  }

  &.ng-select-opened {
    .ng-arrow-wrapper {
      rotate: -90deg;
    }
  }
}
