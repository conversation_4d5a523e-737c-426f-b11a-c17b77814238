@use 'shared' as *;

:host {
  --clip-path-height: 20px;
  display: block;
  color: var(--kui-white);
  container-type: inline-size;
  width: 100%;

  .title {
    font-size: 28px;
    font-weight: 800;
    line-height: 28px;
    text-transform: uppercase;
  }

  .topic-suggestion {
    display: flex;
    flex-direction: column;
    background-color: var(--kui-red-500);
    clip-path: polygon(0 var(--clip-path-height), 101% 0, 100% 100%, 0 100%);
    padding: calc(32px + #{var(--clip-path-height)}) 32px 32px;
    gap: 16px;

    @include container-breakpoint-down(xs) {
      --clip-path-height: 10px;
      padding: calc(32px + #{var(--clip-path-height)}) 16px 32px;
    }

    &-content {
      p {
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;

        strong {
          font-weight: 700;
        }
      }
    }

    app-simple-button {
      margin: 0;
    }
  }
}
