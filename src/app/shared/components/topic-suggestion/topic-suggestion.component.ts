import { ChangeDetectionStrategy, Component } from '@angular/core';
import { BorsSimpleButtonComponent } from '../simple-button/simple-button.component';

@Component({
  selector: 'app-topic-suggestion',
  templateUrl: './topic-suggestion.component.html',
  styleUrl: './topic-suggestion.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [BorsSimpleButtonComponent],
})
export class TopicSuggestionComponent {
  submitTopic(): void {
    window.location.href = 'mailto:<EMAIL>';
  }
}
