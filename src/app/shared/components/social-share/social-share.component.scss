@use 'shared' as *;

:host {
  display: flex;
  gap: 16px;

  .social-link {
    cursor: pointer;
    transition: scale 300ms;

    &:hover {
      scale: 1.1;
    }
  }
}

.copy-container {
  position: fixed;
  top: 250px;
  display: none;
  left: -300px;
  width: 300px;
  height: auto;
  padding: 15px;
  background-color: var(--kui-red-500);
  color: var(--kui-white);
  border-radius: 5px;
  text-align: center;
  z-index: 10001;

  @include media-breakpoint-down(md) {
    top: 200px;
  }

  &-message {
    font-size: 14px;
  }

  &.slided {
    display: block;
    animation: slide 3s forwards;
    font-style: normal;
    font-weight: 700;
    line-height: 130%;
  }
}

@keyframes slide {
  25% {
    left: -5px;
  }
  50% {
    left: -5px;
  }
  75% {
    left: -5px;
  }
  100% {
    left: -300px;
  }
}
