import { ChangeDetectionStrategy, Component, inject, Input } from '@angular/core';
import { CopyToClipboardDirective, getFacebookShareUrl, getTwitterShareUrl, IconComponent } from '@trendency/kesma-ui';
import { SeoService } from '@trendency/kesma-core';
import { Subject, timer } from 'rxjs';
import { map, startWith, switchMap } from 'rxjs/operators';
import { AsyncPipe } from '@angular/common';

type SocialPlatform = 'facebook' | 'twitter';

@Component({
  selector: 'app-social-share',
  templateUrl: './social-share.component.html',
  styleUrls: ['./social-share.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [AsyncPipe, CopyToClipboardDirective, IconComponent],
})
export class SocialShareComponent {
  private readonly seoService = inject(SeoService);

  @Input() emailSubject: string;
  @Input() isAnchorLink = false;
  @Input() linkToCopy?: string;

  private readonly urlCopiedSubject$: Subject<void> = new Subject<void>();

  urlCopied$ = this.urlCopiedSubject$.pipe(
    switchMap(() => {
      return timer(3000).pipe(
        map(() => false),
        startWith(true)
      );
    })
  );

  getShareUrl(platform?: SocialPlatform): string {
    const articleUrl = this.linkToCopy || this.seoService.currentUrl;

    if (platform === 'facebook') return getFacebookShareUrl(articleUrl);
    if (platform === 'twitter') return getTwitterShareUrl(articleUrl);

    return articleUrl;
  }

  showCopyConfirmation(): void {
    this.urlCopiedSubject$.next();
  }
}
