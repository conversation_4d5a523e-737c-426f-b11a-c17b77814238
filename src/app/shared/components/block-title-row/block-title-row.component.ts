import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';
import { RouterLink } from '@angular/router';
import { NgIf, NgTemplateOutlet } from '@angular/common';
import { PageTitleComponent } from '../page-title/page-title.component';

@Component({
  selector: 'app-block-title-row',
  templateUrl: './block-title-row.component.html',
  styleUrls: ['./block-title-row.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, RouterLink, PageTitleComponent, NgTemplateOutlet],
})
export class BlockTitleRowComponent implements OnInit {
  @Input() data: {
    text: string;
    url?: string;
  };

  isExternal: boolean;

  ngOnInit(): void {
    this.isExternal = !!this.data?.url?.match(/^http|\/\//);
  }
}
