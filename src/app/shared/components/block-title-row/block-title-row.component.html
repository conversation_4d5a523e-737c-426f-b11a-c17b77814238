<div class="heading-line" *ngIf="!data?.url">
  <ng-container *ngTemplateOutlet="heading"></ng-container>
</div>
<ng-container *ngIf="data && data?.url">
  <a class="heading-line" *ngIf="!isExternal" [routerLink]="data?.url">
    <ng-container *ngTemplateOutlet="heading"></ng-container>
  </a>
  <a class="heading-line" *ngIf="isExternal" [href]="data?.url" target="_blank">
    <ng-container *ngTemplateOutlet="heading"></ng-container>
  </a>
</ng-container>

<ng-template #heading>
  <app-page-title [title]="data?.text" [headingLevel]="2" />
</ng-template>
