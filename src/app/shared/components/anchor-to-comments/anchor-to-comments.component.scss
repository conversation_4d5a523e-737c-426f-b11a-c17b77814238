@use 'shared' as *;

:host {
  border-top: 1px solid var(--kui-gray-200);
  border-bottom: 1px solid var(--kui-gray-200);
  margin: 32px 0;
  padding: 10px 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;

  @include container-breakpoint-down(sm) {
    flex-direction: column;
    margin: 16px 0;
  }

  .left-side {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    color: var(--kui-black-950);
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: 0.08px;
  }

  .comment-icon {
    margin-right: 16px;
    flex-shrink: 0;
  }

  .comment-text {
    font-weight: 700;
    text-decoration-line: underline;
    text-decoration-style: solid;
    text-decoration-skip-ink: none;
    text-decoration-thickness: auto;
    text-underline-offset: auto;
    text-underline-position: from-font;
  }

  .divider {
    width: 1px;
    height: 28px;
    background-color: var(--kui-gray-200);

    @include container-breakpoint-down(sm) {
      display: none;
    }
  }

  .comment-button {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 40px;
    padding: 0px 32px;
    border-radius: 20px;
    background-color: var(--kui-black-950);
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 20px;
    color: var(--kui-white);
  }
}
