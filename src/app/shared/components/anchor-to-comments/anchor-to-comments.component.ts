import { ChangeDetectionStrategy, Component } from '@angular/core';
import { RouterLink } from '@angular/router';
import { IconComponent } from '@trendency/kesma-ui';

@Component({
  selector: 'app-anchor-to-comments',
  imports: [IconComponent, RouterLink],
  templateUrl: './anchor-to-comments.component.html',
  styleUrl: './anchor-to-comments.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AnchorToCommentsComponent {}
