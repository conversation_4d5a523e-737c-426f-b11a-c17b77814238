@use 'shared' as *;

:host {
  display: block;

  .adult-btn {
    font-size: 14px;
    line-height: 18px;
    font-weight: 500;
    width: 200px;
    background-color: var(--kui-red-500);
    color: var(--kui-white);
    padding: 10px 12px;
    border-radius: 4px;

    @include media-breakpoint-down(sm) {
      font-size: 12px;
      width: 150px;
      padding: 6px;
    }
  }

  .slider-wrapper {
    position: relative;
    cursor: pointer;

    .hover-overlay {
      z-index: 2;
      display: none;
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      background: rgba(19, 20, 24, 0.25);
      align-items: center;
      justify-content: center;

      &-icon-wrapper {
        width: 56px;
        height: 56px;
        border-radius: 50%;
        background: var(--neutrals-80070, rgba(50, 53, 61, 0.7));
        backdrop-filter: blur(6px);
        display: flex;
        align-items: center;
        justify-content: center;

        .icon {
          width: 28px;
          height: 28px;
        }
      }
    }

    &:hover {
      .hover-overlay {
        display: flex;
      }
    }
  }

  .gallery-image {
    aspect-ratio: 3/2;
    object-fit: cover;
    width: 100%;
    cursor: pointer;
    object-position: top;
    max-height: 478px;
  }

  .gallery-details {
    margin-top: 16px;

    .title {
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;

      @include media-breakpoint-down(sm) {
        line-height: 16px;
        margin-bottom: 4px;
      }
    }

    .photographer {
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;

      @include media-breakpoint-down(sm) {
        line-height: 14px;
      }
    }

    .flex-wrapper {
      border-top: 1px solid var(--kui-gray-200);
      border-bottom: 1px solid var(--kui-gray-200);
      margin-top: 16px;
      padding: 8px 0;
      display: flex;
      gap: 20px;
      justify-content: space-between;
      align-items: center;

      .left {
        display: flex;
        gap: 8px;
      }

      .page {
        color: var(--kui-red-500);
        font-size: 12px;
        font-weight: 700;
        line-height: 18px;
      }

      .description {
        font-size: 12px;
        font-weight: 400;
        line-height: 18px;

        @include media-breakpoint-down(sm) {
          line-height: 16px;
        }
      }

      .pager {
        display: flex;
        gap: 12px;

        .slider-prev,
        .slider-next {
          height: 32px;
          width: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;

          &:hover {
            &.slider-prev {
              .icon {
                background-image: url('/assets/icons/chevron-left.svg');
              }
            }

            &.slider-next {
              .icon {
                background-image: url('/assets/icons/chevron-right.svg');
              }
            }
          }

          .icon {
            width: 40px;
            height: 40px;
          }
        }
      }
    }
  }
}
