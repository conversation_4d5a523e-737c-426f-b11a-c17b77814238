import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { createTitlePagination } from '../../utils';

@Component({
  selector: 'app-page-title',
  templateUrl: './page-title.component.html',
  styleUrl: './page-title.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PageTitleComponent {
  title = input.required<string>();
  headingLevel = input<number>(1);

  readonly titlePagination = createTitlePagination();
}
