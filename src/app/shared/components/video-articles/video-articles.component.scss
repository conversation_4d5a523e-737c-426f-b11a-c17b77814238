@use 'shared' as *;

:host {
  display: block;
  width: 100%;
  container-type: inline-size;
  background-color: var(--kui-black-950);
  @include media-breakpoint-down(md) {
    margin: 0 -16px;
    width: calc(100% + 32px);
  }
  .wrapper {
    display: flex;
    flex-direction: column;
    padding: 32px;
    gap: 25px;
    @include container-breakpoint-down(sm) {
      gap: 16px;
      padding: 16px;
    }
  }
  .header {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 8px;
    .title {
      font-size: 48px;
      color: var(--kui-white);
      font-weight: 800;
      line-height: 42px;
      @include container-breakpoint-down(sm) {
        font-size: 28px;
        line-height: 28px;
      }
    }
    .link {
      display: flex;
      align-items: center;
      gap: 10px;
      color: var(--kui-white);
      line-height: 20px;
      font-weight: 700;
      &:hover {
        color: var(--kui-white-o90);
        kesma-icon {
          fill: var(--kui-white-o90);
        }
      }
    }
  }
  kesma-icon {
    fill: var(--kui-white);
    flex-shrink: 0;
  }
  .articles {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 32px;
    .article-card {
      margin-bottom: 0;
      --theme-color: var(--kui-white);
      ::ng-deep {
        .article {
          gap: 8px;
        }
        .article-data {
          margin: 0;
        }
      }
    }
    @include container-breakpoint-down(sm) {
      gap: 16px;
    }
    @include container-breakpoint-down(xs) {
      grid-template-columns: 1fr 1fr;
    }
    @container (max-width: 260px) {
      grid-template-columns: 1fr;
    }
  }
}
