import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ArticleCard, BaseComponent, IconComponent } from '@trendency/kesma-ui';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { ArticleCardType } from '../../definitions';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-video-articles',
  templateUrl: './video-articles.component.html',
  styleUrl: './video-articles.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ArticleCardComponent, IconComponent, RouterLink],
})
export class VideoArticlesComponent extends BaseComponent<ArticleCard[]> {
  readonly ArticleCardType = ArticleCardType;
}
