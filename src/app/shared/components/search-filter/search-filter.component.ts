import { ChangeDetectionStrategy, Component, effect, EventEmitter, inject, Output, signal } from '@angular/core';
import { SearchSelectComponent } from '../search-select/search-select.component';
import { toSignal } from '@angular/core/rxjs-interop';
import { map } from 'rxjs/operators';
import { authorFilters, columnFilters, contentTypeFilters, publishDateFilters } from '../../utils';
import { ActivatedRoute, Router } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgClass } from '@angular/common';
import { LabelValue } from '../../definitions';
import { BackendAuthorData, IconComponent, PrimaryColumn } from '@trendency/kesma-ui';
import { AuthorsService } from 'src/app/feature/authors/auhtors.service';
import { CategoryService } from 'src/app/feature/category/category.service';

@Component({
  selector: 'app-search-filter',
  imports: [SearchSelectComponent, ReactiveFormsModule, FormsModule, NgClass, IconComponent],
  templateUrl: './search-filter.component.html',
  styleUrl: './search-filter.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SearchFilterComponent {
  @Output() filterEvent = new EventEmitter<Record<string, string>>();

  private readonly authorService = inject(AuthorsService);
  private readonly categoryService = inject(CategoryService);
  private readonly router = inject(Router);
  private readonly route = inject(ActivatedRoute);

  readonly publishDateFilters = publishDateFilters;
  readonly contentTypeFilters = contentTypeFilters;

  readonly authorsSource = toSignal(
    this.authorService.getAuthors(0).pipe(
      map(({ data }) => {
        return [authorFilters[0], ...data];
      })
    ),
    { initialValue: [] }
  );
  readonly categorySource = toSignal(
    this.categoryService.getParentColumns().pipe(
      map(({ data }) => {
        return [columnFilters[0], ...data];
      })
    ),
    { initialValue: [] }
  );

  readonly globalFilter = signal('');
  readonly selectedPublishDate = signal<LabelValue<string> | undefined>(publishDateFilters[0]);
  readonly selectedContentType = signal<LabelValue<string> | undefined>(contentTypeFilters[0]);
  readonly selectedColumnSlug = signal<PrimaryColumn | undefined>(undefined);
  readonly selectedAuthorSlug = signal<Partial<BackendAuthorData> | undefined>(undefined);

  readonly isTagFilter = signal(false);
  readonly queryParams = toSignal(this.route.queryParams, { initialValue: {} });
  searchFilter: Record<string, string> = {
    global_filter: '',
    from_date: '',
    'columnSlugs[]': '',
    'content_types[]': '',
    author: '',
    'tagSlugs[]': '',
  };

  toggleTagFilter(): void {
    this.isTagFilter.set(!this.isTagFilter());
  }

  onSubmit(): void {
    this.setRouteParams();
  }

  constructor() {
    effect(() => {
      // We need to set default value AFTER source changes, otherwise, dropdown list will not mark the selected item
      if (!this.selectedAuthorSlug() && this.authorsSource().length > 0) {
        this.selectedAuthorSlug.set(this.authorsSource()[0]);
      }

      if (!this.selectedColumnSlug() && this.categorySource().length > 0) {
        this.selectedColumnSlug.set(this.categorySource()[0]);
      }
    });

    effect(() => {
      this.setInitialFilterValues();
    });
  }

  setInitialFilterValues(): void {
    Object.entries(this.queryParams()).forEach(([paramKey, paramValue]) => {
      switch (paramKey) {
        case 'global_filter':
          this.globalFilter.update(() => paramValue);
          break;
        case 'from_date':
          this.selectedPublishDate.update(() => publishDateFilters.find((filter) => filter.value === paramValue));
          this.searchFilter['from_date'] = paramValue;
          break;
        case 'content_types[]':
          this.selectedContentType.update(() => contentTypeFilters.find((filter) => filter.value === paramValue));
          this.searchFilter['content_types[]'] = paramValue;
          break;
        case 'columnSlugs[]':
          this.selectedColumnSlug.update(() => this.categorySource().find((filter) => filter.slug === paramValue));
          this.searchFilter['columnSlugs[]'] = paramValue;
          break;
        case 'author':
          this.selectedAuthorSlug.update(() => this.authorsSource().find((filter) => filter.public_author_name === paramValue));
          this.searchFilter['author'] = paramValue;
          break;
        case 'tagSlugs[]':
          this.isTagFilter.update(() => true);
          this.globalFilter.update(() => paramValue);
          break;
      }
    });
  }

  onClear(): void {
    this.globalFilter.update(() => '');
  }

  private setRouteParams(): void {
    this.searchFilter['global_filter'] = this.globalFilter();
    this.searchFilter['author'] = this.createAuthorParamFromSelect(this.searchFilter['author']);
    const queryParams = this.searchFilter;
    queryParams['tagSlugs[]'] = this.isTagFilter() ? this.globalFilter() : '';
    queryParams['global_filter'] = this.isTagFilter() ? '' : this.globalFilter();
    // Remove empty props
    Object.entries(queryParams).forEach(([key, value]) => {
      if (!value) delete queryParams[key];
    });

    // Remove page prop
    Object.entries(queryParams).forEach(([key]) => {
      if (key === 'page') delete queryParams[key];
    });

    this.router.navigate(['/kereses'], {
      relativeTo: this.route,
      queryParams,
    });
  }

  private createAuthorParamFromSelect(filterValue?: string): string {
    if (!filterValue || filterValue === '') return '';
    const author = this.authorsSource().find((author) => author.slug === filterValue || author.public_author_name === filterValue);
    return author?.public_author_name ?? '';
  }
}
