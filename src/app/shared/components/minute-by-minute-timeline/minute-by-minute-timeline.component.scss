@use 'shared' as *;

:host {
  display: flex;
  flex-direction: column;
  container-type: inline-size;
  gap: 24px;
  width: 100%;
  @include media-breakpoint-down(md) {
    gap: 16px;
  }
  .title {
    font-size: 24px;
    line-height: 30px;
    color: var(--kui-red-500);
    @include media-breakpoint-down(md) {
      font-size: 20px;
      line-height: 24px;
    }
  }
  .mbm {
    &-date {
      background-color: var(--kui-red-500);
      font-size: 14px;
      color: var(--kui-white);
      font-weight: 600;
      line-height: 20px;
      height: 24px;
      padding: 2px 7px;
      width: max-content;
      margin-bottom: 8px;
    }
    &-link {
      height: 100%;
      font-size: 16px;
      font-weight: 500;
      line-height: 20px;
      overflow-wrap: anywhere;
      border-right: 1px solid var(--kui-gray-200);
      padding-right: 24px;
      display: block;
      &:hover {
        color: var(--kui-red-500);
      }
      @include media-breakpoint-down(md) {
        padding-right: 16px;
        font-size: 14px;
        line-height: 18px;
      }
    }
  }
  .navigation {
    color: var(--kui-black-950);
    width: 40px;
    height: 40px;
    &.prev {
      rotate: -180deg;
    }
  }

  //kesma-swipe
  ::ng-deep .kesma-swipe {
    .bottom-navigation {
      position: absolute;
      top: -66px;
      right: 0;
      @include media-breakpoint-down(md) {
        top: -48px;
      }
    }
    @include container-breakpoint-up(lg) {
      &.hidden-on-desktop .bottom-navigation {
        display: none;
      }
    }
    .item {
      align-self: stretch;
    }
  }
}
