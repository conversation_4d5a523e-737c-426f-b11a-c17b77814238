import { ChangeDetectionStrategy, Component } from '@angular/core';
import { BaseComponent, IconComponent, KesmaSwipeComponent, minuteByMinuteTimePipe, MinuteToMinuteBlock, SwipeBreakpoints } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-minute-by-minute-timeline',
  templateUrl: './minute-by-minute-timeline.component.html',
  styleUrl: './minute-by-minute-timeline.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [KesmaSwipeComponent, RouterLink, minuteByMinuteTimePipe, IconComponent],
})
export class MinuteByMinuteTimelineComponent extends BaseComponent<MinuteToMinuteBlock[]> {
  readonly breakpoints: SwipeBreakpoints = {
    default: {
      itemCount: 2,
      gap: '16px',
    },
    600: {
      itemCount: 4,
      gap: '32px',
    },
    992: {
      itemCount: 6,
    },
  };
}
