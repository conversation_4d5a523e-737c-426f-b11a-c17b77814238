import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { DisableScrollDirective } from '../../directives';
import { NgClass } from '@angular/common';

@Component({
  selector: 'app-popup',
  imports: [DisableScrollDirective, NgClass],
  templateUrl: './popup.component.html',
  styleUrl: './popup.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PopupComponent {
  @Input() hasBackdrop = true;
}
