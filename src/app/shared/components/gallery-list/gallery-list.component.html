<div class="gallery-list" [ngClass]="{ small: componentWidth() < 6 }">
  <div class="gallery-list-title" [ngClass]="{ small: componentWidth() < 6 }">GALÉRIA</div>
  @if (data; as gallery) {
    <div class="gallery-card">
      <div class="gallery-card-image-wrapper">
        <div
          kesma-swipe
          class="gallery-card-image"
          [data]="gallery?.original?.images"
          [breakpoints]="{
            default: {
              itemCount: 1,
            },
          }"
          [itemTemplate]="itemTemplate"
          [ngClass]="{ small: componentWidth() < 6, adult: gallery?.isAdult && !isUserAdult() }"
          [routerLink]="['/galeria', gallery?.slug, activeIndex() + 1]"
        ></div>
      </div>
      <div class="gallery-card-navigation" [ngClass]="{ small: componentWidth() < 6 }">
        <kesma-icon (click)="slidePrev()" class="navigation prev" name="arrow-right" [size]="30" />
        <div class="gallery-card-pager">{{ activeIndex() + 1 }} / {{ gallery?.original?.images?.length }}</div>
        <kesma-icon (click)="slideNext()" class="navigation" name="arrow-right" [size]="30" />
      </div>
      <div class="gallery-card-description" [ngClass]="{ small: componentWidth() < 6 }">{{ gallery.description }}</div>
      @if (gallery?.tags?.[0]?.slug) {
        <a [routerLink]="['/','cimke', `${gallery.tags?.[0].slug}`]" class="gallery-card-tag">{{ gallery.tags?.[0].title }}</a>
      }
    </div>
  }
</div>

<ng-template #itemTemplate let-data="data">
  <img [src]="data?.thumbnailUrl" [alt]="data?.altText ?? ''" loading="lazy" />
  <div class="gallery-card-badge">
    <kesma-icon [name]="'gallery'" [size]="16"></kesma-icon>
    GALÉRIA
  </div>
</ng-template>
