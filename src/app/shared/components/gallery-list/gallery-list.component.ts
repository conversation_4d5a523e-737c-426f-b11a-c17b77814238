import { AfterViewInit, ChangeDetectionStrategy, Component, computed, inject, input, OnInit, signal, viewChild } from '@angular/core';
import { BaseComponent, IconComponent, KesmaSwipeComponent } from '@trendency/kesma-ui';
import { GalleryCardWithOriginal } from '../../definitions';
import { RouterLink } from '@angular/router';
import { NgClass } from '@angular/common';
import { StorageService } from '@trendency/kesma-core';
import { ADULT_CHOICE_STORAGE_KEY } from '../../constants';

@Component({
  selector: 'app-gallery-list',
  templateUrl: './gallery-list.component.html',
  styleUrl: './gallery-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [IconComponent, RouterLink, NgClass, KesmaSwipeComponent],
})
export class GalleryListComponent extends BaseComponent<GalleryCardWithOriginal> implements OnInit, AfterViewInit {
  swipeComponent = viewChild(KesmaSwipeComponent);
  private readonly storageService = inject(StorageService);
  readonly isUserAdult = signal(this.storageService.getSessionStorageData(ADULT_CHOICE_STORAGE_KEY) ?? false);
  startImageIndex = 0;

  isSidebar = input(false);
  desktopWidth = input(12);
  componentWidth = computed(() => (this.isSidebar() ? 1 : this.desktopWidth()));
  activeIndex = computed(() => this.swipeComponent()?.currentIndex() ?? 0);

  ngAfterViewInit(): void {
    if (this.swipeComponent()?.currentIndex() !== this.startImageIndex) {
      this.swipeComponent()?.swipeTo(this.startImageIndex);
    }
  }

  slideNext(): void {
    this.swipeComponent()?.swipePageForward();
  }

  slidePrev(): void {
    this.swipeComponent()?.swipePageBack();
  }
}
