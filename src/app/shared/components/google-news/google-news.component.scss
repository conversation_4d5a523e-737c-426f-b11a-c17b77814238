@use 'shared' as *;

:host {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  padding: 10px 16px;
  border-block: 1px solid var(--kui-gray-200);
  font-size: 16px;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: 0.08px;
  @include media-breakpoint-down(md) {
    flex-direction: column;
    margin-block: 16px;
    padding: 16px;
  }
  .centered {
    text-align: center;
  }
  .link {
    text-align: center;
    text-decoration: underline;
    text-decoration-color: var(--kui-black-950);
    text-underline-position: from-font;
    font-weight: 700;
    &:hover {
      color: var(--kui-red-500);
      text-decoration-color: var(--kui-red-500);
    }
  }
  .vertical-line {
    background-color: var(--kui-gray-200);
    height: 28px;
    width: 1px;
    @include media-breakpoint-down(md) {
      display: none;
    }
  }
}
