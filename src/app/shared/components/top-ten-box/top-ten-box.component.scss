@use 'shared' as *;

:host {
  --clip-path-height: 20px;
  display: block;
  width: 100%;
  container-type: inline-size;
  .wrapper {
    padding: 32px;
    display: flex;
    flex-direction: column;
    gap: 16px;
    background: var(--kui-black-950);
    clip-path: polygon(0 #{var(--clip-path-height)}, 101% 0, 100% 100%, 0 100%);
    @container (max-width: 375px) {
      padding: 32px 16px;
    }
    @include container-breakpoint-down(sm) {
      --clip-path-height: 10px;
    }
    .title {
      color: var(--kui-white);
      font-size: 48px;
      font-weight: 800;
      line-height: 42px;
      text-transform: uppercase;
      padding-top: var(--clip-path-height);
      margin-top: 27px;
      @include container-breakpoint-down(xs) {
        font-size: 28px;
        line-height: 28px;
        margin-top: 0;
      }
    }
    .items {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-bottom: 27px;
      @include container-breakpoint-down(sm) {
        margin-bottom: 0;
      }
      .item {
        background: var(--kui-red-500);
        display: flex;
        min-height: 24px;
        padding: 4px 8px;
        align-items: center;
        color: var(--kui-white);
        font-size: 12px;
        font-weight: 600;
        line-height: normal;
        font-variant: all-small-caps;
        overflow-wrap: anywhere;
        transition: background 0.3s;
        &:hover {
          background: var(--kui-red-550);
        }
      }
    }
  }
}
