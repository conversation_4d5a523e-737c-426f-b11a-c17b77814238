import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { RouterLink } from '@angular/router';

type TopTenBoxType = {
  id: string;
  title: string;
  slug: string;
};

@Component({
  selector: 'app-top-ten-box',
  imports: [RouterLink],
  templateUrl: './top-ten-box.component.html',
  styleUrl: './top-ten-box.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TopTenBoxComponent {
  data = input.required({ transform: (data: TopTenBoxType[]) => (data?.length ? data.filter((item) => !!item) : []) });
  title = input.required<string>();
  path = input.required<string>();
}
