import { ChangeDetectionStrategy, Component } from '@angular/core';
import { BaseComponent, DossierData } from '@trendency/kesma-ui';

@Component({
  selector: 'app-dossier-sponsoration-header',
  imports: [],
  templateUrl: './dossier-sponsoration-header.component.html',
  styleUrl: './dossier-sponsoration-header.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DossierSponsorationHeaderComponent extends BaseComponent<DossierData> {
  get moreButtonLabel(): string {
    return this.data?.overwriteMoreButtonLabel?.length ? this.data?.overwriteMoreButtonLabel : 'A dosszié további hírei';
  }
}
