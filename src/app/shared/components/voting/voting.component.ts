import { AsyncPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, HostBinding, inject, input } from '@angular/core';
import { VoteData, VotingComponent as KesmaVotingComponent } from '@trendency/kesma-ui';
import { VotingType } from '../../definitions';
import { toSignal } from '@angular/core/rxjs-interop';
import { AuthService } from '../../services';
import { Router } from '@angular/router';
import { map } from 'rxjs/operators';
import { combineLatest } from 'rxjs';

type BorsVoteData = VoteData & {
  isShowOnlyLoggedUsers: boolean;
};

@Component({
  selector: 'app-voting',
  templateUrl: './voting.component.html',
  styleUrl: './voting.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [AsyncPipe],
})
export class VotingComponent extends KesmaVotingComponent<BorsVoteData> {
  readonly votingStyle = VotingType;

  readonly authService = inject(AuthService);
  readonly router = inject(Router);

  readonly styleId = input<VotingType>(VotingType.DEFAULT);
  readonly isSidebar = input(false);
  readonly desktopWidth = input(12);

  readonly componentWidth = computed(() => (this.isSidebar() ? 1 : this.desktopWidth()));

  readonly resultState = toSignal(
    combineLatest([this.vm.state$, this.authService.isAuthenticated()]).pipe(
      map(([state, isAuthenticated]) => {
        if (!state?.showResults) {
          return 'default';
        }

        if (!state?.voteData?.isResultVisible) {
          return 'thanks';
        }

        if (state?.voteData?.isShowOnlyLoggedUsers && !isAuthenticated) {
          return 'loginPrompt';
        }

        return 'result';
      })
    )
  );

  @HostBinding('class') get componentClass(): string {
    return `style-${this.styleId()}`;
  }

  @HostBinding('class.small') get isSmall(): boolean {
    return this.componentWidth() < 6;
  }

  onLogin(): void {
    this.router
      .navigate(['/', 'bejelentkezes'], {
        queryParams: { redirect: this.router.routerState.snapshot.url },
      })
      .then();
  }
}
