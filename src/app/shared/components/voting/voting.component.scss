@use 'shared' as *;

:host {
  --voting-checkmark-background: var(--kui-white);
  --voting-checkmark-border: var(--voting-checkmark-background);
  --voting-checkmark-background-checked: var(--kui-black-950);
  --voting-checkmark-border-checked: var(--voting-checkmark-background);
  --voting-accent: var(--kui-black-950);

  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 32px;
  padding: 32px;

  &.small {
    gap: 16px;
    padding: 16px;

    &.style-default {
      padding-top: 26px;
      clip-path: polygon(0 10px, 101% 0, 100% 100%, 0 100%);

      .voting {
        &-title-box-title {
          font-size: 28px;
        }
      }
    }

    &.style-article {
      .voting {
        &-title-box-title {
          font-size: 26px;
        }
      }
    }

    .voting {
      &-progress-list-label {
        font-size: 16px;
      }

      &-form {
        gap: 16px;
      }

      &-question {
        font-size: 20px;
      }

      &-progress-result-list-label {
        font-size: 16px;
      }

      &-progress-result-list-percentage {
        font-size: 14px;
      }
    }
  }

  &.style-default {
    padding-top: 52px;
    background-color: var(--kui-red-500);
    clip-path: polygon(0 20px, 101% 0, 100% 100%, 0 100%);

    .voting {
      &-prompt {
        color: var(--kui-white);
      }

      &-title-box-title {
        font-size: 48px;
        line-height: 42px;
      }
    }
  }

  &.style-article {
    --voting-checkmark-background: var(--kui-white);
    --voting-checkmark-border: var(--kui-black-200);
    --voting-checkmark-background-checked: var(--kui-gray-250);
    --voting-checkmark-border-checked: var(--kui-red-500);
    --voting-accent: var(--kui-red-500);

    border-left: 3px solid var(--kui-red-500);

    .voting {
      &-title-box-title {
        font-size: 36px;
        font-weight: 800;
        color: var(--kui-red-500);
        line-height: 42px;
      }

      &-question {
        color: var(--kui-black-950);
        line-height: 30px;
      }

      &-progress-list-item {
        min-height: 52px;
        padding: 16px;
        border-top: 1px solid var(--kui-gray-250);

        &:last-child {
          border-bottom: 1px solid var(--kui-gray-250);
        }

        &:has(input:checked) {
          background-color: var(--kui-gray-250);
        }
      }

      &-progress-list-label {
        color: var(--kui-black-950);
        font-size: 18px;

        @include media-breakpoint-down(md) {
          font-size: 16px;
          line-height: 24px;
        }
      }

      &-progress-list-text {
        line-height: 26px;

        @include media-breakpoint-down(md) {
          line-height: 24px;
        }
      }

      &-progress-result-list-label {
        color: var(--kui-black-950);
        font-size: 18px;
        line-height: 26px;
        font-weight: 400;
      }

      &-progress-result-list-percentage {
        line-height: 24px;
        color: var(--kui-black-600);
      }

      &-button {
        color: var(--kui-white);
        background-color: var(--kui-red-500);
        font-size: 16px;
        font-weight: 700;
        line-height: 20px;
      }

      &-progress-list-radio-button {
        &:checked ~ .checkmark {
          border: 5px solid var(--kui-red-500);
          background-color: var(--kui-red-500);
        }
      }
    }
  }

  .checkmark {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
    width: 20px;
    height: 20px;
    background-color: var(--voting-checkmark-background);
    border: 2px solid var(--voting-checkmark-border);
    border-radius: 50%;

    &::after {
      content: '';
      position: absolute;
      display: none;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 10px;
      height: 10px;
      background-color: var(--voting-checkmark-background);
      border-radius: 50%;
    }
  }

  .voting {
    &-progress-list {
      margin: 0;
    }

    &-progress-list-item {
      display: block;
      margin-bottom: 0;
    }

    &-progress-list-label {
      display: flex;
      font-family: var(--kui-font-primary);
      color: var(--kui-white);
      font-style: normal;
      font-weight: 400;
      font-size: 18px;
      line-height: 26px;

      &:has(input:checked) {
        font-weight: 700;
      }
    }

    &-progress-list-radio-button {
      position: absolute;
      opacity: 0;
      height: 0;
      width: 0;
      margin-right: 10px;

      &:checked ~ .checkmark {
        border: 5px solid var(--voting-checkmark-background-checked);
        background-color: var(--voting-checkmark-background-checked);

        &::after {
          display: block;
        }
      }
    }

    &-progress-list-text {
      user-select: none;
      position: relative;
      padding-left: 36px;
      cursor: pointer;
    }

    &-form {
      display: flex;
      flex-direction: column;
      gap: 32px;
    }

    &-question {
      font-style: normal;
      font-weight: 700;
      font-size: 24px;
      color: var(--kui-white);
      line-height: 30px;
    }

    &-title-box {
      margin: 0;
      padding: 0;
    }

    &-title-box-title {
      font-size: 48px;
      font-weight: 800;
      color: var(--kui-white);
      text-align: start;
    }

    &-progress-result-list-item {
      display: flex;
      flex-direction: column;

      &:not(:last-child) {
        margin-bottom: 16px;
      }
    }

    &-progress-result-list-label {
      display: flex;
      justify-content: space-between;
      align-items: baseline;
      font-family: var(--kui-font-primary);
      color: var(--kui-white);
      font-style: normal;
      font-weight: 300;
      font-size: 18px;
    }

    &-progress-result-list-percentage {
      font-size: 16px;
      font-weight: 400;
      margin-inline: 5px;
    }

    &-line {
      height: 4px;
      width: 100%;
      border-radius: 2px;
      margin-block: 10px;
      background-color: var(--kui-gray-400);
    }

    &-line-inner {
      display: block;
      height: 4px;
      border-radius: 2px;
      background-color: var(--voting-accent);
    }

    &-separator {
      height: 1px;
      border: none;
      background: var(--kui-gray-200);
      mix-blend-mode: multiply;
      margin-block: 16px;
    }

    &-button {
      display: block;
      width: max-content;
      height: 40px;
      margin-block: auto;
      padding-inline: 16px;
      background-color: var(--kui-black-950);
      color: var(--kui-white);
      font-weight: 700;
      font-size: 16px;
      line-height: 20px;

      &:hover {
        background-color: var(--kui-black-1100);
      }
    }

    &-recaptcha {
      display: flex;
      justify-content: center;
      margin: 0 auto;
    }
  }

  @include media-breakpoint-down(md) {
    gap: 16px;
    padding: 16px;

    &.style-default {
      padding-top: 26px;
      clip-path: polygon(0 10px, 101% 0, 100% 100%, 0 100%);

      .voting {
        &-title-box-title {
          font-size: 28px;
        }
      }
    }

    &.style-article {
      .voting {
        &-title-box-title {
          font-size: 26px;
          line-height: 28px;
        }
      }
    }

    .voting {
      &-progress-list-label {
        font-size: 16px;
        line-height: 24px;
      }

      &-form {
        gap: 16px;
      }

      &-question {
        font-size: 20px;
        line-height: 24px;
      }

      &-progress-result-list-label {
        font-size: 16px;
        line-height: 24px;
      }

      &-progress-result-list-percentage {
        font-size: 14px;
        line-height: 20px;
      }
    }
  }
}
