@if (vm.state$ | async; as state) {
  <div class="voting-title-box">
    <h2 class="voting-title-box-title">{{ state.showResults ? 'Köszönjük válaszát.' : 'SZAVAZÁS' }}</h2>
  </div>

  <div class="voting-form">
    <h3 class="voting-question">{{ data?.question || data?.title }}</h3>
    @if (resultState() === 'loginPrompt') {
      <p class="voting-prompt">
        A szavazás eredménye csak bejelentkezett felhasználók számára érhető el. <br />
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jelentkezz be a megtekintéshez!
      </p>
      <button class="voting-button" (click)="onLogin()">
        <span class="voting-button-text"> Bejelentkezem </span>
      </button>
    } @else if (resultState() === 'thanks') {
      <p class="voting-prompt"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a vá<PERSON>zá<PERSON>!</p>
    } @else {
      <ul class="voting-progress-list">
        @for (item of data?.answers; track item.id) {
          @if (resultState() === 'default') {
            <li class="voting-progress-list-item">
              <div class="voting-progress-list-label">
                <label class="voting-progress-list-text" [for]="item.id">
                  <input
                    [id]="item.id"
                    [name]="'answers-' + data?.id"
                    class="voting-progress-list-radio-button"
                    type="radio"
                    [value]="item.id"
                    [disabled]="state.showResults"
                    [checked]="voteId === item.id"
                    (click)="setVoteId(item.id)"
                  />
                  <span class="checkmark"></span>
                  {{ item.answer }}
                </label>
              </div>
              @if (!$last && styleId() === votingStyle.DEFAULT) {
                <hr class="voting-separator" />
              }
            </li>
          } @else if (resultState() === 'result') {
            <li class="voting-progress-result-list-item">
              <div class="voting-progress-result-list-label">
                <p>{{ item.answer }}</p>
                <div class="voting-progress-result-list-percentage">{{ item?.votePercentage }}%</div>
              </div>
              <div class="voting-line">
                <span class="voting-line-inner" [style.width]="item.votePercentage + '%'"></span>
              </div>
            </li>
          }
        }
      </ul>
    }
  </div>
  @if (resultState() === 'default') {
    <button class="voting-button" [disabled]="!voteId" (click)="onVote()">
      <span class="voting-button-text"> Szavazok </span>
    </button>
  }
}
