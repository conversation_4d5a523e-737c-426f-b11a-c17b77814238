import { ChangeDetectionStrategy, Component, HostBinding, input } from '@angular/core';
import { BaseComponent, DossierData, IconComponent, buildArticleUrl } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { PlaceholderImg } from '../../constants';
import { FormatPipeModule } from 'ngx-date-fns';
import { ArticleCardType, DossierCardType } from '../../definitions';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { DossierSponsorationHeaderComponent } from '../dossier-sponsoration-header/dossier-sponsoration-header.component';

@Component({
  selector: 'app-dossier-card',
  templateUrl: './dossier-card.component.html',
  styleUrl: './dossier-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [IconComponent, RouterLink, FormatPipeModule, ArticleCardComponent, DossierSponsorationHeaderComponent],
})
export class DossierCardComponent extends BaseComponent<DossierData> {
  readonly PlaceholderImg = PlaceholderImg;
  readonly buildArticleUrl = buildArticleUrl;
  sponsoredData = input<DossierData>();

  get moreButtonLabel(): string {
    return this.data?.overwriteMoreButtonLabel?.length ? this.data?.overwriteMoreButtonLabel : 'További cikkek a témában';
  }

  @HostBinding('class') get hostClass(): string {
    return `style-${DossierCardType[this.styleId()]}`;
  }

  styleId = input.required<DossierCardType>();

  readonly DossierCardType = DossierCardType;
  readonly ArticleCardType = ArticleCardType;
}
