@use 'shared' as *;

:host {
  display: block;
  color: var(--kui-black-950);
  container-type: inline-size;
  width: 100%;
  .dossier {
    display: flex;
    flex-direction: column;
    gap: 32px;
    @include container-breakpoint-down(xs) {
      gap: 8px;
    }
    &-thumbnail {
      --clip-path-height: 30px;
      object-fit: cover;
      aspect-ratio: 3/2;
      clip-path: polygon(0 0, 100% 0, 101% 50%, 100% calc(100% - #{var(--clip-path-height)}), 0 100%, 0 100%);
      width: 100%;
      &-box {
        display: block;
        width: 100%;
      }
      &.is-placeholder {
        object-fit: contain;
        background-color: var(--kui-red-500);
      }
      @include container-breakpoint-down(xs) {
        --clip-path-height: 10px;
      }
    }
    &-card {
      display: flex;
      gap: 32px;
      @include container-breakpoint-down(xs) {
        flex-direction: column;
        gap: 16px;
      }
    }
    &-title {
      font-size: 30px;
      color: var(--kui-black-950);
      font-weight: 800;
      overflow-wrap: anywhere;
      line-height: 38px;
      &:hover {
        color: var(--kui-red-500);
      }
      @include container-breakpoint-down(xs) {
        font-size: 24px;
        line-height: 28px;
      }
    }
    &-details {
      width: 100%;
      max-width: 417px;
    }
  }
  .article {
    &-link {
      display: flex;
      gap: 16px;
      align-items: flex-start;
      color: var(--kui-black-950);
      &:hover {
        color: var(--kui-red-500);
      }
      .plus {
        fill: var(--kui-red-500);
        margin-top: 4px;
        flex-shrink: 0;
      }
    }
    &-title {
      font-size: 18px;
      font-weight: 700;
      line-height: 24px;
      overflow-wrap: anywhere;
      @include media-breakpoint-down(sm) {
        font-size: 14px;
        line-height: 16px;
      }
    }
  }
  .dossier-link {
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    text-transform: uppercase;
    &:hover {
      color: var(--kui-red-500);
    }
  }
  .divider {
    height: 1px;
    border-bottom: 1px solid var(--kui-gray-200);
    width: 100%;
    margin-block: 16px;
    &:last-of-type {
      margin-bottom: 0;
    }
  }

  &.style-Layout {
    .dossier {
      &-icon {
        fill: var(--kui-red-500);
        flex-shrink: 0;
        @include container-breakpoint-down(xs) {
          width: 24px;
          height: 16px;
        }
      }
      &-header {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        justify-content: space-between;
        padding-block: 8px;
        align-items: center;
        border-block: 1px solid var(--kui-gray-200);
        @include container-breakpoint-down(xs) {
          max-height: unset;
          gap: 8px;
        }
        &-title {
          display: flex;
          align-items: center;
          color: var(--kui-red-500);
          gap: 16px;
          font-size: 36px;
          font-weight: 800;
          line-height: 42px;
          @include container-breakpoint-down(xs) {
            font-size: 26px;
            line-height: 28px;
            gap: 8px;
          }
        }
      }
    }
  }

  &.style-Article {
    .dossier {
      gap: 0;
      &-icon {
        fill: var(--kui-black-950);
        flex-shrink: 0;
      }
      &-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid var(--kui-gray-200);
        gap: 8px;
        &-title {
          display: flex;
          align-items: center;
          font-size: 24px;
          line-height: 42px;
          gap: 8px;
        }
        &-link {
          font-size: 16px;
          font-weight: 700;
          line-height: 20px;
          display: flex;
          gap: 10px;
          align-items: center;
          &:hover {
            color: var(--kui-red-500);
          }
        }
      }
    }
    .arrow-right {
      flex-shrink: 0;
      fill: var(--kui-red-500);
    }
  }
}

.sponsored {
  margin-top: -30px;
}
