@use 'shared' as *;

:host {
  display: flex;
  padding: 16px;
  gap: 16px;
  align-items: center;
  background-color: var(--kui-gray-250);
  margin-bottom: 32px;

  @include media-breakpoint-down(sm) {
    flex-direction: column;
    margin-bottom: 16px;
  }
}

kesma-icon {
  min-height: 24px;
  min-width: 24px;
}

.info-box {
  &-description {
    font-size: 18px;
    font-style: italic;
    font-weight: 400;
    line-height: 26px;

    @include media-breakpoint-down(sm) {
      font-size: 16px;
      line-height: 24px;
    }
  }
}
