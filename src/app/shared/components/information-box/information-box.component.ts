import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';
import { ArticleBodyDetails, ArticleBodyDetailType, IconComponent } from '@trendency/kesma-ui';

@Component({
  selector: 'app-information-box',
  imports: [IconComponent],
  templateUrl: './information-box.component.html',
  styleUrl: './information-box.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InformationBoxComponent {
  informationBoxDetails = input.required<ArticleBodyDetails[]>();

  informationBoxDescription = computed(
    () => this.informationBoxDetails()?.find((articleBodyElem) => articleBodyElem?.type === ArticleBodyDetailType.InfoBoxDescription)?.value
  );
}
