<ul class="breadcrumb">
  <li>
    <a class="breadcrumb-link homepage" routerLink="/">BORS</a>
  </li>
  @for (breadCrumbItem of data; track breadCrumbItem.label) {
    <li>
      <span class="separator">/</span>
      @if (breadCrumbItem?.url) {
        <a class="breadcrumb-link" [routerLink]="breadCrumbItem.url">{{ breadCrumbItem.label }}</a>
      } @else {
        <span>{{ breadCrumbItem.label }}</span>
      }
    </li>
  }
</ul>
