import { ChangeDetectionStrategy, Component } from '@angular/core';
import { BaseComponent, BreadcrumbItem } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-breadcrumb',
  templateUrl: './breadcrumb.component.html',
  styleUrl: './breadcrumb.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink],
})
export class BreadcrumbComponent extends BaseComponent<BreadcrumbItem[]> {}
