@use 'shared' as *;

:host {
  display: block;
  width: 100%;
  container-type: inline-size;
  .link {
    display: flex;
    flex-direction: column;
    gap: 16px;
    @include container-breakpoint-up(sm) {
      gap: 32px;
      flex-direction: row;
      align-items: flex-start;
    }
  }
  .image-wrapper,
  .data {
    flex: 1;
  }
  .title {
    font-size: 20px;
    line-height: 24px;
    @include container-breakpoint-up(sm) {
      font-size: 24px;
      line-height: 30px;
    }
  }
}
