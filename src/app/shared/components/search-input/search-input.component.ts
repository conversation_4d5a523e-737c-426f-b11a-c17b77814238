import { ChangeDetectionStrategy, Component, inject, input } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-search-input',
  templateUrl: './search-input.component.html',
  styleUrl: './search-input.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [FormsModule],
})
export class SearchInputComponent {
  private readonly router = inject(Router);
  private readonly route = inject(ActivatedRoute);

  placeholder = input.required<string>();

  globalFilter: string = this.route.snapshot.queryParamMap.get('global_filter') || '';

  onSubmit(): void {
    this.router
      .navigate([], {
        queryParams: {
          global_filter: this.globalFilter || null,
        },
        queryParamsHandling: 'merge',
      })
      .then();
  }
}
