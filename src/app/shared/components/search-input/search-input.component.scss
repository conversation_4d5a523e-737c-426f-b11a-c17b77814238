@use 'shared' as *;

:host {
  display: flex;
  color: var(--kui-black-950);
  width: 100%;
  height: 60px;
  @include media-breakpoint-down(md) {
    height: 44px;
  }
  .search {
    padding-inline: 20px;
    background-color: var(--kui-gray-250);
    width: 100%;
    font-size: 16px;
    line-height: 44px;
    font-weight: 400;

    &::placeholder {
      color: #002234;
    }
  }
  .submit {
    line-height: 28px;
    font-weight: 700;
    background-color: var(--kui-red-500);
    color: var(--kui-white);
    font-size: 16px;
    padding-inline: 20px;
    width: 98px;
    &:hover {
      background-color: var(--kui-red-600);
    }
  }
}
