@use 'shared' as *;

:host {
  --clip-path-height: 10px;

  display: block;
  width: 100%;
  color: var(--kui-white);
  container-type: inline-size;

  @include media-breakpoint-down(md) {
    margin-inline: -15px;
    width: calc(100% + 30px);
  }

  .top-commented {
    display: flex;
    flex-direction: column;
    background-color: var(--kui-red-500);
    clip-path: polygon(0 var(--clip-path-height), 101% 0, 100% 100%, 0 100%);
    padding: calc(16px + #{var(--clip-path-height)}) 16px 16px;
    gap: 16px;

    @include container-breakpoint-up(xs) {
      --clip-path-height: 20px;

      padding: calc(32px + #{var(--clip-path-height)}) 32px 32px;
      gap: 32px;
    }

    .article-row {
      display: flex;
      justify-content: space-between;
      gap: 16px;

      @include container-breakpoint-up(xs) {
        gap: 32px;
      }

      h2 {
        font-size: 28px;
        font-weight: 800;
        line-height: 28px;
      }

      a {
        font-size: 16px;
        font-weight: 700;
        line-height: 20px;
        color: var(--kui-white);
        display: flex;
        align-items: center;
        gap: 10px;

        kesma-icon {
          fill: var(--kui-white);
        }
      }

      .article-column {
        display: flex;
        flex-direction: column;
        gap: 16px;
        width: 47%;
      }
    }
  }
}
