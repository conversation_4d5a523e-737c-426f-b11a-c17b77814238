import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';
import { ArticleCard, IconComponent } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { ArticleCardType } from '../../definitions';

@Component({
  selector: 'app-top-commented-articles',
  imports: [RouterLink, IconComponent, ArticleCardComponent],
  templateUrl: './top-commented-articles.component.html',
  styleUrl: './top-commented-articles.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TopCommentedArticlesComponent {
  readonly data = input.required<ArticleCard[]>();
  readonly articleGrid = computed(() =>
    this.data().reduce<[ArticleCard[], ArticleCard[]]>(
      (acc, curr, index) => {
        acc[index % 2].push(curr);
        return acc;
      },
      [[], []]
    )
  );

  readonly featuredArticleStyle = ArticleCardType.TopImgTagTitle;
  readonly normalArticleStyle = ArticleCardType.NoImgTagTitle;
}
