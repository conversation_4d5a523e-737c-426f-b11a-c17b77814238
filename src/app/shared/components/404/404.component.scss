@use 'shared' as *;

.wrapper {
  max-width: 510px;
  display: flex;
  flex-direction: column;
  gap: 32px;
  align-items: center;
  justify-content: center;
  margin: 64px auto;
  color: var(--kui-black-950);

  @include media-breakpoint-down(md) {
    gap: 16px;
    padding: 0 16px;
    margin: 99px auto 80px auto;
  }

  @include media-breakpoint-down(sm) {
    max-width: 343px;
  }

  .not-found {
    &-main {
      font-size: 124px;
      font-weight: 700;
      letter-spacing: 1px;
      color: var(--kui-red-500);
      line-height: 1;

      @include media-breakpoint-down(md) {
        font-size: 67px;
      }
    }

    &-text {
      font-size: 36px;
      font-weight: 800;
      line-height: 42px;
      text-align: center;

      @include media-breakpoint-down(md) {
        font-size: 26px;
        line-height: 28px;
      }
    }

    &-link {
      display: flex;
      width: 200px;
      height: 40px;
      padding: 0 16px;
      justify-content: center;
      align-items: center;
      background-color: var(--kui-red-500);
      color: var(--kui-white);
      font-weight: 700;
      line-height: 20px;
    }

    &-description {
      display: flex;
      flex-direction: column;
      text-align: center;
      font-size: 18px;
      font-weight: 400;
      line-height: 26px;
      gap: 26px;
    }
  }
}
