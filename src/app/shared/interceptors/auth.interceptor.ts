import { HttpEvent, HttpHandlerFn, HttpRequest } from '@angular/common/http';
import { Observable } from 'rxjs';
import { AuthService } from '../services';
import { inject } from '@angular/core';

const SECURE_API: string = 'secureapi';

export const authInterceptor = (req: HttpRequest<unknown>, next: HttpHandlerFn): Observable<HttpEvent<unknown>> => {
  const authService: AuthService = inject(AuthService);
  const addTokenToSecureApi = (req: HttpRequest<unknown>): HttpRequest<unknown> => {
    if (req.url.includes(SECURE_API)) {
      const token: string | undefined = authService.getToken();
      return req.clone({ setHeaders: { 'X-Auth-Token': token ?? '' } });
    }
    return req;
  };
  return next(addTokenToSecureApi(req));
};
