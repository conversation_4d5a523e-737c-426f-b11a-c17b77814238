import { inject, Injectable, signal, WritableSignal } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { backendWeatherForecastToWeatherForecast, translateWeatherIcon } from '../../feature/weather/weather.utils';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import {
  ApiResult,
  BackendWeatherData,
  CityWeatherCurrent,
  WeatherCity,
  BackendWeatherForecast,
  WeatherForecast,
  WeatherDailyShort,
  WeatherDaily,
  WeatherUv,
} from '@trendency/kesma-ui';

export type BorsWeatherData = {
  text: WeatherDailyShort[];
  current: CityWeatherCurrent[];
  daily: Record<WeatherCity, WeatherDaily>;
  forecast: Record<WeatherCity, WeatherForecast[]>;
  uv: WeatherUv[];
};

@Injectable({
  providedIn: 'root',
})
export class WeatherService {
  private readonly reqService = inject(ReqService);
  selectedCity: WritableSignal<WeatherCity> = signal('Székesfehérvár');

  getWeather(): Observable<BorsWeatherData> {
    return this.reqService.get<ApiResult<BackendWeatherData>>('/mediaworks/weather').pipe(map(({ data }) => this.mapBackendWeatherToWeather(data)));
  }

  getWeatherByCity(city?: WeatherCity): Observable<CityWeatherCurrent | undefined> {
    return this.getWeather().pipe(map((weather) => weather.current.filter((item) => item.city === (city ?? this.selectedCity())).shift()));
  }

  private readonly mapBackendWeatherToWeather = (backendWeatherData: BackendWeatherData): BorsWeatherData => ({
    ...backendWeatherData,
    current: this.mapBackendCurrentWeather(backendWeatherData.current),
    forecast: this.mapBackendForecast(backendWeatherData.forecast),
  });

  private readonly mapBackendCurrentWeather = (currentWeather: CityWeatherCurrent[]): CityWeatherCurrent[] =>
    currentWeather.map((weather) => ({
      ...weather,
      icon2: translateWeatherIcon(weather.icon2),
    }));

  private readonly mapBackendForecast = (forecast: Record<WeatherCity, BackendWeatherForecast[]>): Record<WeatherCity, WeatherForecast[]> => {
    return Object.fromEntries(Object.entries(forecast).map(([city, forecasts]) => [city, forecasts.map(backendWeatherForecastToWeatherForecast)])) as Record<
      WeatherCity,
      WeatherForecast[]
    >;
  };
}
