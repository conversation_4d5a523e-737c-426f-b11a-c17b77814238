import { Injectable } from '@angular/core';
import { PortalConfigData, PortalConfigSetting } from '@trendency/kesma-ui';

@Injectable({
  providedIn: 'root',
})
export class PortalConfigService {
  private portalConfigs: PortalConfigData;

  setConfig(portalConfigs: PortalConfigData): void {
    this.portalConfigs = portalConfigs;
  }

  isConfigSet(setting: PortalConfigSetting): boolean {
    return this.portalConfigs[setting] === '1';
  }
}
