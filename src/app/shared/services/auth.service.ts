import { inject, Injectable, signal } from '@angular/core';
import { StorageService, UtilService } from '@trendency/kesma-core';
import { User } from '@trendency/kesma-ui';
import { Observable, of, throwError } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { ApiService } from './api.service';
import { SecureApiService } from './secure-api.service';
import { BackendUserLoginResponse, LoginFormData } from '../definitions';
import { environment } from '../../../environments/environment';

const TOKEN_STORAGE_KEY = 'token';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private readonly apiService = inject(ApiService);
  private readonly secureApiService = inject(SecureApiService);
  private readonly storageService = inject(StorageService);
  private readonly utilsService = inject(UtilService);

  readonly currentUser = signal<User | undefined>(undefined);

  authenticate(formData: LoginFormData, recaptchaToken: string): Observable<boolean> {
    return this.apiService.login(formData, recaptchaToken).pipe(
      switchMap((response: BackendUserLoginResponse) => {
        this.setToken(response.token);
        return this.isAuthenticated();
      }),
      catchError((error) => {
        this.clearTokens();
        return throwError(() => error);
      })
    );
  }

  invalidate(): Observable<boolean> {
    return this.secureApiService.logout().pipe(
      map(() => {
        this.clearTokens();
        return true;
      }),
      catchError(() => {
        this.clearTokens();
        return of(true);
      })
    );
  }

  isAuthenticated(): Observable<boolean> {
    if (this.currentUser()) {
      return of(true);
    } else {
      const token = this.getToken();
      if (token) {
        return this.secureApiService.getCurrentUser().pipe(
          map((user) => {
            this.currentUser.set(user);
            return true;
          }),
          catchError(() => {
            this.clearTokens();
            return of(false);
          })
        );
      } else {
        this.clearTokens();
        return of(false);
      }
    }
  }

  setCurrentUser(user: User): void {
    this.currentUser.set(user);
  }

  getToken(): string | undefined {
    if (!this.utilsService.isBrowser()) {
      return undefined;
    }
    return this.storageService.getCookie(TOKEN_STORAGE_KEY) as string | undefined;
  }

  setToken(token: string): void {
    if (!environment.siteUrl) {
      console.log('Unable to set token. Please set siteUrl in environment.');
      return;
    }
    const siteUrl = new URL(environment.siteUrl);
    this.storageService.setCookie(TOKEN_STORAGE_KEY, token, 6 * 60 * 60, '.' + siteUrl.hostname);
  }

  clearTokens(): void {
    this.storageService.removeCookie(TOKEN_STORAGE_KEY);
    this.currentUser.set(undefined);
  }
}
