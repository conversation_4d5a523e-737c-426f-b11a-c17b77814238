import { inject, Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { ApiResponseMetaList, ApiResult, BackendGalleryDetails, GalleryDetails, mapBackendGalleryDetailsResultToGalleryDetails } from '@trendency/kesma-ui';
import { map, Observable, throwError } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class GalleryService {
  private readonly reqService = inject(ReqService);

  getGalleryDetails(slug: string): Observable<GalleryDetails> {
    if (!slug) {
      return throwError(() => 'Undefined gallery slug');
    }

    return this.reqService
      .get<ApiResult<BackendGalleryDetails[], ApiResponseMetaList>>(`media/gallery/${slug}`)
      .pipe(map(mapBackendGalleryDetailsResultToGalleryDetails));
  }
}
