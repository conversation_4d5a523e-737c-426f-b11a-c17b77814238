import { Injectable, signal } from '@angular/core';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root',
})
export class HeaderService {
  private readonly DEFAULT_COLOR = 'var(--kui-red-500)';

  isHomePage = signal(false);
  backgroundColor = signal(this.DEFAULT_COLOR);

  constructor(private readonly router: Router) {
    this.router.events.subscribe(() => {
      this.isHomePage.set(this.router.url === '/');
    });
  }

  setColor(color?: string): void {
    this.backgroundColor.set(color ?? this.DEFAULT_COLOR);
  }
}
