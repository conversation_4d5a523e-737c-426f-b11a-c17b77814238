import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class UrlService {
  private currentUrl = '';
  private readonly previousUrl: BehaviorSubject<string> = new BehaviorSubject<string>('');

  readonly previousUrl$: Observable<string> = this.previousUrl.asObservable();

  setPreviousUrl(newUrl: string): void {
    this.previousUrl.next(this.currentUrl);
    if (!newUrl.includes('/galeria/')) {
      this.currentUrl = newUrl;
    }
  }
}
