import { inject, Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';
import { Observable, of } from 'rxjs';
import { DOCUMENT } from '@angular/common';
import { map, tap } from 'rxjs/operators';
import { EnvironmentApiUrl, UtilService } from '@trendency/kesma-core';
import { ArticleCard, mapPersonalizedRecommendationToArticleCard, PersonalizedRecommendationApiResponse } from '@trendency/kesma-ui';
import { CleanHttpService } from './clean-http.service';

@Injectable({
  providedIn: 'root',
})
export class PersonalizedRecommendationService {
  private readonly DOMAIN: string = 'Borsonline';
  private readonly PLATFORM: string = 'Borsonline';

  private isPersonalizedContentFetched = false;

  private readonly httpService = inject(CleanHttpService);
  private readonly utilsService = inject(UtilService);
  private readonly document = inject(DOCUMENT);

  get personalizedRecommendationApiUrl(): string {
    if (typeof environment.personalizedRecommendationApiUrl === 'string') {
      return environment.personalizedRecommendationApiUrl;
    }

    const { clientApiUrl, serverApiUrl } = environment.personalizedRecommendationApiUrl as EnvironmentApiUrl;
    return this.utilsService.isBrowser() ? clientApiUrl : serverApiUrl;
  }

  getPersonalizedRecommendations(limit = 24): Observable<ArticleCard[]> {
    const currentArticleUrl: string = this.document?.defaultView?.location.href || '';
    const vidCookie: string = this.document?.cookie?.match(/_vid=([^;]+)/)?.[1] || '';
    const pureSiteUrl: string = environment.siteUrl?.replace(/^https?:\/\/(www\.)?/, '') || '';

    return this.httpService
      .get<PersonalizedRecommendationApiResponse>(`${this.personalizedRecommendationApiUrl}/recommendation`, {
        params: {
          'traffickingPlatforms[]': this.PLATFORM,
          fingerPrint: vidCookie,
          articleCurrent: encodeURIComponent(currentArticleUrl),
          utmSource: pureSiteUrl,
          withoutPos: '1',
        },
      })
      .pipe(
        map((res: PersonalizedRecommendationApiResponse) => res[this.PLATFORM]?.map(mapPersonalizedRecommendationToArticleCard).slice(0, limit)),
        tap(() => (this.isPersonalizedContentFetched = true))
      );
  }

  sendPersonalizedRecommendationAv(externalRecommendations: ArticleCard[]): Observable<void> {
    if (!this.isPersonalizedContentFetched) {
      return of();
    }

    const ids: string[] = externalRecommendations.map((article: ArticleCard) => article.id ?? '');
    const avs: Record<string, string> = ids.reduce((res, value, index) => ({ ...res, [`a[${index + 1}]`]: value }), {});

    return this.httpService.post<void>(`${this.personalizedRecommendationApiUrl}/av`, null, {
      params: {
        domain: this.DOMAIN,
        platform: this.PLATFORM,
        ...avs,
      },
    });
  }
}
