import { inject, Injectable } from '@angular/core';
import { EnvironmentApiUrl, UtilService } from '@trendency/kesma-core';
import { CleanHttpService } from './clean-http.service';
import { PersonalizedRecommendationApiResponse, PersonalizedRecommendationArticle } from '@trendency/kesma-ui';
import { environment } from 'src/environments/environment';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class BrandingBoxService {
  private readonly httpService = inject(CleanHttpService);
  private readonly utils = inject(UtilService);

  getTrafficDeflectorData(traffickingPlatforms: string): Observable<PersonalizedRecommendationArticle[]> {
    return this.httpService
      .get<PersonalizedRecommendationApiResponse>(`${this.deflectorApiUrl}/recommendation`, {
        params: {
          traffickingPlatforms,
          utmSource: 'borsonline.hu',
          withoutPos: '1',
        },
      })
      .pipe(
        map(({ [traffickingPlatforms]: articles }) => articles),
        catchError(() => {
          return of([]);
        })
      );
  }

  private get deflectorApiUrl(): string {
    if (typeof environment.personalizedRecommendationApiUrl === 'string') {
      return environment.personalizedRecommendationApiUrl as string;
    }
    const { clientApiUrl, serverApiUrl } = environment.personalizedRecommendationApiUrl as EnvironmentApiUrl;
    return this.utils.isBrowser() ? clientApiUrl : serverApiUrl;
  }
}
