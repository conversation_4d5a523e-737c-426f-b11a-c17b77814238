import { inject, Injectable } from '@angular/core';
import { IHttpOptions, ReqService } from '@trendency/kesma-core';
import { Observable } from 'rxjs';
import { BackendNameday, Nameday } from '../definitions';
import { ApiResult } from '@trendency/kesma-ui';
import { map } from 'rxjs/operators';
import { mapBackendNamedayToNameday } from '../utils';
import { format } from 'date-fns';

@Injectable({
  providedIn: 'root',
})
export class NamedayService {
  private readonly reqService = inject(ReqService);

  getNamedays(params?: Record<string, string>): Observable<Nameday[]> {
    const options: IHttpOptions = { params };

    return this.reqService
      .get<ApiResult<BackendNameday[]>>('/mediaworks/nameday', options)
      .pipe(map(({ data }) => data.map((n) => mapBackendNamedayToNameday(n))));
  }

  getNamedaysByDate(date?: string | Date): Observable<Nameday[]> {
    const params: Record<string, string> = {};
    if (date) {
      params['date'] = typeof date === 'string' ? date : format(date, 'yyyy-MM-dd');
    }
    return this.getNamedays(params);
  }

  getTodayNamedays(): Observable<Nameday[]> {
    return this.getNamedaysByDate(format(new Date(), 'yyyy-MM-dd'));
  }
}
