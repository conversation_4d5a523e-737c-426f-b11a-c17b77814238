import { inject, Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';
import { ReqService } from '@trendency/kesma-core';
import { AstrologyData, BackendAstrologyResponse } from '../definitions';
import { getAstronomyCardData } from '../utils';

@Injectable({
  providedIn: 'root',
})
export class AstronetService {
  private readonly reqService = inject(ReqService);

  getAstronomy(): Observable<AstrologyData> {
    return this.reqService.get<BackendAstrologyResponse>('mediaworks/astronomy').pipe(map((data: BackendAstrologyResponse) => getAstronomyCardData(data)));
  }
}
