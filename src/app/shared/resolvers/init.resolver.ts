import { inject } from '@angular/core';

import { InitResolverData, InitResponse, SimplifiedMenuTree } from '@trendency/kesma-ui';
import { forkJoin, Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ApiService, PortalConfigService } from '../services';
import { ResolveFn } from '@angular/router';

export const initResolver: ResolveFn<InitResolverData> = (): Observable<InitResolverData> => {
  const apiService = inject(ApiService);
  const portalConfigService = inject(PortalConfigService);
  return forkJoin([
    apiService.init().pipe(
      map(({ data }) => {
        portalConfigService.setConfig(data.portalConfigs);
        return data;
      }),
      catchError(() => {
        return of({} as InitResponse);
      })
    ) as Observable<InitResponse>,
    apiService.getMenu().pipe(
      catchError(() => {
        return of({} as SimplifiedMenuTree);
      })
    ) as Observable<SimplifiedMenuTree>,
  ]).pipe(map<[InitResponse, SimplifiedMenuTree], InitResolverData>(([init, menu]) => ({ init, menu })));
};
