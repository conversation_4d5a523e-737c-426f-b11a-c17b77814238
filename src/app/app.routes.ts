import { Routes } from '@angular/router';
import { BaseComponent, CheckRedirectBefore404, initResolver } from './shared';

export const routes: Routes = [
  {
    path: 'layout-editor',
    loadChildren: () => import('./feature/layout-editor/layout-editor.routing').then((m) => m.layoutEditorRoutes),
  },
  {
    path: '',
    component: BaseComponent,
    resolve: { data: initResolver },
    children: [
      {
        path: '',
        pathMatch: 'full',
        loadChildren: () => import('./feature/home/<USER>').then((m) => m.HOME_ROUTES),
      },
      {
        path: 'file/:fileId/:fileName',
        loadChildren: () => import('./feature/file/file.routing').then((m) => m.FILE_ROUTES),
      },
      {
        path: 'cimke',
        loadChildren: () => import('./feature/tags-page/tags-page.routing').then((m) => m.TAGS_PAGE_ROUTES),
        runGuardsAndResolvers: 'paramsOrQueryParamsChange',
      },
      {
        path: 'galeriak',
        pathMatch: 'full',
        loadChildren: () => import('./feature/galleries/galleries.routing').then((m) => m.GALLERIES_ROUTES),
      },
      {
        path: 'galeria/:gallerySlug',
        loadChildren: () => import('./feature/gallery-layer/gallery-layer.routing').then((m) => m.GALLERY_LAYER_ROUTES),
      },
      {
        path: 'szerzo',
        loadChildren: () => import('./feature/authors/authors.routing').then((m) => m.AUTHORS_PAGE_ROUTES),
      },
      {
        path: 'szerzok',
        loadChildren: () => import('./feature/authors/authors.routing').then((m) => m.AUTHORS_PAGE_ROUTES),
      },
      {
        path: 'lexikon',
        loadChildren: () => import('./feature/lexicon/lexicon.routes').then((m) => m.LEXICON_ROUTES),
      },
      {
        path: 'kereses',
        loadChildren: () => import('./feature/search/search.routing').then((m) => m.SEARCH_ROUTES),
      },
      {
        path: 'hirlevel-feliratkozas',
        loadChildren: () => import('./feature/newsletter/newsletter-signup/newsletter-signup.routing').then((m) => m.NEWSLETTER_SIGNUP_ROUTES),
      },
      {
        path: 'hirlevel-feliratkozas-megerositese',
        loadChildren: () => import('./feature/newsletter/newsletter-confirm/newsletter-confirm.routing').then((m) => m.NEWSLETTER_CONFIRM_ROUTES),
      },
      {
        path: 'hirlevel-feliratkozas-megerosites-sikeres',
        loadChildren: () => import('./feature/newsletter/newsletter-success/newsletter-success.routing').then((m) => m.NEWSLETTER_SUCCESS_ROUTES),
        data: { pageTextSuffix: 'feliratkozás' },
      },
      {
        path: 'hirlevel-leiratkozas',
        loadChildren: () => import('./feature/newsletter/newsletter-success/newsletter-success.routing').then((m) => m.NEWSLETTER_SUCCESS_ROUTES),
        data: { pageTextSuffix: 'leiratkozás' },
      },
      {
        path: 'hirlevel-feliratkozas-sikertelen',
        loadChildren: () => import('./feature/newsletter/newsletter-failed/newsletter-failed.routing').then((m) => m.NEWSLETTER_FAILED_ROUTES),
      },
      {
        path: 'hirarchivum',
        loadChildren: () => import('./feature/news-archive-page/news-archive-page.routing').then((m) => m.NEWS_ARCHIVE_PAGE_ROUTES),
      },
      {
        path: 'dosszie/:dossierSlug',
        loadChildren: () => import('./feature/dossier-list/dossier-list.routing').then((m) => m.DOSSIER_LIST_ROUTES),
      },
      // "short-circuit" - ha a file nem létezik, az SSR lefut - ami file lehet, azt küldjük 404-re
      {
        path: 'assets/:file',
        redirectTo: '404',
      },
      {
        path: 'assets/:dir/:file',
        redirectTo: '404',
      },
      {
        path: 'script/:file',
        redirectTo: '404',
      },
      {
        path: 'bejelentkezes',
        loadChildren: () => import('./feature/login/login.routing').then((m) => m.loginRouting),
      },
      {
        path: 'regisztracio',
        loadChildren: () => import('./feature/registration/registration.routing').then((m) => m.registrationRouting),
      },
      {
        path: 'elfelejtett-jelszo',
        loadChildren: () => import('./feature/forgot-password/forgot-password.routing').then((m) => m.forgotPasswordRouting),
      },
      {
        path: 'legtobbet-kommentelt',
        loadChildren: () => import('./feature/top-commented-page/top-commented-page.routing').then((m) => m.TOP_COMMENTED_PAGE_ROUTES),
      },
      {
        path: 'rovat/:categorySlug',
        loadChildren: () => import('./feature/category/category.routing').then((m) => m.CATEGORY_ROUTES),
        runGuardsAndResolvers: 'paramsOrQueryParamsChange',
      },
      {
        path: 'cikk-elonezet/:previewHash',
        loadChildren: () => import('./feature/article/article.routes').then((m) => m.ARTICLE_ROUTES),
        data: {
          skipSeoMetaCheck: true,
          omitGlobalPageView: true,
        },
      },
      {
        path: 'cikk-elonezet/:previewHash/:previewType',
        loadChildren: () => import('./feature/article/article.routes').then((m) => m.ARTICLE_ROUTES),
        data: {
          skipSeoMetaCheck: true,
          omitGlobalPageView: true,
        },
      },
      {
        path: ':categorySlug/:year/:month/:articleSlug',
        loadChildren: () => import('./feature/article/article.routes').then((m) => m.ARTICLE_ROUTES),
        data: {
          skipSeoMetaCheck: true,
          showRecommendations: true,
          omitGlobalPageView: true,
        },
      },
      {
        path: 'cikk-elonezet/:previewHash/:previewType',
        loadChildren: () => import('./feature/article/article.routes').then((m) => m.ARTICLE_ROUTES),
        data: {
          skipSeoMetaCheck: true,
          omitGlobalPageView: true,
        },
      },
      {
        path: 'elrendezes-elonezet/:layoutHash',
        loadChildren: () => import('./feature/layout-preview/layout-preview.routing').then((m) => m.LAYOUT_PREVIEW_ROUTES),
      },
      {
        path: 'idojaras',
        loadChildren: () => import('./feature/weather/weather.routing').then((m) => m.WEATHER_ROUTES),
      },
      {
        path: 'jatekok',
        loadChildren: () => import('./feature/games/games.routing').then((m) => m.GAMES_PAGE_ROUTES),
      },
      {
        path: 'profil',
        loadChildren: () => import('./feature/profile/profile.routing').then((m) => m.PROFILE_ROUTES),
      },
      {
        path: 'friss-hirek',
        loadChildren: () => import('./feature/fresh-news/fresh-news.routing').then((m) => m.FRESH_NEWS_ROUTES),
      },
      {
        path: '404',
        loadComponent: () => import('./shared/components/404/404.component').then((m) => m.Error404Component),
        canActivate: [CheckRedirectBefore404],
      },
      {
        path: ':slug',
        loadChildren: () => import('./feature/static-page/static-page.routing').then((m) => m.STATIC_PAGE_ROUTES),
        data: {
          omitGlobalPageView: true,
        },
      },
      {
        path: '**',
        redirectTo: '404',
      },
    ],
  },
];
