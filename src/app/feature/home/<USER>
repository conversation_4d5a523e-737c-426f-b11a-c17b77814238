import { Component, inject } from '@angular/core';
import { ActivatedRoute, Data } from '@angular/router';
import { SeoService } from '@trendency/kesma-core';
import { LayoutApiData, LayoutPageType } from '@trendency/kesma-ui';
import { map, Observable } from 'rxjs';
import { defaultMetaInfo } from '../../shared';
import { LayoutComponent } from '../layout/components/layout/layout.component';
import { AsyncPipe } from '@angular/common';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  imports: [LayoutComponent, AsyncPipe],
})
export class HomeComponent {
  private readonly route = inject(ActivatedRoute);
  private readonly seo = inject(SeoService);

  layoutApiData$: Observable<LayoutApiData> = this.route.data.pipe(
    map((data: Data | { layoutData: LayoutApiData }) => {
      this.seo.setMetaData({
        ...defaultMetaInfo,
        keywords: 'hírek, információk, sporthírek, sztárok, életmód, időjárás, programajánló',
      });
      return data.layoutData;
    })
  );

  LayoutPageType = LayoutPageType;

  constructor() {
    this.seo.updateCanonicalUrl('');
  }
}
