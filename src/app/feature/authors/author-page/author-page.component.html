<section>
  <div class="wrapper">
    <div class="header">
      <app-breadcrumb [data]="[{ label: '<PERSON><PERSON><PERSON><PERSON><PERSON>', url: '/szerzo' }]" />
      @if (author()?.publicAuthorName; as authorName) {
        <app-page-title [title]="authorName" />
      }
    </div>
    <div class="divider"></div>
    <app-author-card [data]="author()" [showArticlesLink]="false" [isLazy]="false"></app-author-card>
    <div class="divider"></div>
    <app-search-input class="search" placeholder="Keresés a szerző cikkeiben" />
  </div>
  <div class="wrapper with-aside">
    <div class="left-column">
      <div class="select">
        <div class="title">A szerző cikkei</div>
        <app-search-select [items]="orderItems" [value]="initialOrder" bindValue="value" (valueChanged)="onOrderChanged($event)"></app-search-select>
      </div>
      <div class="divider"></div>
      @for (article of articles(); track article.id) {
        <app-article-card class="article-card" [data]="article" [styleId]="ArticleCardType.SideImgDateTitleLead"></app-article-card>
      }
      @if (limitable(); as paginator) {
        @if (paginator?.pageMax) {
          <app-pager [rowAllCount]="paginator?.rowAllCount" [rowOnPageCount]="paginator?.rowOnPageCount"></app-pager>
        }
      }
    </div>
    <aside class="aside">
      <app-sidebar />
    </aside>
  </div>
</section>
