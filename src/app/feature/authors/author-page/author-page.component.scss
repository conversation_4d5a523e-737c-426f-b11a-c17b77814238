@use 'shared' as *;

:host {
  display: block;
  margin-block: 32px 64px;
  color: var(--kui-black-950);
  @include media-breakpoint-down(md) {
    margin-block: 32px;
    .aside {
      margin-top: 32px;
    }
  }
  .header {
    display: flex;
    flex-direction: column;
    @include media-breakpoint-down(md) {
      gap: 8px;
    }
  }
  .divider {
    height: 1px;
    background-color: rgba($black, 0.2);
    margin-block: 32px;
    width: 100%;
    @include media-breakpoint-down(md) {
      margin-block: 16px;
    }
  }
  .search {
    margin-bottom: 64px;
    @include media-breakpoint-down(md) {
      margin-bottom: 32px;
    }
  }
  .select {
    display: flex;
    justify-content: space-between;
    gap: 32px;
    app-search-select {
      min-width: 240px;
    }
    @include media-breakpoint-down(sm) {
      flex-direction: column;
      gap: 8px;
    }
  }
  .title {
    font-size: 30px;
    font-weight: 700;
    line-height: 38px;
    @include media-breakpoint-down(md) {
      font-size: 24px;
      line-height: 28px;
    }
  }
  .article-card {
    margin-bottom: 32px;
    @include media-breakpoint-down(md) {
      margin-bottom: 16px;
    }
  }
}
