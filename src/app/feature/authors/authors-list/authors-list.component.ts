import { ChangeDetectionStrategy, Component, inject, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { toSignal } from '@angular/core/rxjs-interop';
import { map } from 'rxjs/operators';
import { ApiResponseMetaList, ApiResult, BackendAuthorData, createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';
import { AuthorCardComponent, BreadcrumbComponent, createBorsOnlineTitle, defaultMetaInfo, PagerComponent, PageTitleComponent } from '../../../shared';
import { IMetaData, SeoService } from '@trendency/kesma-core';

@Component({
  selector: 'app-authors-list',
  imports: [PagerComponent, PageTitleComponent, BreadcrumbComponent, AuthorCardComponent],
  templateUrl: './authors-list.component.html',
  styleUrl: './authors-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AuthorsListComponent implements OnInit {
  private readonly route = inject(ActivatedRoute);
  private readonly seoService = inject(SeoService);

  readonly authors = toSignal(this.route.data.pipe(map(({ data }) => data as ApiResult<BackendAuthorData[], ApiResponseMetaList>)));

  ngOnInit(): void {
    this.setMetaData();
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('szerzo', this.route.snapshot);
    canonical && this.seoService.updateCanonicalUrl(canonical);
    const title = createBorsOnlineTitle('Szerzők');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title,
      ogTitle: title,
    };
    this.seoService.setMetaData(metaData);
  }
}
