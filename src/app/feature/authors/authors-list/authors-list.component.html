<section>
  <div class="wrapper">
    <div class="header">
      <app-breadcrumb [data]="[{ label: 'Szer<PERSON>ők' }]" />
      <app-page-title title="Szerzők" />
    </div>
    <div class="divider"></div>
    @for (author of authors()?.data || []; track author.id; let first = $first) {
      <app-author-card [data]="author" [isLazy]="!first"></app-author-card>
    }
    @if (authors()?.meta?.limitable; as paginator) {
      @if (paginator?.pageMax) {
        <app-pager [rowAllCount]="paginator?.rowAllCount" [rowOnPageCount]="paginator?.rowOnPageCount"></app-pager>
      }
    }
  </div>
</section>
