import { ActivatedRouteSnapshot, ResolveFn, Router } from '@angular/router';
import { SeoService } from '@trendency/kesma-core';
import { inject } from '@angular/core';
import { catchError, map } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { ApiResponseMetaList, ApiResult, BackendAuthorData, RedirectService } from '@trendency/kesma-ui';
import { AuthorsService } from '../auhtors.service';

export const authorsListResolver: ResolveFn<ApiResult<BackendAuthorData[], ApiResponseMetaList>> = (route: ActivatedRouteSnapshot) => {
  const seoService = inject(SeoService);
  const redirectService = inject(RedirectService);
  const authorsService = inject(AuthorsService);
  const router = inject(Router);

  // Redirect plural url to singular
  if (seoService.currentUrl.includes('szerzok')) {
    redirectService.redirectOldUrl(`szerzo`);
  }
  const currentPage = route.queryParams['page'] ? parseInt(route.queryParams['page'], 10) - 1 : 0;
  return authorsService.getAuthors(currentPage, 12, false).pipe(
    map((res) => {
      return {
        data: res.data as BackendAuthorData[],
        meta: res.meta as ApiResponseMetaList,
      };
    }),
    map((res) => {
      if (redirectService.shouldBeRedirect(currentPage, res.data)) {
        redirectService.redirectOldUrl(`szerzo`, false, 302);
      }
      return res;
    }),
    catchError((error) => {
      router
        .navigate(['/404'], {
          state: { errorResponse: JSON.stringify(error) },
          skipLocationChange: true,
        })
        .then();
      return throwError(error);
    })
  );
};
