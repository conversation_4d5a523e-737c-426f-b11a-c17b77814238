@use 'shared' as *;

:host {
  display: block;
  margin-top: 32px;
  margin-bottom: 64px;
  @include media-breakpoint-down(md) {
    margin-bottom: 32px;
  }
  .wrapper {
    display: flex;
    flex-direction: column;
    gap: 32px;
    color: var(--kui-black-950);
    @include media-breakpoint-down(md) {
      gap: 16px;
    }
  }
  .header {
    display: flex;
    flex-direction: column;
    @include media-breakpoint-down(md) {
      gap: 8px;
    }
  }
  .divider {
    height: 1px;
    background-color: rgba($black, 0.2);
    width: 100%;
  }
  app-pager {
    margin-top: 32px;
    @include media-breakpoint-down(md) {
      margin-top: 16px;
    }
  }
}
