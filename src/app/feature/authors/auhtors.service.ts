import { inject, Injectable } from '@angular/core';
import { IHttpOptions, ReqService } from '@trendency/kesma-core';
import { Observable } from 'rxjs';
import { ApiResponseMetaList, ApiResult, BackendAuthorData } from '@trendency/kesma-ui';
import { AuthorData } from '../../shared';

@Injectable({
  providedIn: 'root',
})
export class AuthorsService {
  reqService = inject(ReqService);

  /**
   * Returns the list of all authors
   */
  getAuthors(page: number, perPage = 9999, isInner?: boolean): Observable<ApiResult<BackendAuthorData[], ApiResponseMetaList>> {
    const params = {
      rowCount_limit: perPage.toString(),
      page_limit: page.toString(),
      is_active: '1',
    };

    if (isInner) {
      Object.assign(params, { is_inner: '1' });
    }

    return this.reqService.get('user/authors', {
      params,
    });
  }

  getPublicAuthorSocial(global_filter?: string, options?: IHttpOptions): Observable<ApiResult<AuthorData, ApiResponseMetaList>> {
    let params = { ...options?.params };
    params = global_filter ? { ...params, global_filter: global_filter } : params;
    return this.reqService.get(`/user/author_social`, { params });
  }
}
