import { Routes } from '@angular/router';
import { PageValidatorGuard } from '@trendency/kesma-ui';
import { AuthorsListComponent } from './authors-list/authors-list.component';
import { authorPageResolver } from './author-page/author-page.resolver';
import { AuthorPageComponent } from './author-page/author-page.component';
import { authorsListResolver } from './authors-list/authors-list.resolver';

export const AUTHORS_PAGE_ROUTES: Routes = [
  {
    path: '',
    component: AuthorsListComponent,
    resolve: { data: authorsListResolver },
    runGuardsAndResolvers: 'always',
    canActivate: [PageValidatorGuard],
  },
  {
    path: ':authorSlug',
    component: AuthorPageComponent,
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    pathMatch: 'full',
    resolve: {
      data: authorPageResolver,
    },
    canActivate: [PageValidatorGuard],
  },
];
