import { HttpClient, HttpEvent, HttpEventType, HttpHeaders } from '@angular/common/http';
import { ChangeDetectionStrategy, Component, inject, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { catchError, Observable, of } from 'rxjs';
import { EnvironmentApiUrl, IMetaData, SeoService, UtilService } from '@trendency/kesma-core';
import { environment } from '../../../environments/environment';
import { defaultMetaInfo } from '../../shared';

@Component({
  selector: 'app-file',
  templateUrl: './file.component.html',
  styleUrl: './file.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FileComponent implements OnInit {
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  private readonly http = inject(HttpClient);
  private readonly utils = inject(UtilService);
  private readonly seo = inject(SeoService);

  fileId = '';
  fileName = '';

  ngOnInit(): void {
    const fileId: string = this.route.snapshot.paramMap.get('fileId') || '';
    const fileName: string = this.route.snapshot.paramMap.get('fileName') || '';

    if (Boolean(fileId) && Boolean(fileName)) {
      this.downloadFile(this.getUri(fileId), fileName);
      this.fileId = fileId;
      this.fileName = fileName;
    }
    this.setMetaData();
  }

  handleClick(e: MouseEvent): void {
    e.preventDefault();
    this.downloadFile(this.getUri(this.fileId), this.fileName);
  }

  private getUri(fileId: string): string {
    const apiUri = typeof environment.apiUrl === 'string' ? environment.apiUrl : (environment.apiUrl as EnvironmentApiUrl).clientApiUrl;
    return `${apiUri}/media/file/stream/${fileId}`;
  }

  private getFile(uri: string): Observable<HttpEvent<Blob> | undefined> {
    return this.http
      .get(uri, {
        headers: new HttpHeaders().set('portal', 'bors'),
        observe: 'events',
        reportProgress: true,
        responseType: 'blob',
      })
      .pipe(
        catchError((err) => {
          console.error('Failed to download file: ', err);
          this.router.navigate(['/404']);
          return of(undefined);
        })
      );
  }

  private downloadFile(uri: string, fileName: string): void {
    if (!this.utils.isBrowser()) {
      return;
    }
    this.getFile(uri).subscribe((x) => {
      if (!x) {
        return;
      }
      /*
       * It is necessary to create a new blob object with mime-type explicitly set
       * otherwise only Chrome works like it should
       */
      if (x.type === HttpEventType.Response) {
        const newBlob = new Blob([x.body as BlobPart], { type: 'application/pdf' });

        // Create a link pointing to the ObjectURL containing the blob.
        const data = window.URL.createObjectURL(newBlob);

        const link = document.createElement('a');
        link.href = data;
        link.download = fileName;
        // this is necessary as link.click() does not work on the latest firefox
        link.dispatchEvent(new MouseEvent('click', { bubbles: true, cancelable: true, view: window }));

        setTimeout(() => {
          // For Firefox it is necessary to delay revoking the ObjectURL
          window.URL.revokeObjectURL(data);
          link.remove();
        }, 100);
      }
    });
  }

  private setMetaData(): void {
    const title = `Fájl letöltés - ${defaultMetaInfo.ogSiteName}`;

    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
