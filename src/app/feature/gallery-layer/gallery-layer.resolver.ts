import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, ResolveFn, Router } from '@angular/router';
import { throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';
import { ApiService } from '../../shared';
import { GalleryDetails } from '@trendency/kesma-ui';

export const GalleryLayerResolver: ResolveFn<GalleryDetails> = (route: ActivatedRouteSnapshot) => {
  const router = inject(Router);
  const apiService = inject(ApiService);
  const { gallerySlug, index } = route.params;

  if (isNaN(index) || index <= 0) {
    router.navigate(['/galeria', gallerySlug, 1]).then();
  }

  return apiService.getGalleryDetails(gallerySlug).pipe(
    tap((gallery: GalleryDetails) => {
      const images = gallery?.images;
      if (!images?.length) {
        router.navigate(['/404']).then();
      } else if (index > images.length) {
        router.navigate(['/galeria', gallerySlug, 1]).then();
      }
    }),
    catchError((error: HttpErrorResponse) => {
      router
        .navigate(['/404'], {
          skipLocationChange: true,
        })
        .then();
      return throwError(() => error);
    })
  );
};
