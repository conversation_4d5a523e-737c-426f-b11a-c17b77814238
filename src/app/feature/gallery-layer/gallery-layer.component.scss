@use 'shared' as *;

$imageBackground: #03273b;

:host {
  user-select: none;
  background-color: var(--kui-black-950);
  color: var(--kui-white);
  fill: var(--kui-white);
  padding-bottom: 24px;
  overflow-x: auto;
  position: fixed;
  inset: 0;
  z-index: 1500;
  .header {
    padding: 16px 20px;
    display: flex;
    align-items: flex-start;
    margin-bottom: 12px;
    gap: 8px;
  }
  .title {
    font-size: 16px;
    font-weight: 700;
    line-height: 20px;
    overflow-wrap: anywhere;
    letter-spacing: 0.08px;
    margin-right: auto;
  }
  .fullscreen {
    margin-left: 8px;
  }
  .close {
    padding: 8px;
  }
  .arrow {
    outline: 1px solid var(--kui-white);
    position: absolute;
    top: calc(50% - 20px);
    width: 40px;
    height: 40px;

    &:hover {
      outline-color: var(--kui-yellow-500);
      color: var(--kui-yellow-500);
    }
    &.prev {
      rotate: -180deg;
      left: 20px;
      @include media-breakpoint-down(md) {
        left: 1.5px;
      }
    }
    &.next {
      right: 20px;
      @include media-breakpoint-down(md) {
        right: 1.5px;
      }
    }
  }
  section {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .wrapper {
    max-width: $global-wrapper-width-with-bg;
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin: auto;
    padding: 0 16px;
  }
  .thumbnail {
    width: 100%;
    aspect-ratio: 16/9;
    object-fit: contain;
    background-color: $imageBackground;
    min-height: 200px;
    @include media-breakpoint-down(md) {
      aspect-ratio: auto;
      max-height: 300px;
    }
  }
  .data {
    padding: 12px;
    font-size: 14px;
    line-height: 16px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    @include media-breakpoint-down(md) {
      padding-inline: 0;
      margin-top: auto;
    }
  }
  .count {
    display: flex;
    align-items: flex-start;
    gap: 10px;
  }
  .caption {
    font-weight: 400;
    overflow-wrap: anywhere;
  }
  kesma-icon {
    cursor: pointer;
    flex-shrink: 0;
    &:hover {
      fill: var(--kui-yellow-500);
    }
  }
  .photographer {
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
  }
  @include media-breakpoint-down(md) {
    .thumbnail-wrapper {
      margin-top: auto;
      position: relative;
    }
  }
}
