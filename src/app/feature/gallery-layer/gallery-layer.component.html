@if (!isUserAdultChoice() && gallery().isAdult) {
  <app-adult-layer (isUserAdult)="onIsUserAdultChoose($event)" />
} @else {
  <section trFullscreen #fullscreen="trFullscreen">
    <div class="header">
      <h2 class="title">{{ gallery().title }}</h2>
      <kesma-icon class="fullscreen" name="fullscreen" [size]="32" (click)="onToggleFs(fullscreen)" />
      <kesma-icon class="close" name="close" [size]="16" (click)="onClose()" />
    </div>
    <div class="wrapper">
      @if (selectedImage(); as image) {
        <div class="thumbnail-wrapper">
          <kesma-icon class="arrow prev" name="arrow-right" [size]="40" (click)="onNavigate(selectedIndex() - 1)" />
          <img class="thumbnail" [src]="image.url.fullSize" [alt]="image?.altText || image?.title || gallery().title" loading="eager" />
          <kesma-icon class="arrow next" name="arrow-right" [size]="40" (click)="onNavigate(selectedIndex() + 1)" />
        </div>
        <div class="data">
          <div class="count">
            <strong>{{ selectedIndex() }}/{{ gallery().images.length }}</strong>
            @if (image?.caption) {
              <div class="caption">{{ image.caption }}</div>
            }
          </div>
          @if (image?.photographer) {
            <div class="photographer">Fotó: {{ image.photographer }}</div>
          }
        </div>
      }
    </div>
  </section>
}
