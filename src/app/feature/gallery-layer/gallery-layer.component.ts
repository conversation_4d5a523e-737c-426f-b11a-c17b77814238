import { ChangeDetectionStrategy, Component, computed, DestroyRef, inject, OnInit, signal, Signal } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { map, tap } from 'rxjs/operators';
import { ActivatedRoute, Router } from '@angular/router';
import { createCanonicalUrlForPageablePage, GalleryData, IconComponent } from '@trendency/kesma-ui';
import { FullscreenDirective, IMetaData, SeoService, StorageService, UtilService } from '@trendency/kesma-core';
import { ADULT_CHOICE_STORAGE_KEY, AdultLayerComponent, createBorsOnlineTitle, defaultMetaInfo, UrlService } from '../../shared';
import { Location } from '@angular/common';

@Component({
  selector: 'app-gallery-layer',
  templateUrl: './gallery-layer.component.html',
  styleUrl: './gallery-layer.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [IconComponent, FullscreenDirective, AdultLayerComponent],
})
export class GalleryLayerComponent implements OnInit {
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  private readonly seoService = inject(SeoService);
  private readonly utilService = inject(UtilService);
  private readonly urlService = inject(UrlService);
  private readonly location = inject(Location);
  private readonly destroyRef = inject(DestroyRef);
  private readonly storageService = inject(StorageService);

  /**
   * Url of the article that was the source referrer of the current gallery navigation
   * for example: clicking on a gallery in an article body.
   */
  referrerArticleUrl?: string;

  readonly isFullscreen = signal<boolean>(false);
  readonly previousUrl = signal<string | null>(null);
  readonly isUserAdultChoice = signal<boolean>(false);

  readonly gallery: Signal<GalleryData> = toSignal(
    this.route.data.pipe(
      map(({ data }) => data),
      tap((data) => this.setMetaData(data))
    ),
    {
      initialValue: {} as GalleryData,
    }
  );

  readonly selectedIndex: Signal<number> = toSignal(this.route.params.pipe(map(({ index }) => (isNaN(+index) ? 1 : Number(index)))), {
    initialValue: 1,
  });

  readonly selectedImage = computed(() => {
    const images = this.gallery()?.images || [];
    return images.at(this.selectedIndex() - 1 || 0);
  });

  ngOnInit(): void {
    this.setMetaData(this.gallery());
    this.isUserAdultChoice.set((this.storageService.getSessionStorageData(ADULT_CHOICE_STORAGE_KEY, false) ?? false) && this.gallery()?.isAdult);
    if (this.utilService.isBrowser()) {
      this.referrerArticleUrl = history?.state?.referrerArticle;
      this.urlService.previousUrl$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((previousUrl) => {
        previousUrl && this.previousUrl.set(previousUrl);
      });
    }
  }

  onIsUserAdultChoose(isUserAdult: boolean): void {
    this.isUserAdultChoice.set(isUserAdult);
  }

  onToggleFs(directive: FullscreenDirective): void {
    this.isFullscreen.update((value) => !value);
    directive.toggleFullScreen();
  }

  onClose(): void {
    this.isFullscreen.set(false);
    if (this.previousUrl()) {
      this.location.back();
      return;
    }

    this.router.navigate(['/', 'galeriak']).then();
  }

  onNavigate(index: number): void {
    const maxLength = this.gallery().images.length;
    if (index > maxLength) {
      index = 1;
    } else if (index < 1) {
      index = maxLength;
    }
    this.router
      .navigate(['/galeria', this.gallery().slug, index], {
        state: {
          ...(this.referrerArticleUrl ? { referrerArticle: this.referrerArticleUrl } : {}),
        },
        replaceUrl: true,
      })
      .then();
  }

  private setMetaData(gallery: GalleryData): void {
    const title = createBorsOnlineTitle(gallery.title);

    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title,
      description: gallery?.description || defaultMetaInfo.description,
      ogTitle: title,
      ogImage: gallery?.highlightedImageUrl,
    };
    this.seoService.setMetaData(metaData);
    const canonical = createCanonicalUrlForPageablePage('galeria', this.route.snapshot);
    canonical && this.seoService.updateCanonicalUrl(canonical);
  }
}
