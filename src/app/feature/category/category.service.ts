import { inject, Injectable } from '@angular/core';
import { ApiService, CategoryResolverResponse, mapCategoryResponse } from '../../shared';
import { ApiResponseMetaList, ApiResult, ArticleCard, LayoutService, LayoutWithExcludeIds, PrimaryColumn, RedirectService } from '@trendency/kesma-ui';
import { Params, Router } from '@angular/router';
import { Observable, switchMap, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ReqService } from '@trendency/kesma-core';

export const MAX_RESULTS_PER_PAGE = 30;

@Injectable({
  providedIn: 'root',
})
export class CategoryService {
  private readonly apiService = inject(ApiService);
  private readonly layoutService = inject(LayoutService);
  private readonly router = inject(Router);
  private readonly redirectService = inject(RedirectService);
  private readonly reqService = inject(ReqService);

  getRequestForCategoryLayout(params: Params, queryParams: Params): Observable<CategoryResolverResponse> {
    const { categorySlug } = params;
    const { page } = queryParams;
    const pageIndex = page ? page - 1 : 0;

    return this.layoutService.getLayoutWithExcludeIds(categorySlug).pipe(
      switchMap((layoutResponse: LayoutWithExcludeIds) => {
        return this.apiService.getCategoryArticles(categorySlug, pageIndex, MAX_RESULTS_PER_PAGE, undefined, undefined, layoutResponse.excludedIds).pipe(
          catchError((error) => {
            return throwError(error);
          }),
          map((res) => {
            if (this.redirectService.shouldBeRedirect(pageIndex, res?.data)) {
              this.redirectService.redirectOldUrl(`rovat/${categorySlug}`, false, 302);
            }
            return res as ApiResult<ArticleCard[], ApiResponseMetaList>;
          }),
          map((categoryResponse: ApiResult<ArticleCard[], ApiResponseMetaList>) => {
            const mappedResponse = mapCategoryResponse(categoryResponse, categorySlug, undefined, undefined, layoutResponse);
            return mappedResponse;
          })
        );
      })
    );
  }

  getParentColumns(): Observable<ApiResult<PrimaryColumn[], ApiResponseMetaList>> {
    return this.reqService.get('/source/content-group/columns?parents_only=1&rowCount_limit=9999');
  }

  getRequestForCategoryByDate(params: Params, queryParams: Params): Observable<CategoryResolverResponse> {
    const { categorySlug, year, month } = params;
    const { page } = queryParams;
    const pageIndex = page ? page - 1 : 0;

    const request$ = this.layoutService.getLayoutWithExcludeIds(categorySlug).pipe(
      switchMap((layoutResponse) =>
        this.apiService.getCategoryArticles(categorySlug, pageIndex, MAX_RESULTS_PER_PAGE, year, month, []).pipe(
          map((categoryResponse: ApiResult<ArticleCard[], ApiResponseMetaList>) =>
            mapCategoryResponse(categoryResponse, categorySlug, year, month, layoutResponse as LayoutWithExcludeIds, true)
          ),
          catchError((error) => {
            this.router
              .navigate(['/', '404'], {
                state: {
                  errorResponse: JSON.stringify(error),
                },
                skipLocationChange: true,
              })
              .then();
            return throwError(error);
          })
        )
      )
    );
    if (isNaN(year) || isNaN(month)) {
      this.router.navigate(['/', '404'], {
        skipLocationChange: true,
      });
    }
    return request$;
  }
}
