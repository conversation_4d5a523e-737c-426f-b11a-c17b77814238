import { Routes } from '@angular/router';
import { CategoryComponent } from './category.component';
import { PageValidatorGuard } from '@trendency/kesma-ui';
import { categoryPageResolver } from './category.resolver';

export const CATEGORY_ROUTES: Routes = [
  {
    path: '',
    pathMatch: 'full',
    canActivate: [PageValidatorGuard],
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    component: CategoryComponent,
    resolve: {
      data: categoryPageResolver,
    },
    data: { categoryRouteType: 'category-layout' },
  },
  {
    path: ':month',
    pathMatch: 'full',
    canActivate: [PageValidatorGuard],
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    component: CategoryComponent,
    resolve: {
      data: categoryPageResolver,
    },
    data: { categoryRouteType: 'category-month' },
  },
];
