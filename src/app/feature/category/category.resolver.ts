import { CategoryResolverResponse, CategoryRouteType, HeaderService } from '../../shared';
import { ResolveFn, Router } from '@angular/router';
import { inject } from '@angular/core';
import { CategoryService } from './category.service';
import { catchError, tap } from 'rxjs/operators';
import { Observable, throwError } from 'rxjs';

export const categoryPageResolver: ResolveFn<CategoryResolverResponse> = ({ data, params, queryParams }) => {
  const categoryService = inject(CategoryService);
  const headerService = inject(HeaderService);
  const router = inject(Router);
  const categoryRouteType: CategoryRouteType = data['categoryRouteType'];

  switch (categoryRouteType) {
    case 'category-layout':
      return categoryService.getRequestForCategoryLayout(params, queryParams).pipe(
        tap((data) => {
          if (data?.columnColor) {
            headerService.setColor(data?.columnColor);
          } else {
            headerService.setColor();
          }
        }),
        catchError((error) => {
          router.navigate(['/', '404'], { skipLocationChange: true }).then();
          return throwError(() => error);
        })
      ) as Observable<CategoryResolverResponse>;
    default:
      return categoryService.getRequestForCategoryByDate(params, queryParams).pipe(
        tap((data) => {
          if (data?.columnColor) {
            headerService.setColor(data?.columnColor);
          } else {
            headerService.setColor();
          }
        })
      );
  }
};
