<section>
  @if (!currentStaticPageType() || currentStaticPageType() === customStaticPageType.StaticPage) {
    <div class="wrapper page-header">
      <app-breadcrumb [data]="[{ label: title() }]" />
      <app-page-title [title]="title()" />
      <div class="divider"></div>
    </div>
    <div class="wrapper with-aside">
      <div class="left-column">
        @for (element of body(); track element.uuid) {
          @switch (element.type) {
            @case (ArticleBodyType.Wysywyg) {
              @for (item of element?.details; track item.uuid) {
                <app-wysiwyg-box [html]="item?.value"></app-wysiwyg-box>
              }
            }
          }
        }
      </div>
      <aside>
        <app-sidebar [adPageType]="adPageType"></app-sidebar>
      </aside>
    </div>
  }
  @if (currentStaticPageType() === customStaticPageType.CustomPage) {
    <div class="wrapper">
      <div class="page-header">
        <app-breadcrumb [data]="[{ label: title() }]" />
        <app-page-title [title]="title()" />
        <div class="divider"></div>
      </div>
      <div class="static-page-container-no-side">
        <app-layout
          [adPageType]="adPageType"
          [layoutType]="LayoutPageType.HOME"
          [structure]="layoutApiData()?.struct"
          [configuration]="layoutApiData()?.content"
        >
        </app-layout>
      </div>
    </div>
  }
</section>
