import { inject, Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { StaticPageService } from './static-page.service';
import { ApiResult, LayoutApiData, LayoutMeta, StaticPageResponse } from '@trendency/kesma-ui';

@Injectable({ providedIn: 'root' })
export class StaticPageResolver {
  private readonly staticPageService = inject(StaticPageService);
  private readonly router = inject(Router);

  resolve(route: ActivatedRouteSnapshot): Observable<ApiResult<LayoutApiData, LayoutMeta> | ApiResult<StaticPageResponse>> {
    const param = route.params['slug'] as string;
    const previewHash = route.queryParams['previewHash'] as string;
    const request$ = previewHash ? this.staticPageService.getStaticPagePreview(param, previewHash) : this.staticPageService.getStaticPage(param);

    return request$.pipe(
      catchError((error: Error) => {
        this.router
          .navigate(['/', '404'], {
            state: { errorResponse: JSON.stringify(error) },
            skipLocationChange: true,
          })
          .then();
        return throwError(() => error);
      })
    );
  }
}
