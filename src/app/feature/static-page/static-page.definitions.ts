export interface IStaticPageResponse {
  body: IComponentData[];
  id: string;
  publishDate: IDate;
  slug: string;
  title: string;
  type: string;
}

export type IComponentData = Readonly<{
  type: string;
  uuid: string;
  key: string;
  subComponents: IComponentData[];
  details: IComponentFieldData[];
  autoplay?: boolean;
  adId?: number;
  cssClass?: string;
}>;

export enum CustomStaticPageType {
  StaticPage = 'staticPage',
  CustomPage = 'customBuiltPage',
}

export type IDate = Readonly<{
  date: string;
}>;

export type IComponentFieldData = Readonly<{
  type: string;
  inputType: string;
  key: string;
  uuid: string;
  value: string;
  multiple?: boolean;
  properties?: any;
  valueDetails?: any;
}>;
