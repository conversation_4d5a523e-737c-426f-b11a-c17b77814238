import { CommentOrder, Reaction } from '../api/comment.definitions';
import { ApiListResult, BackendComment, BackendCommentUser, backendDateToDate, Comment, ReactionsData, strBoolToBool, User } from '@trendency/kesma-ui';

export function getCommentOrderParam(order?: CommentOrder): Record<string, string> {
  switch (order) {
    case 'most-popular':
      return { 'likeCount_order[0]': 'desc', 'createdAt_order[1]': 'desc' };
    case 'oldest':
      return { 'createdAt_order[0]': 'asc' };
    default:
      return { 'createdAt_order[0]': 'desc' };
  }
}

export function mapBackendCommentToComment(backendComment: BackendComment): Comment {
  return {
    id: backendComment.id,
    author: mapBackendCommentUserToUser(backendComment.user),
    text: backendComment.text,
    answerCount: backendComment.answerCount ? parseInt(backendComment.answerCount, 10) : 0,
    likeCount: backendComment.likeCount ? parseInt(backendComment.likeCount, 10) : 0,
    dislikeCount: backendComment.dislikeCount ? parseInt(backendComment.dislikeCount, 10) : 0,
    createdAt: backendDateToDate(backendComment.createdAt) ?? new Date(),
    updatedAt: backendComment.updatedAt ? (backendDateToDate(backendComment.updatedAt) ?? undefined) : undefined,
    isLeader: strBoolToBool(backendComment.isLeader ?? '0'),
    isUpdated: strBoolToBool(backendComment.isUpdated ?? '0'),
  };
}

export function mapBackendCommentUserToUser({ id, username }: BackendCommentUser): User {
  return {
    uid: id,
    username: username,
    email: '',
  };
}

export function getMyReaction({ likes, dislikes }: ReactionsData, commentId: string): Reaction | undefined {
  return likes.includes(commentId) ? 'like' : dislikes.includes(commentId) ? 'dislike' : undefined;
}

export function populateReactionsForComments({ data, meta }: ApiListResult<Comment>, myVotes: ReactionsData): ApiListResult<Comment> {
  return {
    data: data.map((comment) => ({
      ...comment,
      myReaction: getMyReaction(myVotes, comment.id),
    })),
    meta,
  };
}
