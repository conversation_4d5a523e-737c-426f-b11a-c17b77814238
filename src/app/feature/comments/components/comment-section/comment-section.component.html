<div #scrollTarget id="kommentek"></div>
<h2 class="comment-title">KOMMENT</h2>
<p class="comment-count">Összesen {{ commentCount() }} komment</p>
@if (authService.currentUser(); as user) {
  <p class="comment-author">
    Hozzászólok mint <span class="comment-author-name">{{ user.username }}</span>
  </p>
  <app-comment-answer-form (onSend)="onSend($event)" [isButtonDisabled]="loadingState() === 'loading'" [isTopLevel]="true" />
} @else {
  <p class="comment-guest-text">
    A kommentek nem szerkesztett tartalmak, tartalmuk a szerzőjük álláspontját tükrözi. Mielőtt hozzászólna, kérjük, olvassa el a
    <a [routerLink]="['/felhasznalasi-feltetelek']">kommentszabályzatot</a>.
  </p>
}

@switch (loadingState()) {
  @case ('loading') {
    <p class="comment-loading">K<PERSON><PERSON>j<PERSON>k, várjon...</p>
  }

  @case ('error') {
    <p class="comment-error">Hiba történt a hozzászólás beküldésekor. Kérjük próbálja meg később</p>
  }

  @case ('submitted') {
    <app-comment-success-modal (closeModal)="loadingState.set('idle')" />
  }
}

<hr class="separator" />

<app-comment-list parentType="article" [parentId]="articleId()" />

@if (!authService.currentUser()) {
  @if (!commentCount()) {
    <!-- CTA is smaller then an empty comment list, therefore without this the CTA would hide the guest-text -->
    <div class="comment-clearfix"></div>
  }
  <div class="comment-cta">
    <app-comment-cta />
  </div>
}
