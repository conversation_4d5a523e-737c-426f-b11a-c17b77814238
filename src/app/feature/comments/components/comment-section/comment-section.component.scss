@use 'shared' as *;

:host {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin: 16px 0;
  width: 100%;
  padding: 16px;
  background-color: var(--kui-gray-250);

  @include media-breakpoint-up(md) {
    margin: 32px 0;
    padding: 32px;
  }
}

.comment {
  &-title {
    color: var(--kui-red-500);
    font-family: var(--kui-font-primary);
    font-size: 24px;
    font-weight: 800;

    @include media-breakpoint-up(md) {
      font-size: 36px;
    }
  }

  &-count {
    font-size: 18px;
    font-weight: 700;
    line-height: 26px;
  }

  &-author {
    font-size: 16px;
    line-height: 24px;

    &-name {
      font-weight: 700;
    }
  }

  app-comment-answer-form {
    margin-bottom: 16px;
  }

  &-guest-text {
    background-color: var(--kui-red-500);
    color: var(--kui-white);
    border-radius: 20px;
    padding: 16px;
    font-size: 18px;
    font-weight: 600;
    line-height: 160%;

    a {
      color: var(--kui-white);
      text-decoration: underline;
    }
  }

  &-loading,
  &-error,
  &-success {
    font-size: 16px;
    font-weight: 600;
  }

  &-error {
    color: var(--kui-red-500);
  }

  &-clearfix {
    min-height: 30px;
  }

  &-cta {
    position: relative;
    width: 100%;

    app-comment-cta {
      position: absolute;
      z-index: 2;
      transform: translate(-15px, -80%);
      width: calc(100% + 30px);
    }
  }
}

.separator {
  border-top: 1px solid var(--kui-gray-200);
  margin: 0;
}
