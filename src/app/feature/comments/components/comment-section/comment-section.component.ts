import { ChangeDetectionStrategy, Component, DestroyRef, inject, input, model, signal } from '@angular/core';
import { AuthService } from 'src/app/shared';
import { CommentAnswerFormComponent } from '../comment-answer-form/comment-answer-form.component';
import { RouterLink } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { CommentListComponent } from '../comment-list/comment-list.component';
import { CommentService } from '../../api/comment.service';
import { catchError } from 'rxjs/operators';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { throwError } from 'rxjs';
import { CommentCtaComponent } from '../comment-cta/comment-cta.component';
import { CommentSuccessModalComponent } from '../comment-success-modal/comment-success-modal.component';

@Component({
  selector: 'app-comment-section',
  imports: [CommentAnswerFormComponent, RouterLink, FormsModule, CommentListComponent, CommentCtaComponent, CommentSuccessModalComponent],
  templateUrl: './comment-section.component.html',
  styleUrl: './comment-section.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CommentSectionComponent {
  private readonly destroyRef = inject(DestroyRef);
  private readonly commentService = inject(CommentService);
  readonly authService = inject(AuthService);

  readonly commentCount = model.required<number>();
  readonly articleId = input.required<string>();

  readonly loadingState = signal<'idle' | 'loading' | 'error' | 'submitted'>('idle');

  onSend(text: string): void {
    if (!this.authService.currentUser()) {
      this.commentService.redirectToLogin();
      return;
    }

    this.loadingState.set('loading');
    this.commentService
      .add('article', this.articleId(), text)
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        catchError((err) => {
          console.error('Failed to update comment', err);
          this.loadingState.set('error');
          return throwError(() => err);
        })
      )
      .subscribe(() => this.loadingState.set('submitted'));
  }
}
