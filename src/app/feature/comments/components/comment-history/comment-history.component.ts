import { ChangeDetectionStrategy, Component, DestroyRef, inject, input, OnInit, output, signal } from '@angular/core';
import { CommentService } from '../../api/comment.service';
import { HistoricalComment } from '../../api/comment.definitions';
import { IconComponent, Comment } from '@trendency/kesma-ui';
import { PublishDatePipe } from '@trendency/kesma-core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';

@Component({
  selector: 'app-comment-history',
  imports: [IconComponent, PublishDatePipe],
  templateUrl: './comment-history.component.html',
  styleUrl: './comment-history.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CommentHistoryComponent implements OnInit {
  private readonly commentService = inject(CommentService);
  private readonly destroyRef = inject(DestroyRef);

  readonly comment = input.required<Comment>();

  readonly closeEvent = output<void>();

  readonly history = signal<HistoricalComment | undefined>(undefined);
  readonly loadingState = signal<'idle' | 'loading' | 'error'>('idle');

  ngOnInit(): void {
    this.loadingState.set('loading');
    this.commentService
      .history(this.comment().id)
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        catchError((err) => {
          console.error('Failed to update comment', err);
          this.loadingState.set('error');
          return throwError(() => err);
        })
      )
      .subscribe((result) => {
        this.loadingState.set('idle');
        this.history.set(result);
      });
  }

  onClose(): void {
    this.closeEvent.emit();
  }
}
