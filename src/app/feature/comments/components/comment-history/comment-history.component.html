<div class="title">
  <h2>Szerkesz<PERSON><PERSON></h2>
  <kesma-icon name="" [size]="24" (click)="onClose()" />
</div>

<div class="separator"></div>

@switch (loadingState()) {
  @case ('loading') {
    <div class="loading">
      <p>Betöltés...</p>
    </div>
  }

  @case ('error') {
    <div class="error">
      <p>Hiba történt az előzmények betöltésekor.</p>
    </div>
  }

  @default {
    <div class="original col">
      <h3><PERSON><PERSON><PERSON> hozzászólás</h3>
      <p class="timestamp">{{ history()?.createdAt | publishDate }}</p>
      <p class="content">{{ history()?.commentText }}</p>
    </div>

    <div class="separator"></div>

    <div class="latest col">
      <i class="icon icon-chevron-down"></i>
      <p class="timestamp">Utoljára módosítva: {{ comment().updatedAt | publishDate }}</p>
      <p class="content">{{ comment().text }}</p>
    </div>
  }
}
