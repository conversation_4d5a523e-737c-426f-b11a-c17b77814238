import { ChangeDetectionStrategy, Component, output } from '@angular/core';
import { BorsSimpleButtonComponent, PopupComponent } from '../../../../shared';

@Component({
  selector: 'app-comment-success-modal',
  imports: [PopupComponent, BorsSimpleButtonComponent],
  templateUrl: './comment-success-modal.component.html',
  styleUrl: './comment-success-modal.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CommentSuccessModalComponent {
  readonly closeModal = output<void>();
}
