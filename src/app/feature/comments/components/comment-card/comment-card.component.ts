import { ChangeDetectionStrategy, Component, computed, DestroyRef, inject, input, model, signal } from '@angular/core';
import { Reaction } from '../../api/comment.definitions';
import { CommentAnswerFormComponent } from '../comment-answer-form/comment-answer-form.component';
import { Comment, IconComponent } from '@trendency/kesma-ui';
import { CommentService } from '../../api/comment.service';
import { AuthService, BorsSimpleButtonComponent } from '../../../../shared';
import { catchError, finalize } from 'rxjs/operators';
import { PublishDatePipe } from '@trendency/kesma-core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { throwError } from 'rxjs';
import { User } from '@trendency/kesma-ui/lib/definitions/user.definitions';
import { CommentSuccessModalComponent } from '../comment-success-modal/comment-success-modal.component';

@Component({
  selector: 'app-comment-card',
  imports: [CommentAnswerFormComponent, IconComponent, PublishDatePipe, BorsSimpleButtonComponent, CommentSuccessModalComponent],
  templateUrl: './comment-card.component.html',
  styleUrl: './comment-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CommentCardComponent {
  private readonly authService = inject(AuthService);
  private readonly commentService = inject(CommentService);
  private readonly destroyRef = inject(DestroyRef);

  readonly comment = model.required<Comment>();
  readonly isChildrenOpen = model.required<boolean>();

  readonly parentAuthor = input<User | undefined>(undefined);

  readonly state = signal<'idle' | 'answering' | 'editing' | 'history' | 'success'>('idle');
  readonly isOptionsOpen = signal<boolean>(false);
  readonly isLoading = signal<boolean>(false);
  readonly error = signal<string | undefined>(undefined);

  readonly isGuest = computed(() => !this.authService.currentUser());

  handleSend(text: string): void {
    this.isLoading.set(true);
    this.error.set(undefined);

    if (this.state() !== 'answering') {
      this.commentService
        .update(this.comment().id, text)
        .pipe(
          takeUntilDestroyed(this.destroyRef),
          finalize(() => this.isLoading.set(false)),
          catchError((err) => {
            console.error('Failed to update comment', err);
            this.error.set('Szerkesztés sikertelen. Kérjük próbálja újra később.');
            return throwError(() => err);
          })
        )
        .subscribe(() => {
          this.state.set('idle');
        });

      return;
    }

    this.commentService
      .add('comment', this.comment().id, text)
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        finalize(() => this.isLoading.set(false)),
        catchError((err) => {
          console.error('Failed to create comment', err);
          this.error.set('Válasz beküldése sikertelen. Kérjük próbálja újra később.');
          return throwError(() => err);
        })
      )
      .subscribe(() => {
        this.state.set('success');
        this.isChildrenOpen.set(false);
      });
  }

  react(reaction: Reaction): void {
    if (!this.authService.currentUser()) {
      this.commentService.redirectToLogin();
      return;
    }

    this.isLoading.set(true);
    this.error.set(undefined);

    if (this.comment().myReaction === reaction) {
      // User clicked on the same reaction again, so we need to clear it
      this.commentService
        .clearReaction(this.comment().id)
        .pipe(
          takeUntilDestroyed(this.destroyRef),
          finalize(() => this.isLoading.set(false)),
          catchError((err) => {
            console.error('Failed to remove reaction', err);
            this.error.set('Értékelés törlése sikertelen. Kérjük próbálja újra később.');
            return throwError(() => err);
          })
        )
        .subscribe(() =>
          this.comment.update((comment) => ({
            ...comment,
            likeCount: reaction === 'like' ? comment.likeCount - 1 : comment.likeCount,
            dislikeCount: reaction === 'dislike' ? comment.dislikeCount - 1 : comment.dislikeCount,
            myReaction: undefined,
          }))
        );

      return;
    }

    // Switching reactions are handled on BE (e.g. previously liked and now disliked), but we need to update the UI
    this.commentService
      .react(this.comment().id, reaction)
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        finalize(() => this.isLoading.set(false)),
        catchError((err) => {
          console.error('Failed to add reaction', err);
          this.error.set('Értékelés sikertelen. Kérjük próbálja újra később.');
          return throwError(() => err);
        })
      )
      .subscribe(() => {
        this.comment.update((comment) => {
          comment[`${reaction}Count`] += 1;

          if (comment.myReaction) {
            // User switched reactions, so we need to decrease the previous reaction count
            comment[`${comment.myReaction}Count`] -= 1;
          }

          comment.myReaction = reaction;
          return comment;
        });
      });
  }

  report(): void {
    this.isLoading.set(true);
    this.error.set(undefined);
    this.commentService
      .report(this.comment().id)
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        finalize(() => this.isLoading.set(false)),
        catchError((err) => {
          console.error('Failed to report comment', err);
          this.error.set('Jelentés sikertelen. Kérjük próbálja újra később.');
          return throwError(() => err);
        })
      )
      .subscribe();
  }

  toggleChildren(): void {
    this.isChildrenOpen.update((isOpen) => !isOpen);
  }

  toggleOptions(): void {
    this.isOptionsOpen.update((isOpen) => !isOpen);
  }

  onOptionsVisibleChange(isVisible: boolean): void {
    if (isVisible) {
      return; // We are only watching for "close" event
    }

    this.isOptionsOpen.set(false);
  }

  openAnswerForm(): void {
    if (!this.authService.currentUser()) {
      this.commentService.redirectToLogin();
      return;
    }
    this.state.set('answering');
  }
}
