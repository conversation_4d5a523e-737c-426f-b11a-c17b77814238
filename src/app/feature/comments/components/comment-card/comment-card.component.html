<div class="header">
  <div class="header-author">
    {{ comment().author.username }}
    @if (parentAuthor(); as parentAuthor) {
      <kesma-icon name="reply-to" [size]="19" class="header-author-icon" />
      <span class="header-author-parent">{{ parentAuthor.username }}</span>
    }
  </div>
  <div class="header-date">{{ comment().createdAt | publishDate }}</div>
</div>

<p class="content">{{ comment().text }}</p>

@if (state() === 'answering') {
  <app-comment-answer-form (onSend)="handleSend($event)" (onCancel)="this.state.set('idle')" />
}

@if (state() === 'idle') {
  <div class="action">
    <div class="action-group">
      <app-simple-button color="link" icon="reply" (click)="openAnswerForm()" [disabled]="isLoading()" [useKesmaIcon]="true" [kesmaIconSize]="15"
        >V<PERSON>lasz erre</app-simple-button
      >
    </div>

    <div class="action-group">
      <app-simple-button
        [color]="comment().myReaction === 'like' ? 'primary' : 'secondary'"
        round="circle"
        icon="upvote"
        iconPosition="center"
        [useKesmaIcon]="true"
        [kesmaIconSize]="20"
        (click)="react('like')"
        [disabled]="isLoading()"
      >
      </app-simple-button>
      {{ comment().likeCount }}

      <app-simple-button
        [color]="comment().myReaction === 'dislike' ? 'primary' : 'secondary'"
        round="circle"
        icon="downvote"
        iconPosition="center"
        [useKesmaIcon]="true"
        [kesmaIconSize]="20"
        (click)="react('dislike')"
        [disabled]="isLoading()"
      >
      </app-simple-button>
      {{ comment().dislikeCount }}
    </div>
  </div>
}

@if (error()) {
  <p class="error">{{ error() }}</p>
}

@if (comment().answerCount > 0 && !isGuest()) {
  <app-simple-button class="children-button" color="link" (click)="toggleChildren()" [disabled]="isLoading()" [class.icon-rotate]="!isChildrenOpen()">
    <kesma-icon name="chevron" [size]="16" />
    <span class="children-count">{{ comment().answerCount }} válasz</span>
  </app-simple-button>
}

@if (state() === 'success') {
  <app-comment-success-modal (closeModal)="state.set('idle')" />
}
