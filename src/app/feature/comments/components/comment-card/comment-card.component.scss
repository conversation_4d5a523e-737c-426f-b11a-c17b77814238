@use 'shared' as *;

:host {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.header {
  &-author {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 16px;
    font-weight: 700;
    line-height: 24px;

    &-icon {
      color: var(--kui-red-500);
    }

    &-parent {
      color: var(--kui-black-600);
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
    }
  }

  &-date {
    font-size: 12px;
    font-weight: 400;
    line-height: 18px;
  }
}

.content {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;

  @include media-breakpoint-up(md) {
    font-size: 18px;
    line-height: 26px;
  }
}

.action {
  display: flex;
  justify-content: space-between;
  align-items: center;

  &-group {
    display: flex;
    width: fit-content;
    gap: 10px;
    align-items: center;
    font-size: 12px;
    font-weight: 600;
    line-height: 15px;
  }
}

.children-button.icon-rotate::ng-deep kesma-icon {
  transform: rotate(180deg);
}

app-simple-button {
  margin: 0;

  &::ng-deep {
    button.btn-primary {
      padding: 0 10px;
    }

    button.btn-secondary {
      padding: 0 8px;
    }

    button.btn-link {
      font-size: 12px;
      font-weight: 600;
      line-height: 15px;
      display: flex;

      kesma-icon {
        display: inline-block;
        margin: -3px 5px 0 0;
        transition: all 0.2s ease-in-out;
        transform-origin: 50% 40%;
      }
    }
  }
}
