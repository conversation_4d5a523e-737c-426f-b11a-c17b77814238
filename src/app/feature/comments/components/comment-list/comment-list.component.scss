@use 'shared' as *;

:host {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap-reverse;
  width: 100%;
  font-size: 14px;

  &-sort {
    display: flex;
    align-items: center;

    &-select {
      &::ng-deep .ng-select-container {
        border: none;
        font-family: var(--kui-font-secondary);
        font-weight: 700;
        line-height: 16px;
        margin-top: -3px;
        background-color: transparent;
        padding: 0;
        box-shadow: none !important;

        div.ng-value-container {
          overflow: visible;

          div.ng-value {
            overflow: visible;
          }
        }

        .ng-arrow-wrapper {
          background-image: url(/assets/images/icons/arrow-right.svg);
          transition: rotate 300ms;
          width: 40px;
          height: 40px;
          right: 5px;
          rotate: 90deg;
        }
        .ng-arrow {
          border: none;
        }
      }

      &.ng-select-opened::ng-deep {
        .ng-arrow-wrapper {
          rotate: -90deg;
        }
      }
    }

    &-caret {
      transition: transform 0.3s ease;
      transform-origin: 50% 40%;

      &.rotate {
        transform: rotate(180deg);
      }
    }
  }

  &-refresh {
    margin: 0;
  }
}

.loading,
.error,
.success {
  font-size: 16px;
  font-weight: 600;
}

.error {
  color: var(--kui-red-500);
}

.answer {
  padding: 16px 0 16px 32px;
  border-radius: 20px;
  border-left: 2px solid var(--kui-black-600);
}

.comment-container {
  display: flex;
  flex-direction: column;
  gap: 16px;

  .indent {
    margin-left: 32px;
  }
}
