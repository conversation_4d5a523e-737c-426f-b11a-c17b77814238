import { ChangeDetectionStrategy, Component, computed, DestroyRef, inject, input, model, OnInit, signal } from '@angular/core';
import { CommentService } from '../../api/comment.service';
import { ApiListResult, ApiResponseMetaList, Comment, User } from '@trendency/kesma-ui';
import { CommentableType, CommentOrder } from '../../api/comment.definitions';
import { of } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { FormsModule } from '@angular/forms';
import { CommentCardComponent } from '../comment-card/comment-card.component';
import { AuthService, BorsSimpleButtonComponent, LabelValue } from '../../../../shared';
import { NgSelectComponent } from '@ng-select/ng-select';

const COMMENTS_PER_PAGE = 10;

@Component({
  selector: 'app-comment-list',
  imports: [CommentCardComponent, FormsModule, BorsSimpleButtonComponent, NgSelectComponent],
  templateUrl: './comment-list.component.html',
  styleUrl: './comment-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CommentListComponent implements OnInit {
  private readonly commentService = inject(CommentService);
  private readonly destroyRef = inject(DestroyRef);

  readonly currentUser = inject(AuthService).currentUser;

  readonly orderItems: LabelValue<CommentOrder>[] = [
    {
      label: 'Legújabb kommentek elől',
      value: 'latest',
    },
    {
      label: 'Legrégebbi kommentek elől',
      value: 'oldest',
    },
    {
      label: 'Legnépszerűbb kommentek elől',
      value: 'most-popular',
    },
  ];

  readonly order = model<LabelValue<CommentOrder>>(this.orderItems[0]);

  readonly level = input<number>(0);
  readonly parentType = input<CommentableType>('comment');
  readonly parentAuthor = input<User | undefined>(undefined);
  readonly parentId = input.required<string>();

  readonly parentIdChanges$ = toObservable(this.parentId).pipe(takeUntilDestroyed());

  readonly page = signal(0);
  readonly loadingState = signal<'idle' | 'loading' | 'error'>('idle');
  readonly response = signal<ApiListResult<Comment> | undefined>(undefined);
  readonly isSelectOpen = signal<boolean>(false);
  readonly comments = computed(() => this.response()?.data ?? []);
  readonly hasMore = computed(() => (this.response()?.meta.limitable?.pageMax ?? 0) > (this.response()?.meta.limitable?.pageCurrent ?? 0));
  readonly openChildren = signal<Record<string, boolean>>({});

  ngOnInit(): void {
    this.parentIdChanges$.subscribe(() => this.loadComments(0));
  }

  loadComments(page: number): void {
    this.loadingState.set('loading');
    this.commentService
      .list(this.parentType(), this.parentId(), { page_limit: page, rowCount_limit: COMMENTS_PER_PAGE, order: this.order().value })
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        catchError((error) => {
          this.loadingState.set('error');
          console.error('Failed to load comments.', error);
          return of({ data: [], meta: { limitable: { rowAllCount: 0 } } as ApiResponseMetaList } as ApiListResult<Comment>);
        })
      )
      .subscribe(({ data, meta }) => {
        this.loadingState.set('idle');
        if (page === 0) {
          this.response.set({ data, meta });
          this.openChildren.set({});
          return;
        }

        this.response.set({
          data: [...this.comments(), ...data],
          meta,
        });
      });
  }

  loadMore(): void {
    if (!this.hasMore()) {
      return;
    }
    this.page.update((page) => ++page);
    this.loadComments(this.page());
  }

  setCommentOrder(newOrder: LabelValue<CommentOrder>): void {
    this.order.set(newOrder);
    this.loadComments(0);
  }

  toggleSelect(): void {
    this.isSelectOpen.update((isOpen) => !isOpen);
  }

  setOpenChildren(id: string, isOpen: boolean): void {
    this.openChildren.update((children) => ({ ...children, [id]: isOpen }));
  }
}
