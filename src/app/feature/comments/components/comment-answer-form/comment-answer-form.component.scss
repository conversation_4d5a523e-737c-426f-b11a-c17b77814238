@use 'shared' as *;

:host {
  display: block;
  width: 100%;
}

.comment-form {
  &-wrapper {
    width: 100%;
    position: relative;
    margin-bottom: 16px;
  }

  &-input {
    padding: 16px;
    border-radius: 20px;
    border: 3px solid var(--kui-gray-300);
    background: var(--kui-white);
    width: 100%;
    height: 140px;
    font-size: 18px;
    font-weight: 400;
    line-height: 26px;

    @include media-breakpoint-down(sm) {
      font-size: 16px;
      line-height: 24px;
    }

    &::placeholder {
      color: var(--kui-black-600);
      font-size: 18px;
      font-weight: 400;
      line-height: 26px;

      @include media-breakpoint-down(sm) {
        font-size: 16px;
        line-height: 24px;
      }
    }
  }

  &-error {
    display: block;
    margin-bottom: 16px;
    color: var(--kui-red-500);
  }

  &-counter {
    position: absolute;
    bottom: 16px;
    right: 16px;
    color: var(--kui-black-600);
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
  }

  &-buttons {
    display: flex;
    gap: 10px;

    app-simple-button {
      margin: 0;
    }
  }
}
