<form class="comment-form">
  <div class="comment-form-wrapper">
    <textarea class="comment-form-input" [formControl]="control" [placeholder]="placeholder()"></textarea>
    <div class="comment-form-counter">Maxim<PERSON>lis karakterszám: {{ control.value.length }}/{{ MAX_LENGTH }}</div>
  </div>

  @if (control.hasError('forbiddenLink')) {
    <small class="comment-form-error">Link nem helyezhető el a komment szövegében. Kérjük törölje, és próbálja elküldeni újra.</small>
  }

  @if (control.hasError('minlength')) {
    <small class="comment-form-error">A hozzászólás beküldéséhez írjon legalább {{ MIN_LENGTH }} karaktert.</small>
  }

  <div class="comment-form-buttons">
    <app-simple-button
      [color]="isTopLevel() ? 'dark' : 'primary'"
      [isSubmit]="true"
      (click)="handleSubmit()"
      [disabled]="isButtonDisabled() || control.invalid"
      round="round"
    >
      {{ isTopLevel() ? 'Hozzászólás küldése' : 'Válasz küldése' }}
    </app-simple-button>
    @if (!isTopLevel()) {
      <app-simple-button color="secondary" (click)="handleCancel()" round="round">Mégse</app-simple-button>
    }
  </div>
</form>
