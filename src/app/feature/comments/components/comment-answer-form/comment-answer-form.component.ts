import { ChangeDetectionStrategy, Component, inject, Input, input, output } from '@angular/core';
import { FormsModule, NonNullableFormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { BorsSimpleButtonComponent } from '../../../../shared';
import { forbiddenLinkValidator } from '../../validators/forbidden-link.validator';

@Component({
  selector: 'app-comment-answer-form',
  imports: [FormsModule, ReactiveFormsModule, BorsSimpleButtonComponent],
  templateUrl: './comment-answer-form.component.html',
  styleUrl: './comment-answer-form.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CommentAnswerFormComponent {
  readonly MAX_LENGTH = 1000;
  readonly MIN_LENGTH = 6;
  readonly control = inject(NonNullableFormBuilder).control('', [
    Validators.required,
    Validators.minLength(this.MIN_LENGTH),
    Validators.maxLength(this.MAX_LENGTH),
    forbiddenLinkValidator,
  ]);

  readonly placeholder = input<string>('Hozzászólás írása');
  readonly isButtonDisabled = input<boolean>(false);
  readonly isTopLevel = input(false);
  readonly onSend = output<string>();
  readonly onCancel = output<void>();

  @Input()
  set initialValue(value: string) {
    this.control.setValue(value);
  }

  handleSubmit(): void {
    this.onSend.emit(this.control.value);
    this.control.reset();
  }

  handleCancel(): void {
    this.onCancel.emit();
    this.control.reset();
  }
}
