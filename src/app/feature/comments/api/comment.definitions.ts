export type HistoricalComment = Readonly<{
  id: string;
  createdAt: string;
  text: string;
}>;

export type Reaction = 'like' | 'dislike';

export type CommentOrder = 'latest' | 'oldest' | 'most-popular';

export type CommentableType = 'article' | 'comment';

export interface CommentRequestParams {
  page_limit?: number;
  rowCount_limit?: number;
  order?: CommentOrder;
}

export type BackendArticleSocial = Readonly<{
  commentCount: number;
  dislikeCount: number;
  likeCount: number;
  disableLikesAndDislikes: boolean;
  disableComments: boolean;
}>;
