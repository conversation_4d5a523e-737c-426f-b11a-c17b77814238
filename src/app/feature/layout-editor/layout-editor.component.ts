import { ChangeDetectionStrategy, Component, inject, OnInit } from '@angular/core';
import { AdvertisementPlaceholderComponent, LayoutEditorComponent as KesmaLayoutEditor, LayoutElementContentType, LayoutPageType } from '@trendency/kesma-ui';
import { SeoService, UtilService } from '@trendency/kesma-core';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { NgSelectModule } from '@ng-select/ng-select';
import { LayoutComponent } from '../layout/components/layout/layout.component';
import { NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'app-layout-editor',
  templateUrl: './layout-editor.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgSelectModule, DragDropModule, LayoutComponent, NgTemplateOutlet, KesmaLayoutEditor, AdvertisementPlaceholderComponent],
})
export class LayoutEditorComponent implements OnInit {
  readonly utilService = inject(UtilService);
  readonly isBrowser = this.utilService.isBrowser();
  readonly seoService = inject(SeoService);
  readonly LayoutElementContentType = LayoutElementContentType;
  readonly LayoutPageType = LayoutPageType;

  ngOnInit(): void {
    this.seoService.setMetaData({
      robots: 'noindex, nofollow',
    });
  }
}
