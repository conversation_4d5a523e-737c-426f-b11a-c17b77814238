import { ActivatedRouteSnapshot, ResolveFn, Router } from '@angular/router';
import { Observable, throwError } from 'rxjs';
import { SearchData } from './search.definitions';
import { inject } from '@angular/core';
import { ApiResult, ArticleSearchResult, RedirectService, searchResultToArticleCard, Tag } from '@trendency/kesma-ui';
import { ApiService, calculateFilters } from '../../shared';
import { catchError, map, switchMap } from 'rxjs/operators';
import { format } from 'date-fns';
import { ReqService } from '@trendency/kesma-core';
import { HttpParams } from '@angular/common/http';

export const MAX_RESULTS_PER_PAGE = 10;

export const SearchResolver: ResolveFn<any> = (route: ActivatedRouteSnapshot): Observable<SearchData> => {
  const router = inject(Router);
  const apiService = inject(ApiService);
  const redirectService = inject(RedirectService);
  const reqService = inject(ReqService);

  let filters = calculateFilters(route.queryParams);
  if ('from_date' in filters && !('to_date' in filters)) {
    filters = { ...filters, to_date: format(new Date(), 'yyyy-MM-dd') };
  }

  const currentPage = route.queryParams['page'] ? parseInt(route.queryParams['page'], 10) - 1 : 0;
  const globalFilter = (route.queryParams['global_filter'] || route.queryParams['tagSlugs[]']) ?? undefined;

  const handleSearch = (updatedFilters: Record<string, any>, globalFilterValue: string | string[]): Observable<SearchData> =>
    apiService.searchArticles(currentPage, MAX_RESULTS_PER_PAGE, updatedFilters).pipe(
      map(({ data, meta }) => {
        if (redirectService.shouldBeRedirect(currentPage, data)) {
          redirectService.redirectOldUrl(`kereses`, false, 302);
        }
        return {
          articles: data.map((sr: ArticleSearchResult) => searchResultToArticleCard(sr)),
          limitable: meta?.limitable,
          globalFilter: globalFilterValue,
        } as SearchData;
      }),
      catchError((err) => {
        router.navigate(['/404'], { skipLocationChange: true }).then();
        return throwError(() => err);
      })
    );

  if (!route.queryParams['tagSlugs[]']) {
    return handleSearch(filters, globalFilter);
  } else {
    const tagSlugs = route.queryParamMap.getAll('tagSlugs[]');
    let params = new HttpParams();
    tagSlugs.forEach((tag) => {
      params = params.append('global_filter', tag);
      params = params.append('rowCount_limit', 100);
    });

    return reqService.get<ApiResult<Tag[]>>(`/source/content-group/tags`, { params }).pipe(
      switchMap(({ data }: { data: Tag[] }) => {
        let tagSlugs: string[] = [];
        if (data.length < 1) {
          tagSlugs = route.queryParamMap.getAll('tagSlugs[]');
        } else {
          tagSlugs = data.map((tag: Tag) => tag.slug);
        }
        const newFilters = { ...filters, 'tagSlugs[]': tagSlugs };
        return handleSearch(newFilters, route.queryParams['tagSlugs[]']);
      })
    );
  }
};
