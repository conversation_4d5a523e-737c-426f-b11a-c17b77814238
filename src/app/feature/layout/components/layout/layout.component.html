@if (vm(); as vm) {
  <kesma-layout
    [breakingNews]="vm.breakingNews"
    [configuration]="vm.configuration"
    [layoutType]="vm.layoutType"
    [structure]="vm.structure"
    [adPageType]="vm.adPageType"
    [blockTitleRef]="blockTitles"
    [contentComponentsRef]="contentComponents"
    [contentComponentWrapperRef]="contentComponentsWrapper()"
    [contentComponentInnerWrapperRef]="contentComponentsInnerWrapper()"
    [blockTitleWrapperRef]="blockTitleWrapper()"
    [editorFrameSize]="editorFrameSize()"
  >
  </kesma-layout>
}

<ng-template #blockTitles let-layoutType="layoutType" let-layoutElement="layoutElement">
  <app-block-title-row [data]="layoutElement.blockTitle || (isInIframe() && { text: 'Adja meg a blokk címet!' })"></app-block-title-row>
</ng-template>

<ng-template
  #contentComponents
  let-layoutElement="layoutElement"
  let-backgroundColor="backgroundColor"
  let-desktopWidth="desktopWidth"
  let-index="index"
  let-extractor="extractor"
>
  @if (layoutElement?.config || layoutElement?.configurable === false) {
    @switch (layoutElement.contentType) {
      @case (LayoutElementContentType.Ad) {
        @if (layoutElement.ad) {
          <kesma-advertisement-adocean
            [ad]="layoutElement.ad"
            [isHidden]="layoutElement.contentType !== LayoutElementContentType.Ad && !layoutElement.ad"
          ></kesma-advertisement-adocean>
        }
      }
      @case (LayoutElementContentType.Article) {
        @if (layoutElement.extractorData?.[index]; as data) {
          @let isPriorityContent = (isMobile && !!data.priorityContentMobile) || (!isMobile && !!data.priorityContentDesktop);
          <app-article-card
            [data]="data"
            [desktopWidth]="desktopWidth"
            [hasLeftBorder]="layoutElement?.withVerticalSeparator"
            [isExclusive]="data?.isExclusive"
            [isBreaking]="data?.isBreaking"
            [isSidebar]="layoutType() === LayoutPageType.SIDEBAR"
            [styleId]="layoutElement.styleId"
            [fetchpriority]="isPriorityContent ? 'high' : 'auto'"
            [useEagerLoad]="isPriorityContent"
            [hasBlockBackground]="!!backgroundColor"
          ></app-article-card>
        }
      }
      @case (LayoutElementContentType.Opinion) {
        @if (layoutElement.extractorData?.[index]; as data) {
          <app-article-card
            [data]="data"
            [desktopWidth]="desktopWidth"
            [isOpinion]="true"
            [hasLeftBorder]="layoutElement?.withVerticalSeparator"
            [isSidebar]="layoutType() === LayoutPageType.SIDEBAR"
            [styleId]="layoutElement.styleId"
            [hasBlockBackground]="!!backgroundColor"
          ></app-article-card>
        }
      }
      @case (LayoutElementContentType.PrBlock) {
        @if (layoutElement.extractorData?.[index]; as data) {
          <app-article-card
            [data]="data"
            [desktopWidth]="desktopWidth"
            [isExclusive]="data?.isExclusive"
            [isBreaking]="data?.isBreaking"
            [hasLeftBorder]="layoutElement?.withVerticalSeparator"
            [isSidebar]="layoutType() === LayoutPageType.SIDEBAR"
            [hasSponsorship]="true"
            [styleId]="layoutElement.styleId"
            [hasBlockBackground]="!!backgroundColor"
          ></app-article-card>
        }
      }
      @case (LayoutElementContentType.Gallery) {
        @for (gallery of layoutElement.extractorData; track gallery) {
          @if (gallery) {
            <app-gallery-list [data]="gallery" [desktopWidth]="desktopWidth" [isSidebar]="layoutType() === LayoutPageType.SIDEBAR"></app-gallery-list>
          }
        }
      }
      @case (LayoutElementContentType.Wysiwyg) {
        @if (layoutElement.extractorData; as data) {
          <app-wysiwyg-box [htmlArray]="data"></app-wysiwyg-box>
        }
      }
      @case (LayoutElementContentType.Dossier) {
        @if (layoutElement.extractorData?.[index]; as data) {
          <app-dossier-card [data]="data" [sponsoredData]="layoutElement.extractorData?.[0]" [styleId]="DossierCardType.Layout"></app-dossier-card>
        }
      }
      @case (LayoutElementContentType.Image) {
        @if (layoutElement.extractorData; as data) {
          <app-image [data]="data"></app-image>
        }
      }
      @case (LayoutElementContentType.MOST_VIEWED) {
        @if (layoutElement.extractorData; as data) {
          <app-most-viewed [data]="data"></app-most-viewed>
        }
      }
      @case (LayoutElementContentType.Vote) {
        @if (layoutElement.extractorData; as extractorData) {
          @if ((votingCache[extractorData?.data?.id] | async) || extractorData; as voteData) {
            <app-voting
              (vote)="onVotingSubmit($event, voteData)"
              [data]="voteData?.data"
              [voteId]="voteData.votedId"
              [desktopWidth]="desktopWidth"
              [isSidebar]="layoutType() === LayoutPageType.SIDEBAR"
            />
          }
        }
      }
      @case (LayoutElementContentType.HtmlEmbed) {
        @if (index === 0 && layoutElement?.config?.htmlContent; as data) {
          <kesma-html-embed [data]="data"></kesma-html-embed>
        }
      }
      @case (LayoutElementContentType.ASTRONET_JOSLAS) {
        <app-astronet-fortune></app-astronet-fortune>
      }
      @case (LayoutElementContentType.FreshBlock) {
        @if (layoutElement.extractorData?.[index]; as data) {
          <app-fresh-article-card [data]="data"></app-fresh-article-card>
        }
      }
      @case (LayoutElementContentType.ASTRONET_HOROSZKOP) {
        <app-astronet-horoscope></app-astronet-horoscope>
      }
      @case (LayoutElementContentType.NewsletterBlock) {
        <app-newsletter-block [desktopWidth]="desktopWidth" [isSidebar]="layoutType() === LayoutPageType.SIDEBAR"></app-newsletter-block>
      }
      @case (LayoutElementContentType.BrandingBoxEx) {
        @if (layoutElement?.brand) {
          <app-branding-box-ex [brand]="layoutElement.brand"></app-branding-box-ex>
        }
      }
      @case (LayoutElementContentType.ARTICLES_WITH_VIDEO_CONTENT) {
        @if (layoutElement.extractorData; as data) {
          <app-video-articles [data]="data"></app-video-articles>
        }
      }
      @case (LayoutElementContentType.STAR_BIRTHS) {
        @if (layoutElement.extractorData; as data) {
          <app-star-births [data]="data" />
        }
      }
      @case (LayoutElementContentType.COLUMN_BLOCK) {
        @if (layoutElement.extractorData; as data) {
          <app-column-block
            [data]="data"
            [desktopWidth]="desktopWidth"
            [columnTitle]="layoutElement.config?.autoFill?.filterColumns?.[0]?.title"
          ></app-column-block>
        }
      }
      @case (LayoutElementContentType.IngatlanbazarSearch) {
        <kesma-real-estate-bazaar-search-block
          [showBudapestLocations]="layoutElement.config.showBudapestLocations"
          [showCountyLocations]="layoutElement.config.showBudapestLocations"
          [showOtherLocations]="layoutElement.config.showOtherLocations"
          [showNewBuildButton]="layoutElement.config.showNewBuildButton"
          [showAdvertiseButton]="layoutElement.config.showAdvertiseButton"
          [defaultLocation]="layoutElement.config.defaultLocation"
          [defaultType]="layoutElement.config.defaultType"
          [utmSource]="layoutElement.config.utmSource"
        >
        </kesma-real-estate-bazaar-search-block>
      }
      @case (LayoutElementContentType.TOP_TEN_TAGS) {
        @if (index === 0 && layoutElement.extractorData; as tags) {
          <app-top-ten-box title="TOP 10" path="cimke" [data]="tags" />
        }
      }
      @case (LayoutElementContentType.LATEST_AND_MOST_READ_ARTICLES) {
        @if (layoutElement.extractorData; as data) {
          <app-latest-and-most-read-articles [data]="data" />
        }
      }
      @case (LayoutElementContentType.MinuteToMinute) {
        @if (layoutElement.extractorData?.[index]; as data) {
          <app-minute-by-minute [data]="data" />
        }
      }
      @case (LayoutElementContentType.SUB_COLUMNS) {
        @if (layoutElement?.config?.selectedColumns; as data) {
          <app-sub-columns-box [data]="data" />
        }
      }
      @case (LayoutElementContentType.ASTRONET_BRANDING_BOX) {
        <app-astronet-branding-box [brand]="layoutElement.brand" [description]="layoutElement?.config?.description" />
      }
      @case (LayoutElementContentType.ASTRONET_COLUMNS) {
        <app-astronet-columns [description]="layoutElement?.config?.description" />
      }
      @case (LayoutElementContentType.TOPIC_SUGGESTION) {
        <app-topic-suggestion></app-topic-suggestion>
      }
      @case (LayoutElementContentType.TOP_COMMENTED_ARTICLES) {
        @if (layoutElement.extractorData; as data) {
          <app-top-commented-articles [data]="data" />
        }
      }
    }
  }
  @if (layoutElement.contentType === LayoutElementContentType.IngatlanbazarConfigurable) {
    <kesma-real-estate-bazaar-block
      (initEvent)="handleRealEstateInitEvent()"
      [showHeader]="layoutElement.showHeader"
      [itemsToShow]="layoutElement.itemsToShow"
      [data]="vm().realEstateData"
    >
    </kesma-real-estate-bazaar-block>
  }
  @if (layoutElement.contentType === LayoutElementContentType.INGATLANBAZAR) {
    <kesma-real-estate-bazaar-block
      (initEvent)="handleRealEstateInitEvent()"
      [showHeader]="true"
      [itemsToShow]="1"
      [data]="vm().realEstateData"
    ></kesma-real-estate-bazaar-block>
  }
  @if (layoutElement.contentType === LayoutElementContentType.DID_YOU_KNOW) {
    <app-variable-sponsored-did-you-know-wrapper [id]="layoutElement.config?.selectedDidYouKnowBox?.[0]?.id"></app-variable-sponsored-did-you-know-wrapper>
  }
  @if (layoutElement.contentType === LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX) {
    <kesma-sponsored-box [data]="layoutElement.config"></kesma-sponsored-box>
  }
</ng-template>
