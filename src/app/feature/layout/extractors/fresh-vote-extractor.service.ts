import { inject, Injectable } from '@angular/core';
import {
  backendVotingDataToVotingData,
  DataExtractorFunction,
  LayoutDataExtractorService,
  LayoutElementContent,
  LayoutElementContentConfigurationVote,
  VoteDataWithAnswer,
  VoteService,
} from '@trendency/kesma-ui';

@Injectable()
export class FreshVoteExtractor implements LayoutDataExtractorService<VoteDataWithAnswer | undefined> {
  private readonly voteService = inject(VoteService);

  extractData: DataExtractorFunction<VoteDataWithAnswer | undefined> = (element: LayoutElementContent) => {
    const conf = element.config as LayoutElementContentConfigurationVote;

    const selectedVote = conf?.selectedVote?.data;
    if (!selectedVote) {
      return;
    }

    const voteData = this.voteService.getVoteData(backendVotingDataToVotingData(selectedVote));

    this.voteService.setVoteCache(voteData);

    return {
      data: voteData,
      meta: {
        extractedBy: FreshVoteExtractor.name,
      },
    };
  };
}
