import { ResolveFn, Router } from '@angular/router';
import { inject } from '@angular/core';
import { ApiService } from '../../shared';
import { catchError, map } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { ArticleCard, Tag } from '@trendency/kesma-ui';

export const TopCommentedPageResolver: ResolveFn<ArticleCard[]> = () => {
  const apiService = inject(ApiService);
  const router = inject(Router);
  return apiService.getTopCommentedArticles().pipe(
    map(({ data }) =>
      data.map((article: ArticleCard & { firstTag?: Tag; commentsCount?: number }) => ({
        ...article,
        title: article?.recommendedTitle || article.title,
        tags: article?.firstTag ? [article.firstTag] : [],
        commentCount: article?.commentsCount,
      }))
    ),
    catchError((error) => {
      router.navigate(['/', '404'], { skipLocationChange: true }).then();
      return throwError(() => error);
    })
  );
};
