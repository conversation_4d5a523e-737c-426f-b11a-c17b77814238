import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { ArticleCardComponent, ArticleCardType, BreadcrumbComponent, createBorsOnlineTitle, defaultMetaInfo, PageTitleComponent } from '../../shared';
import { ActivatedRoute } from '@angular/router';
import { toSignal } from '@angular/core/rxjs-interop';
import { map, tap } from 'rxjs/operators';
import { ArticleCard, createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';
import { IMetaData, SeoService } from '@trendency/kesma-core';

@Component({
  selector: 'app-top-commented-page',
  templateUrl: './top-commented-page.component.html',
  styleUrl: './top-commented-page.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [SidebarComponent, BreadcrumbComponent, PageTitleComponent, ArticleCardComponent],
})
export class TopCommentedPageComponent {
  private readonly route = inject(ActivatedRoute);
  private readonly seoService = inject(SeoService);

  readonly ArticleCardType = ArticleCardType;

  readonly data = toSignal<ArticleCard[]>(
    this.route.data.pipe(
      map(({ data }) => data),
      tap(() => this.setMetaData())
    )
  );

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('legtobbet-kommentelt');
    canonical && this.seoService.updateCanonicalUrl(canonical);
    const title = createBorsOnlineTitle('Legtöbbet kommentelt');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title,
      ogTitle: title,
    };
    this.seoService.setMetaData(metaData);
  }
}
