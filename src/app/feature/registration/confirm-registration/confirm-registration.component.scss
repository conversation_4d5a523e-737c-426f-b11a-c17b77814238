@use 'shared' as *;
:host {
  @include media-breakpoint-down(md) {
    display: flex;
    width: 100%;
    justify-content: center;
  }
}

.confirm-registration {
  width: 100%;
  max-width: 640px;
  background-color: var(--kui-gray-100);
  margin: 60px auto;
  padding: 60px 0;

  @include media-breakpoint-down(md) {
    padding: 32px 16px;
    margin: 32px 16px 16px 16px;
  }

  &-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 32px;
    width: 100%;
    max-width: 416px;

    .title {
      font-size: 36px;
      font-weight: 800;
      text-align: center;
      line-height: 42px;

      @include media-breakpoint-down(md) {
        font-size: 26px;
        line-height: 28px;
      }
    }

    p {
      text-align: center;
      font-size: 16px;
      font-weight: 400;
      line-height: 24px;
    }
  }

  .error,
  a {
    color: var(--kui-red-500);
    text-align: center;
  }
}
