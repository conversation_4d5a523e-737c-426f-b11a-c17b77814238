<section>
  <div class="wrapper meta">
    <app-breadcrumb
      [data]="[
        {
          label: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        },
      ]"
    ></app-breadcrumb>
    <app-page-title class="page-title" title="<PERSON><PERSON><PERSON>ri<PERSON>" />
    <hr class="divider" />
  </div>
  @if (galleries()?.[0]; as gallery) {
    <div class="wrapper">
      <app-article-card
        class="gallery-card"
        [showAdultLayer]="gallery?.isAdult && !isUserAdultChoice()"
        [styleId]="ArticleCardType.HighlightedSideImgDateTitleLead"
        [data]="gallery"
        [useEagerLoad]="true"
      ></app-article-card>
      <hr class="divider" />
    </div>
  }
  <div class="wrapper with-aside">
    <div class="left-column">
      @if (galleries()?.length > 1) {
        @for (gallery of galleries() | slice: 1; track gallery.id; let i = $index) {
          <app-article-card
            class="gallery-card"
            [showAdultLayer]="gallery?.isAdult && !isUserAdultChoice()"
            [styleId]="ArticleCardType.SideImgDateTitleLead"
            [data]="gallery"
          ></app-article-card>

          @if (i === 0) {
            @if (adverts()?.desktop?.roadblock_1; as ad) {
              <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
            }
            @if (adverts()?.mobile?.mobilrectangle_1; as ad) {
              <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
            }
          }
          @if (i === 2) {
            @if (adverts()?.desktop?.roadblock_2; as ad) {
              <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
            }
            @if (adverts()?.mobile?.mobilrectangle_2; as ad) {
              <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
            }
          }
        }
      }
      @if (limitable()?.pageMax) {
        <app-pager class="pager" [rowAllCount]="limitable()?.rowAllCount!" [rowOnPageCount]="limitable()?.rowOnPageCount!"></app-pager>
      }
    </div>
    <aside>
      <app-sidebar></app-sidebar>
    </aside>
  </div>
</section>
