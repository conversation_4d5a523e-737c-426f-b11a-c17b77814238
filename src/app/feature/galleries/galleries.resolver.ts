import { ActivatedRouteSnapshot, ResolveFn, Router } from '@angular/router';
import { inject } from '@angular/core';
import { ApiService } from '../../shared';
import { catchError, map } from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';
import { throwError } from 'rxjs';
import { ApiResult, GalleryData } from '@trendency/kesma-ui';

export const galleriesResolver: ResolveFn<ApiResult<GalleryData[]>> = (route: ActivatedRouteSnapshot) => {
  const apiService = inject(ApiService);
  const router = inject(Router);
  const { page } = route.queryParams;
  const currentPage = page ? parseInt(page, 10) - 1 : 0;

  return apiService.getGalleries(currentPage).pipe(
    map(({ data, meta }) => ({
      data: data.map(
        (gallery) =>
          ({
            ...gallery,
            contentType: 'gallery',
            thumbnail: {
              url: gallery.highlightedImageUrl,
            },
            publishDate: gallery.publicDate,
          }) as GalleryData
      ),
      meta,
    })),
    catchError((error: HttpErrorResponse | Error) => {
      console.error('Galleries page resolver error', error);
      router
        .navigate(['/', '404'], {
          state: { errorResponse: JSON.stringify(error) },
          skipLocationChange: true,
        })
        .then();
      return throwError(() => error);
    })
  );
};
