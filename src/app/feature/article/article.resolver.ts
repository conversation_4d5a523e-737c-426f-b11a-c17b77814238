import { ActivatedRouteSnapshot, ResolveFn, Router } from '@angular/router';
import { inject } from '@angular/core';
import { ApiResult, Article, ArticleRouteParams } from '@trendency/kesma-ui';
import { forkJoin, Observable, of, switchMap, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ArticleService } from '../../shared';

type ArticleResolverResponse = Readonly<{
  article: ApiResult<Article>;
  url?: string;
}>;

export const articleResolver: ResolveFn<ArticleResolverResponse> = (route: ActivatedRouteSnapshot) => {
  const router = inject(Router);
  const articleService = inject(ArticleService);

  const params = route.params as ArticleRouteParams;
  const previewType = params?.previewType || 'accepted';
  const { previewHash, categorySlug, articleSlug } = params;
  const isYear = !isNaN(parseInt(params.year as string));
  const year = isYear && params.year ? params.year : undefined;
  const month = isYear && params.month ? params.month : undefined;
  const url = `${categorySlug}/${year}/${month}/${articleSlug}`;

  if ((!year || !month) && !previewHash) {
    router
      .navigate(['/', '404'], {
        skipLocationChange: true,
      })
      .then();
  }

  const request$: Observable<ArticleResolverResponse> = of({}).pipe(
    switchMap(() => {
      if (previewHash) {
        return forkJoin({
          article: articleService.getArticlePreview('cikk-elonezet', previewHash, previewType),
        });
      }
      return forkJoin({
        article: articleService.getArticle(categorySlug, String(year), String(month), articleSlug),
        url: of(url),
      });
    })
  );

  return request$.pipe(
    catchError(() => {
      router
        .navigate(['/', '404'], {
          skipLocationChange: true,
        })
        .then();
      return throwError(() => null);
    })
  ) as Observable<ArticleResolverResponse>;
};
