@use 'shared' as *;

:host {
  margin: 32px 0;
  color: var(--kui-black-950);
  display: block;
  .article {
    display: flex;
    flex-direction: column;
    gap: 32px;
  }
  .wrapper {
    display: flex;
    gap: 32px;
    @include media-breakpoint-down(md) {
      gap: 16px;
    }
  }
  .column {
    flex-direction: column;
  }
  .title {
    font-size: 52px;
    font-weight: 800;
    line-height: 60px;
    @include media-breakpoint-down(md) {
      font-size: 32px;
      line-height: 38px;
    }
  }
  .first-tag {
    font-size: 16px;
    font-weight: 700;
    width: fit-content;
    line-height: normal;
    text-transform: uppercase;
    padding: 5px 8px 3px 0;
    color: var(--kui-red-500);
    margin-top: -24px;
    &:hover {
      color: var(--kui-red-550);
    }
    @include media-breakpoint-down(md) {
      margin-top: -8px;
    }
  }
  .dates {
    font-size: 12px;
    font-style: normal;
    line-height: 16px;
    text-transform: uppercase;
    padding-block: 16px;
    border-block: 1px solid var(--kui-gray-200);
  }
  .tags-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    .tag {
      color: var(--kui-red-500);
      border: 1px solid var(--kui-red-500);
      padding: 4px 8px;
      font-size: 12px;
      font-weight: 600;
      line-height: normal;
      font-variant: all-small-caps;
      &:hover {
        background-color: var(--kui-red-550);
        border-color: var(--kui-red-550);
        color: var(--kui-white);
      }
    }
  }
  .lead {
    font-size: 24px;
    font-weight: 600;
    line-height: 30px;
    @include media-breakpoint-down(md) {
      font-size: 20px;
      line-height: 28px;
    }
  }
  .author {
    font-size: 20px;
    font-weight: 700;
    line-height: 28px;
    color: var(--kui-red-500);
    display: flex;
    flex-direction: column;
    @include media-breakpoint-down(md) {
      font-size: 18px;
      line-height: 26px;
    }
    a {
      color: var(--kui-red-500);

      &:hover {
        color: var(--kui-red-550);
      }
    }
    &-rank {
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      color: var(--kui-black-950);
    }
    &-data {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 16px;
      padding-block: 16px;
      border-block: 1px solid var(--kui-gray-200);
      @include media-breakpoint-up(lg) {
        margin-top: -16px;
      }
    }
    &-link {
      display: flex;
      gap: 10px;
      font-size: 16px;
      font-weight: 700;
      line-height: 20px;
      align-items: center;
      color: var(--kui-red-500);
      fill: var(--kui-red-500);
      &:hover {
        color: var(--kui-red-550);
        fill: var(--kui-red-550);
      }
    }
  }
  .with-aside {
    margin-top: 0;
    .left-column {
      container-type: inline-size;
    }
  }
  .block {
    margin-bottom: 32px;
    @include media-breakpoint-down(md) {
      margin-bottom: 16px;
    }
  }
  .ten-recommender {
    border-top: 1px solid var(--kui-gray-200);
  }
  .external-recommendation {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 32px;
    @include container-breakpoint-down(sm) {
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
    }

    .full-row {
      grid-column: 1 / -1;
    }
    app-article-card {
      margin-bottom: 0;
    }
  }
  kesma-article-video {
    display: block;
  }
  .share {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .comment-count {
    background-color: var(--kui-black-950);
    color: var(--kui-white);
    fill: var(--kui-white);
    height: 30px;
    font-size: 12px;
    font-weight: 600;
    line-height: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 4px 16px;
    border-radius: 15px;
    &:hover {
      color: var(--kui-white);
      fill: var(--kui-white);
      background-color: var(--kui-black-1100);
    }
  }

  //Minute by minute
  .mbm-header {
    display: flex;
    justify-content: space-between;
    gap: 16px;
    font-size: 14px;
    line-height: 18px;
    &-text {
      font-weight: 700;
      color: var(--kui-red-500);
    }
  }
  .mbm-post {
    padding: 32px;
    border: 1px solid var(--kui-red-500);
    @include media-breakpoint-down(md) {
      padding: 16px;
    }
    &-header {
      display: flex;
      align-items: center;
      margin-bottom: 24px;
      gap: 12px;
    }
    &-time {
      font-size: 14px;
      font-weight: 600;
      line-height: 20px;
      background-color: var(--kui-red-500);
      color: var(--kui-white);
      padding: 2px 7px;
    }
    &-date {
      font-size: 12px;
      line-height: 16px;
      text-transform: uppercase;
    }
    &-share {
      margin-left: auto;
    }
    &-title {
      font-size: 24px;
      line-height: 30px;
      overflow-wrap: anywhere;
      margin-bottom: 16px;
      @include media-breakpoint-down(md) {
        font-size: 20px;
        line-height: 24px;
      }
    }
  }
  .mbm-wysiwyg {
    display: block;
    margin-bottom: -32px;
    @include media-breakpoint-down(md) {
      margin-bottom: -16px;
    }
  }
  .scroll-target {
    position: absolute;
    top: -120px;
    &-wrapper {
      position: relative;
      z-index: 1;
    }
  }
}
