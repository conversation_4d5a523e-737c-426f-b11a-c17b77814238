<section>
  <div class="wrapper meta">
    <app-search-filter />
    <app-breadcrumb
      [data]="[
        {
          label: 'Legfrissebb hírek',
        },
      ]"
    ></app-breadcrumb>
    <app-page-title title="Legfrissebb hírek" />
    <hr class="divider" />
  </div>
  @if (articleCards().length) {
    <div class="wrapper">
      <app-article-card
        [styleId]="ArticleCardType.HighlightedSideImgDateTitleLead"
        [data]="mainArticleCard()"
        [fetchpriority]="'high'"
        [useEagerLoad]="true"
      ></app-article-card>
      <hr class="divider" />
    </div>
  }
  <div class="wrapper with-aside">
    <div class="left-column">
      @if (articleCards().length > 1) {
        @for (articleCard of smallArticleCards(); track articleCard.id; let index = $index) {
          <app-article-card [styleId]="ArticleCardType.SideImgDateTitleLead" [data]="articleCard" [useEagerLoad]="index <= 3"></app-article-card>
        }
      }
      @if (limitable()?.pageMax) {
        <app-pager [rowAllCount]="limitable()?.rowAllCount!" [rowOnPageCount]="limitable()?.rowOnPageCount!"></app-pager>
      }
    </div>
    <aside class="aside">
      <app-sidebar></app-sidebar>
    </aside>
  </div>
</section>
