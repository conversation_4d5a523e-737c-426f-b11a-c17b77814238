import { ActivatedRouteSnapshot, ResolveFn } from '@angular/router';
import { inject } from '@angular/core';
import { ApiService } from '../../../shared';
import { RedirectService, searchResultToArticleCard } from '@trendency/kesma-ui';
import { map, tap } from 'rxjs/operators';

export const freshNewsResolver: ResolveFn<any> = (route: ActivatedRouteSnapshot) => {
  const api = inject(ApiService);
  const redirectService = inject(RedirectService);
  const filters = { ...route.queryParams, 'publishDate_order[]': 'desc' };
  const currentPage = route.queryParams['page'] ? parseInt(route.queryParams['page'], 10) - 1 : 0;
  return api.getArticlesByLastDay('', currentPage, 11, filters).pipe(
    tap(({ data }) => {
      if (redirectService.shouldBeRedirect(currentPage, data)) {
        redirectService.redirectOldUrl(`kereses`, false, 302);
      }
    }),
    map((res) => ({ data: res.data.map(searchResultToArticleCard), meta: res.meta }))
  );
};
