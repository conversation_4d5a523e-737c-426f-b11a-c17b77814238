import { ChangeDetectionStrategy, Component, computed, inject, OnInit, signal } from '@angular/core';

import {
  CityWeatherCurrent,
  createCanonicalUrlForPageablePage,
  IconComponent,
  KesmaSwipeComponent,
  SwipeBreakpoints,
  WeatherCity,
  WeatherDailyShort,
  WeatherData,
  WeatherForecast,
  WeatherUv,
} from '@trendency/kesma-ui';
import { SeoService } from '@trendency/kesma-core';
import {
  ApiService,
  ArticleCardComponent,
  ArticleCardType,
  BorsWeatherData,
  BreadcrumbComponent,
  createBorsOnlineTitle,
  defaultMetaInfo,
  PageTitleComponent,
  SearchSelectComponent,
} from '../../shared';
import { toSignal } from '@angular/core/rxjs-interop';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { NgSelectModule } from '@ng-select/ng-select';
import { FormsModule } from '@angular/forms';
import { SlicePipe } from '@angular/common';
import { FormatPipeModule } from 'ngx-date-fns';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { catchError, map } from 'rxjs/operators';
import { FILTERED_CITIES } from './weather.utils';
import { of } from 'rxjs';

@Component({
  selector: 'app-weather',
  templateUrl: './weather.component.html',
  styleUrl: './weather.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    SidebarComponent,
    BreadcrumbComponent,
    PageTitleComponent,
    NgSelectModule,
    FormsModule,
    IconComponent,
    SlicePipe,
    FormatPipeModule,
    KesmaSwipeComponent,
    SearchSelectComponent,
    ArticleCardComponent,
    RouterLink,
  ],
})
export class WeatherComponent implements OnInit {
  private readonly route = inject(ActivatedRoute);
  private readonly seoService = inject(SeoService);
  private readonly apiService = inject(ApiService);

  readonly mathRound = Math.round;
  readonly ArticleCardType = ArticleCardType;

  readonly breakpoints: SwipeBreakpoints = {
    default: {
      itemCount: 4,
      gap: '12px',
    },
    600: {
      itemCount: 7,
    },
    768: {
      itemCount: 9,
    },
  };

  readonly cities: WeatherCity[] = [
    'Budapest',
    'Debrecen',
    'Eger',
    'Győr',
    'Kaposvár',
    'Kecskemét',
    'Miskolc',
    'Nyíregyháza',
    'Pécs',
    'Salgótarján',
    'Szeged',
    'Székesfehérvár',
    'Szekszárd',
    'Szolnok',
    'Szombathely',
    'Tatabánya',
    'Veszprém',
    'Zalaegerszeg',
  ];

  readonly selectedCity = signal<WeatherCity>('Székesfehérvár');

  readonly mapCities = computed<CityWeatherCurrent[]>(() => {
    return this.weatherData()?.current?.filter(({ city }) => FILTERED_CITIES.includes(city)) as CityWeatherCurrent[];
  });

  readonly selectedWeather = computed<WeatherData>(() => {
    const currentWeather = this.weatherData()?.current.find(({ city }) => this.selectedCity() === city);
    const forecast = this.weatherData()?.forecast[this.selectedCity()];
    const uv = this.weatherData()?.uv.find(({ city }) => this.selectedCity() === city);
    const text = this.weatherData()?.text;
    return {
      current: currentWeather as CityWeatherCurrent,
      forecast: forecast as WeatherForecast[],
      text: text as WeatherDailyShort[],
      uv: uv as WeatherUv,
    };
  });

  readonly weatherData = toSignal<BorsWeatherData>(this.route.data.pipe(map(({ data }) => data)));
  readonly weatherArticles = toSignal(this.apiService.getCategoryArticles('idojaras', 0, 4).pipe(catchError(() => of([]))));

  formatDate(date: string): string {
    const time = date.substring(0, 5); // 06:25;
    if (time.startsWith('0')) {
      return time.slice(1); // 6:25;
    }
    return time;
  }

  ngOnInit(): void {
    const canonical = createCanonicalUrlForPageablePage('idojaras');
    canonical && this.seoService.updateCanonicalUrl(canonical);
    const title = createBorsOnlineTitle('Időjárás');
    this.seoService.setMetaData({
      ...defaultMetaInfo,
      title,
      ogTitle: title,
    });
  }
}
