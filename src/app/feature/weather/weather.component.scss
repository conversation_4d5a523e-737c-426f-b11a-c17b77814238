@use 'shared' as *;

:host {
  display: block;
  color: var(--kui-black-950);
  width: 100%;
  margin-block: 32px;
  .top {
    display: flex;
    flex-direction: column;
    @include media-breakpoint-down(md) {
      gap: 8px;
    }
  }
  .weather-page {
    display: flex;
    flex-direction: column;
    gap: 32px;
    @include media-breakpoint-down(md) {
      gap: 16px;
    }
    &-row {
      display: flex;
      gap: 32px;
    }
    &-col {
      width: 100%;
    }
  }
  .cities {
    display: flex;
    align-items: center;
    gap: 16px;
    @include media-breakpoint-down(md) {
      flex-direction: column-reverse;
    }
  }
  .koponyeg-text {
    font-size: 16px;
    font-weight: 700;
    line-height: normal;
    text-align: end;
    .link {
      color: var(--kui-red-500);
      text-decoration-line: underline;
      text-underline-offset: 3.1px;
    }
  }
  .current-weather {
    display: flex;
    gap: 32px;
    @include media-breakpoint-down(md) {
      flex-direction: column;
    }
    &-data {
      border: 1px solid var(--kui-gray-200);
      padding: 32px;
      margin-top: 32px;
      @include media-breakpoint-down(md) {
        padding: 16px;
        margin-top: 16px;
      }
    }
    &-top {
      display: flex;
      justify-content: space-between;
      gap: 8px;
      .sunrise {
        width: 44px;
      }
      @include media-breakpoint-down(md) {
        flex-direction: column;
      }
    }
    &-temp {
      margin-block: 32px;
      display: flex;
      align-items: center;
      gap: 22px;
      .temperature {
        font-size: 80px;
        font-weight: 700;
        line-height: normal;
        min-width: 113px;
      }
      .min-max {
        display: flex;
        flex-direction: column;
        font-size: 30px;
        line-height: normal;
        gap: 13px;
        @include media-breakpoint-down(md) {
          font-size: 24px;
          gap: 8px;
        }
      }
      .max,
      .min {
        height: 30px;
        @include media-breakpoint-down(md) {
          height: 24px;
        }
      }
      .max {
        color: var(--kui-red-500);
      }
    }
    &-details {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      &-item {
        display: flex;
        flex-direction: column;
        line-height: normal;
        gap: 11px;
        @include media-breakpoint-down(md) {
          gap: 8px;
        }
      }
      .label {
        font-size: 12px;
        height: 12px;
      }
      .humidity {
        width: 74px;
      }
      .air-pressure {
        width: 91px;
      }
      .wind {
        width: 85px;
      }
      .wind-direction {
        width: 62px;
      }
      .uv {
        width: 98px;
      }
    }
    .skyview {
      font-size: 24px;
      line-height: 30px;
      font-weight: 700;
      @include media-breakpoint-down(md) {
        font-size: 20px;
        line-height: 24px;
      }
    }
    .times {
      display: flex;
      align-items: center;
      gap: 6px;
      kesma-icon {
        margin-right: 3px;
      }
    }
  }
  .divider {
    height: 1px;
    background-color: rgba($black, 0.2);
    width: 100%;
  }
  .empty {
    text-align: center;
    font-weight: 800;
  }
  .with-aside {
    margin-top: 0;
  }
  .title {
    font-size: 24px;
    font-weight: 700;
    line-height: 30px;
    @include media-breakpoint-down(md) {
      font-size: 20px;
      line-height: 24px;
    }
  }
  .day,
  .text {
    font-size: 18px;
    line-height: 40px;
    @include media-breakpoint-down(md) {
      font-size: 16px;
      line-height: 32px;
    }
  }
  .text {
    margin-bottom: 40px;
    &:last-of-type {
      margin-bottom: 32px;
    }
  }
  .day {
    font-weight: 700;
  }
  .national {
    margin-block: 32px;
    @include media-breakpoint-down(md) {
      margin-bottom: 16px;
    }
  }
  .forecast {
    text-align: center;
    margin-top: 32px;
    @include media-breakpoint-down(md) {
      margin-top: 16px;
    }
    &-box {
      border: 1px solid var(--kui-gray-200);
      font-size: 12px;
      line-height: normal;
      text-transform: capitalize;
      &-bottom,
      &-top {
        padding-block: 21px;
      }
      &-body {
        display: flex;
        flex-direction: column;
        padding-block: 22px 29px;
        align-items: center;
        border-block: 1px solid var(--kui-gray-200);
        .avg {
          padding-block: 31px;
          font-size: 30px;
          font-weight: 700;
          line-height: normal;
        }
        .max {
          font-size: 16px;
          line-height: normal;
          color: var(--kui-red-500);
          margin-bottom: 18px;
        }
        .min {
          -webkit-text-stroke: 1px var(--kui-black-950);
          -webkit-text-fill-color: var(--kui-white);
          color: transparent;
          font-size: 16px;
          line-height: normal;
        }
      }
      &-bottom {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;
      }
    }
    &-day-name {
      margin-bottom: 9px;
    }
  }
  .navigation {
    color: var(--kui-black-950);
    width: 40px;
    height: 40px;
    &.prev {
      rotate: -180deg;
    }
  }
  .left-column {
    container-type: inline-size;
  }
  .map {
    width: fit-content;
    margin: 0 auto;
    position: relative;
    &-layer {
      position: absolute;
      background: url(/assets/images/icons/weather/map-layer.svg) no-repeat;
      background-size: contain;
      width: 56px;
      height: 96px;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-top: 10px;
      font-size: 16px;
      @include media-breakpoint-down(xs) {
        background: url(/assets/images/icons/weather/map-mobile-layer.svg) no-repeat;
        width: 30px;
        height: 65px;
        font-size: 12px;
        padding-top: 5px;
        kesma-icon {
          width: 16px;
          height: 17px;
        }
      }
      &-max {
        color: var(--kui-red-500);
        margin-block: 9px 3px;
        height: 15px;
        @include media-breakpoint-down(xs) {
          height: 12px;
          margin-block: 4px 0;
        }
      }
      &.Győr {
        top: 15%;
        left: 20%;
      }
      &.Miskolc {
        right: 27%;
        top: -2%;
      }
      &.Debrecen {
        right: 15%;
        top: 20%;
      }
      &.Pécs {
        left: 27%;
        bottom: 10%;
      }
      &.Szeged {
        right: 38%;
        bottom: 20%;
      }
      &.Zalaegerszeg {
        left: 8%;
        top: 40%;
      }
      &.Budapest {
        left: 40%;
        top: 15%;
      }
    }
  }

  //kesma-swipe
  ::ng-deep .kesma-swipe {
    .bottom-navigation {
      position: absolute;
      top: -66px;
      right: 0;
      @include media-breakpoint-down(md) {
        top: -48px;
      }
    }
  }
  @include container-breakpoint-up(md) {
    ::ng-deep .bottom-navigation {
      visibility: hidden;
    }
  }

  // Articles
  .recommendation {
    display: flex;
    flex-direction: column;
    padding: 32px 24px;
    gap: 24px;
    @include media-breakpoint-down(xs) {
      margin: 0 -15px;
      width: calc(100% + 30px);
    }
    @include container-breakpoint-down(sm) {
      gap: 16px;
      padding: 16px;
    }
    &-header {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      gap: 8px;
    }
    &-title {
      font-size: 48px;
      color: var(--kui-white);
      font-weight: 800;
      line-height: 42px;
      @include container-breakpoint-down(sm) {
        font-size: 28px;
        line-height: 28px;
      }
    }
    &-link {
      display: flex;
      align-items: center;
      gap: 10px;
      color: var(--kui-white);
      line-height: 20px;
      font-weight: 700;
      &:hover {
        color: var(--kui-white-o90);
        kesma-icon {
          fill: var(--kui-white-o90);
        }
      }
    }
    kesma-icon {
      fill: var(--kui-white);
      flex-shrink: 0;
    }
    .articles {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 16px;
      .article-card {
        margin-bottom: 0;
        --theme-color: var(--kui-white);
      }
      @include container-breakpoint-down(xs) {
        grid-template-columns: 1fr 1fr;
      }
    }
  }
}
