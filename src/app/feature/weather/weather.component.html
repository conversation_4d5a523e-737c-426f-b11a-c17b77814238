<section class="weather-page">
  <div class="wrapper top">
    <app-breadcrumb [data]="[{ label: 'Időjárás' }]" />
    <app-page-title title="Időjárás" />
  </div>
  <div class="wrapper divider"></div>
  <div class="wrapper">
    <div class="current-weather">
      <div class="weather-page-col">
        <div class="cities">
          <app-search-select class="weather-page-col" [value]="selectedCity()" [items]="cities" (valueChanged)="selectedCity.set($event)"></app-search-select>
          <div class="koponyeg-text weather-page-col">Adatok szolgáltatója: <a class="link" href="https://koponyeg.hu" target="_blank">köpönyeg.hu</a></div>
        </div>
        <div class="current-weather-data">
          @if (selectedWeather()?.current; as current) {
            <div class="current-weather-top">
              <div class="skyview">{{ current.skyView }}</div>
              <div class="times">
                <kesma-icon name="weather/sunrise" [size]="18.786" [height]="20" />
                <span class="sunrise">{{ formatDate(current.sunrise) }}</span>
                <kesma-icon name="weather/sunset" [size]="15.028" [height]="15.98" />
                {{ formatDate(current.sunset) }}
              </div>
            </div>
            <div class="current-weather-temp">
              <kesma-icon [name]="'weather/' + current.icon2" [size]="96" />
              <div class="temperature">{{ current.temperature }}°</div>
              <div class="min-max">
                <div class="max">{{ current.maxTemperature }}°</div>
                <div class="min">{{ current.minTemperature }}°</div>
              </div>
            </div>
            <div class="current-weather-details">
              <div class="current-weather-details-item">
                <div class="label humidity">Csapadék</div>
                {{ current.humidity }} mm
              </div>
              <div class="current-weather-details-item">
                <div class="label air-pressure">Légnyomás</div>
                {{ current.airPressure }} hPa
              </div>
              <div class="current-weather-details-item">
                <div class="label wind">Szélerősség</div>
                {{ current.wind }} km/h
              </div>
              <div class="current-weather-details-item">
                <div class="label wind-direction">Szélirány</div>
                {{ current.windDirection }}
              </div>
              <div class="current-weather-details-item">
                <div class="label uv">UV</div>
                {{ mathRound(+selectedWeather().uv.index) }}, {{ selectedWeather().uv.text }}
              </div>
            </div>
          } @else {
            <div class="empty">A választott városhoz nincs elérhető mai adat!</div>
          }
        </div>
      </div>
      <div class="weather-page-col">
        <div class="map">
          <img src="./assets/images/icons/weather/map.svg" alt="Időjárás térkép" loading="eager" />
          @for (city of mapCities(); track city.city) {
            <div class="map-layer" [class]="city.city" [title]="city.city">
              <kesma-icon [name]="'weather/' + city.icon2" [size]="22" />
              <p class="map-layer-max">{{ city.maxTemperature }}°</p>
              <p class="map-layer-min">{{ city.minTemperature }}°</p>
            </div>
          }
        </div>
      </div>
    </div>
  </div>
  <div class="wrapper with-aside">
    <div class="left-column">
      <h2 class="title">9 napos előrejelzés</h2>
      <ng-template #previousNavigation>
        <kesma-icon class="navigation prev" name="arrow-right" />
      </ng-template>
      <ng-template #nextNavigation>
        <kesma-icon class="navigation" name="arrow-right" />
      </ng-template>
      @if (selectedWeather()?.forecast) {
        <div
          class="forecast"
          kesma-swipe
          [data]="selectedWeather().forecast | slice: 0 : 9"
          [itemTemplate]="forecastTemplate"
          [previousNavigationTemplate]="previousNavigation"
          [nextNavigationTemplate]="nextNavigation"
          [useNavigation]="true"
          [usePagination]="true"
          dataTrackByProperty="day"
          [breakpoints]="breakpoints"
        ></div>
      }
      <h2 class="title national">Országos előrejelzés</h2>
      <div>
        @for (data of selectedWeather().text; track data.day) {
          <h3 class="day">{{ data.day }}</h3>
          <div class="text">{{ data.text }}</div>
        }
      </div>

      @if (weatherArticles()?.data?.length) {
        <div class="recommendation" [style.background-color]="weatherArticles()?.meta?.column?.mainColor || 'var(--kui-red-500)'">
          <div class="recommendation-header">
            <h2 class="recommendation-title">IDŐJÁRÁS</h2>
            <a class="recommendation-link" routerLink="/rovat/idojaras">
              Még Időjárás
              <kesma-icon name="arrow-right-long" [size]="14" [height]="16"></kesma-icon>
            </a>
          </div>
          <div class="articles">
            @for (article of weatherArticles()?.data; track article.id) {
              <app-article-card class="article-card" [data]="article" [hasBlockBackground]="true" [styleId]="ArticleCardType.Weather"></app-article-card>
            }
          </div>
        </div>
      }
    </div>
    <aside>
      <app-sidebar></app-sidebar>
    </aside>
  </div>
</section>

<ng-template #forecastTemplate let-data="data">
  <div class="forecast-box">
    <div class="forecast-box-top">
      <div class="forecast-day-name">
        {{ data.date | dfnsFormat: 'EEEE' }}
      </div>
      <div class="forecast-time">
        {{ data.date | dfnsFormat: 'MMM d.' }}
      </div>
    </div>
    <div class="forecast-box-body">
      <kesma-icon [name]="'weather/' + data.icon2" title="data.description" [size]="36" />
      <p class="avg">{{ (data.maxTemperature + data.minTemperature) / 2 }}°</p>
      <p class="max">{{ data.maxTemperature }}°</p>
      <p class="min">{{ data.minTemperature }}°</p>
    </div>
    <div class="forecast-box-bottom">
      <kesma-icon name="weather/rain" [size]="18.804" [height]="13.75" />
      <p class="bottom-text">{{ data.rain }} mm</p>
      <kesma-icon name="weather/wind" [size]="18.804" [height]="14.667" />
      <p class="bottom-text">{{ data.wind }} km/h</p>
    </div>
  </div>
</ng-template>
