import { inject, Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ApiService } from '../../shared';
import { Layout } from '@trendency/kesma-ui';

@Injectable()
export class LayoutPreviewResolver {
  private readonly apiService = inject(ApiService);
  private readonly router = inject(Router);

  resolve(route: ActivatedRouteSnapshot): Observable<Layout> {
    const { layoutHash } = route.params;
    return this.apiService.getLayoutPreview(layoutHash).pipe(
      catchError((err: Error) => {
        this.router
          .navigate(['/', '404'], {
            skipLocationChange: true,
          })
          .then();
        return throwError(() => err);
      }),
      map(({ data }) => data)
    );
  }
}
