import { inject, Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { ApiResponseMetaList, ApiResult, ArticleCard, ArticleSearchResult, buildPhpArrayParam, searchResultToArticleCard, Tag } from '@trendency/kesma-ui';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class TagsPageService {
  private readonly reqService = inject(ReqService);
  private readonly maxResultsPerPage = 20;

  getTag(slug: string): Observable<ApiResult<Tag>> {
    return this.reqService.get(`content-group/tags/${slug}`, {});
  }

  searchArticleByTags(
    tags: string[],
    page = 0,
    itemsPerPage = this.maxResultsPerPage,
    orderByAsc = true,
    extraParams?: Record<string, string>
  ): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    const params = {
      ...buildPhpArrayParam(tags, 'tags'),
      rowCount_limit: itemsPerPage.toString(),
      page_limit: page.toString(),
      'date_order[0]': orderByAsc ? 'asc' : 'desc',
    };

    if (extraParams) {
      Object.assign(params, extraParams);
    }
    return this.reqService
      .get<ApiResult<ArticleSearchResult[], ApiResponseMetaList>>(`/content-page/search`, {
        params,
      })
      .pipe(
        map(({ data, meta }) => ({
          data: data.map((item) => searchResultToArticleCard(item)),
          meta,
        }))
      );
  }
}
