import { ChangeDetectionStrategy, Component, computed, effect, inject, OnInit, Signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { toSignal } from '@angular/core/rxjs-interop';
import { ArticleCard, createCanonicalUrlForPageablePage, LimitableMeta, Tag } from '@trendency/kesma-ui';
import {
  ArticleCardComponent,
  ArticleCardType,
  BreadcrumbComponent,
  createBorsOnlineTitle,
  defaultMetaInfo,
  makeBreadcrumbSchema,
  PagerComponent,
  PageTitleComponent,
  RelatedTagsComponent,
  SearchFilterComponent,
} from '../../shared';
import { map } from 'rxjs/operators';
import { TagsPageResponse } from './tags-page.resolver';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { IMetaData, SchemaOrgService, SeoService } from '@trendency/kesma-core';
import capitalize from 'lodash-es/capitalize';
import { SlicePipe, TitleCasePipe } from '@angular/common';

@Component({
  selector: 'app-tags-page',
  imports: [
    ArticleCardComponent,
    BreadcrumbComponent,
    PagerComponent,
    SidebarComponent,
    TitleCasePipe,
    SearchFilterComponent,
    PageTitleComponent,
    SlicePipe,
    RelatedTagsComponent,
  ],
  templateUrl: './tags-page.component.html',
  styleUrl: './tags-page.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TagsPageComponent implements OnInit {
  private readonly route = inject(ActivatedRoute);
  private readonly seoService = inject(SeoService);
  private readonly schemaService = inject(SchemaOrgService);

  readonly resolverData = toSignal(this.route.data.pipe(map(({ data }) => data as TagsPageResponse)));

  readonly tag: Signal<Tag | undefined> = computed(() => this.resolverData()?.tag);
  readonly articles: Signal<ArticleCard[] | undefined> = computed(() => this.resolverData()?.articles);
  readonly limitable: Signal<LimitableMeta | undefined> = computed(() => this.resolverData()?.limitable);

  readonly ArticleCardType = ArticleCardType;

  constructor() {
    effect(() => {
      const t = this.tag();
      if (!t?.slug || !t?.title) {
        return;
      }
      const tagTitle = t.title;
      const tagSlug = t.slug;
      const breadcrumbSchema = makeBreadcrumbSchema([{ label: tagTitle, url: ['/', tagSlug] }]);
      this.schemaService.insertSchema(breadcrumbSchema);
    });
  }

  ngOnInit(): void {
    this.setMetaData();
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('cimke', this.route.snapshot);
    canonical && this.seoService.updateCanonicalUrl(canonical);
    const title = createBorsOnlineTitle(capitalize(this.tag()?.title as string));
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'index, follow',
    };
    this.seoService.setMetaData(metaData);
  }
}
