<section>
  <div class="wrapper meta">
    <app-search-filter />
    <app-breadcrumb
      [data]="[
        {
          label: tag()?.title | titlecase,
        },
      ]"
    ></app-breadcrumb>
    <app-page-title class="page-title" [title]="tag()?.title | titlecase" />
    <hr class="divider" />
  </div>
  @if (articles()?.length) {
    <div class="wrapper">
      <app-article-card
        class="article-card"
        [styleId]="ArticleCardType.HighlightedSideImgDateTitleLead"
        [useEagerLoad]="true"
        fetchpriority="high"
        [data]="articles()?.[0]"
      ></app-article-card>
      <hr class="divider" />
    </div>
  }
  <div class="wrapper with-aside">
    <div class="left-column">
      @if (articles()?.length > 1) {
        @for (articleCard of articles() | slice: 1; track articleCard.id; let index = $index) {
          <app-article-card
            class="article-card"
            [styleId]="ArticleCardType.SideImgDateTitleLead"
            [data]="articleCard"
            [useEagerLoad]="index < 4"
          ></app-article-card>
        }
      }
      @if (limitable()?.pageMax) {
        <app-pager class="pager" [rowAllCount]="limitable()?.rowAllCount!" [rowOnPageCount]="limitable()?.rowOnPageCount!"></app-pager>
      }
    </div>
    <aside>
      @if (tag()?.relatedThematicTags?.length) {
        <app-related-tags [data]="tag()?.relatedThematicTags" />
      }
      <app-sidebar></app-sidebar>
    </aside>
  </div>
</section>
