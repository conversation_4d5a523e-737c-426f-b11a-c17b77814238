<section class="forgot-form">
  <div class="wrapper forgot-form-wrapper">
    @if (!isSubmitted()) {
      <div class="forgot-form-header">
        <h1 class="forgot-form-header-title">Elfelejtett jelszó</h1>
        <p class="forgot-form-header-text">
          Elfelejtette jelszavát? <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adja meg regisztrált e-mail címét, amely<PERSON> küldünk egy linket. A linkre kattintva továbbnavigálju<PERSON>, ahol új
          jelsz<PERSON>t adhat meg felhasználói fiókjához.
        </p>
      </div>
      @if (formGroup()) {
        <form [formGroup]="formGroup()" (ngSubmit)="requestPasswordReset()">
          <app-input-control controlName="email" labelText="E-mail cím" placeholder="Adja meg az e-mail címét"></app-input-control>
          @if (error()) {
            <div class="general-form-error">
              {{ error() }}
            </div>
          }
          <div class="forgot-form-buttons">
            <app-simple-button class="forgot-form-button w-100" [disabled]="isLoading()" [isSubmit]="true">{{
              isLoading() ? 'Kérem, várjon...' : 'Folytatás'
            }}</app-simple-button>
            <app-simple-button class="forgot-form-button w-100" [routerLink]="'/bejelentkezes'" [color]="'secondary'">Mégsem</app-simple-button>
          </div>
        </form>
      }
    } @else {
      <div class="forgot-form-header">
        <h1 class="forgot-form-header-title">Elfelejtett jelszó</h1>
        <p class="forgot-form-header-text">
          Amennyiben a megadott e-mail cím létezik rendszerünkben, küldtünk rá egy linket. A linkre kattintva továbbnavigáljuk Önt az oldalunkra, ahol új
          jelszót adhat meg felhasználói fiókjához.
        </p>
      </div>
      <app-simple-button [routerLink]="'/'">Tovább a címlapra</app-simple-button>
    }
  </div>
</section>
