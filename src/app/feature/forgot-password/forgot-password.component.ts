import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit, signal } from '@angular/core';
import { ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { createCanonicalUrlForPageablePage, emailValidator, markControlsTouched } from '@trendency/kesma-ui';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { ApiService, AppInputControlComponent, BorsSimpleButtonComponent, createBorsOnlineTitle, defaultMetaInfo } from '../../shared';
import { ReCaptchaV3Service } from 'ngx-captcha';
import { environment } from '../../../environments/environment';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ViewportScroller } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-forgot-password',
  templateUrl: './forgot-password.component.html',
  styleUrl: './forgot-password.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, ReactiveFormsModule, AppInputControlComponent, BorsSimpleButtonComponent],
})
export class ForgotPasswordComponent implements OnInit {
  private readonly formBuilder = inject(UntypedFormBuilder);
  private readonly seoService = inject(SeoService);
  private readonly reCaptchaV3Service = inject(ReCaptchaV3Service);
  private readonly apiService = inject(ApiService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly viewportScroller = inject(ViewportScroller);

  readonly formGroup = signal<UntypedFormGroup>(this.initForm);
  readonly isSubmitted = signal<boolean>(false);
  readonly error = signal<string | null>(null);
  readonly isLoading = signal<boolean>(false);

  ngOnInit(): void {
    this.setMetaData();
  }

  requestPasswordReset(): void {
    if (this.formGroup()) {
      markControlsTouched(this.formGroup());
    }
    if (this.formGroup().invalid) {
      return;
    }
    this.error.set(null);
    this.isLoading.set(true);
    this.reCaptchaV3Service.execute(
      environment.googleSiteKey ?? '',
      'app_publicapi_portal_user_password_forget',
      (recaptchaToken: string) => {
        this.apiService
          .requestPasswordReset(this.formGroup().value.email, recaptchaToken)
          .pipe(takeUntilDestroyed(this.destroyRef))
          .subscribe({
            next: () => {
              this.isSubmitted.set(true);
              this.isLoading.set(false);
              this.viewportScroller.scrollToPosition([0, 0]);
            },
            error: () => {
              // If we have error we just proceed
              this.error.set('Ismeretlen hiba!');
              this.isSubmitted.set(false);
              this.isLoading.set(false);
            },
          });
      },
      {
        useGlobalDomain: false,
      },
      () => {
        this.error.set('Captcha: Robot ellenőrzés hiba!');
        this.isLoading.set(false);
      }
    );
  }

  private get initForm(): UntypedFormGroup {
    return this.formBuilder.group({
      email: [null, [Validators.required, emailValidator]],
    });
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('elfelejtett-jelszo');
    canonical && this.seoService.updateCanonicalUrl(canonical);
    const title = createBorsOnlineTitle('Elfelejtett jelszó');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seoService.setMetaData(metaData);
  }
}
