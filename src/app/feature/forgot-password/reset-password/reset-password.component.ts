import { ChangeDetectionStrategy, Component, DestroyRef, inject, signal } from '@angular/core';
import { ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { markControlsTouched, passwordValidator } from '@trendency/kesma-ui';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Params, RouterLink } from '@angular/router';
import { map } from 'rxjs/operators';
import { ApiService, AppPasswordControlComponent, BorsSimpleButtonComponent, PopupComponent } from '../../../shared';

@Component({
  selector: 'app-reset-password',
  templateUrl: './reset-password.component.html',
  styleUrls: ['../forgot-password.component.scss', './reset-password.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ReactiveFormsModule, RouterLink, AppPasswordControlComponent, BorsSimpleButtonComponent, PopupComponent],
})
export class ResetPasswordComponent {
  private readonly formBuilder = inject(UntypedFormBuilder);
  private readonly route = inject(ActivatedRoute);
  private readonly apiService = inject(ApiService);
  private readonly destroyRef = inject(DestroyRef);

  readonly formGroup = signal<UntypedFormGroup>(this.initForm);
  readonly error = signal<string | null>(null);
  readonly isLoading = signal<boolean>(false);
  readonly isSubmitted = signal<boolean>(false);
  readonly email = signal<string | null>(null);
  readonly token = signal<string | null>(null);

  readonly hasValidParams = toSignal<boolean>(
    this.route.queryParams.pipe(
      map((params: Params) => {
        const { email, token } = params;
        this.email.set(email);
        this.token.set(token);
        return email && token;
      })
    )
  );

  resetPassword(): void {
    if (this.formGroup()) {
      markControlsTouched(this.formGroup());
    }
    if (this.formGroup().invalid) {
      return;
    }
    this.error.set(null);
    this.isLoading.set(true);

    this.apiService
      .resetPassword(this.email() ?? '', this.formGroup().value.password, this.token() ?? '')
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: () => {
          this.isSubmitted.set(true);
        },
        error: () => {
          this.error.set('Hiba, az új jelszó beállításra kiküldött link érvénytelen vagy lejárt, kérem próbálja újra!');
          this.isSubmitted.set(false);
          this.isLoading.set(false);
        },
      });
  }

  private get initForm(): UntypedFormGroup {
    return this.formBuilder.group({
      password: [null, [Validators.required, passwordValidator]],
    });
  }
}
