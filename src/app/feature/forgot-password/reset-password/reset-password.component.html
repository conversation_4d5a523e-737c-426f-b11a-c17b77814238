<section class="forgot-form">
  <div class="wrapper forgot-form-wrapper">
    <div class="forgot-form-header">
      <h1 class="forgot-form-header-title"><PERSON><PERSON> j<PERSON><PERSON> be<PERSON>llítása</h1>
      <p class="forgot-form-header-text">
        <PERSON><PERSON> elfelej<PERSON>tt j<PERSON> v<PERSON>llításának utolsó lépések<PERSON>t, k<PERSON>rem, adjon meg egy ú<PERSON>, amivel a jövőben bejelentkezni kíván a felhasználói
        fiókjába.
      </p>
    </div>
    @if (!hasValidParams()) {
      <div class="general-form-error"><PERSON><PERSON>, érvénytelen link, kérj<PERSON>k ellenőrizze a böngészőben megadott hivatkozást!</div>
    } @else {
      <form [formGroup]="formGroup()" (ngSubmit)="resetPassword()">
        <app-password-control [label]="'Új j<PERSON>'" [placeholder]="'Adja meg az ú<PERSON> j<PERSON>'"></app-password-control>
        @if (error()) {
          <div class="general-form-error">
            {{ error() }}
          </div>
        }
        <div class="forgot-form-buttons">
          <app-simple-button class="forgot-form-button w-100" [isSubmit]="true" [disabled]="isLoading()"
            >{{ isLoading() ? 'Kérem, várjon...' : 'Folytatás' }}
          </app-simple-button>
          <app-simple-button class="forgot-form-button w-100" [routerLink]="'/bejelentkezes'" [color]="'secondary'">Mégsem</app-simple-button>
        </div>
      </form>
    }
  </div>
</section>

@if (isSubmitted()) {
  <app-popup>
    <div class="success-modal-panel">
      <div class="forgot-form-header">
        <h1 class="forgot-form-header-title">Sikeres jelszóváltoztatás</h1>
        <div class="success-modal-text-box">
          <p class="forgot-form-header-text top">Kedves Olvasónk!</p>
          <p class="forgot-form-header-text">Sikeresen módosította jelszavát.</p>
          <p class="forgot-form-header-text">További kellemes olvasást kívánunk!</p>
        </div>
      </div>
      <app-simple-button class="w-100" [routerLink]="'/bejelentkezes'">Tovább a bejelentkezéshez</app-simple-button>
    </div>
  </app-popup>
}
