import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import { map, Observable, of } from 'rxjs';
import { ApiResult, BackendCommentWithArticle, ApiResponseMetaList } from '@trendency/kesma-ui';
import { AuthService, SecureApiService } from 'src/app/shared';
import { profileComments } from 'src/app/shared/utils/profile.utils';

export const profileCommentsResolver: ResolveFn<Observable<ApiResult<BackendCommentWithArticle[], ApiResponseMetaList>>> = () => {
  const authService = inject(AuthService);
  const user = authService.currentUser;
  const secureApiService = inject(SecureApiService);

  const params = { page_limit: 0, rowCount_limit: 5 };
  if (user()?.uid) {
    return secureApiService.getMyComments(params, user()!.uid).pipe(
      map(({ data, meta }) => ({
        data: data.map((articles) => profileComments(articles)),
        meta,
      }))
    );
  } else {
    return of({
      data: [],
      meta: {} as ApiResponseMetaList,
    });
  }
};
