@use 'shared' as *;

:host {
  width: 100%;
}

.profile {
  display: flex;
  flex-direction: column;
  row-gap: 32px;
  max-width: 864px;
  margin-bottom: 60px;

  &-title {
    font-size: 36px;
    font-weight: 800;
    line-height: 42px;

    @include media-breakpoint-down(md) {
      font-size: 26px;
      line-height: 28px;
    }
  }

  &-articles-of-comments {
    padding-top: 32px;
    border-top: 1px solid var(--kui-gray-300);
    display: flex;
    flex-direction: column;
    row-gap: 12px;
  }
}
