import { HttpErrorResponse } from '@angular/common/http';
import { ChangeDetectionStrategy, Component, DestroyRef, inject, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { BackendFormErrors, markControlsTouched, nonWhitespaceOnlyValidator, passwordValidator, User } from '@trendency/kesma-ui';
import { catchError, switchMap, throwError } from 'rxjs';
import {
  AppInputControlComponent,
  AppPasswordControlComponent,
  AuthService,
  BorsSimpleButtonComponent,
  PopupComponent,
  SecureApiService,
} from 'src/app/shared';

@Component({
  selector: 'app-profile-settings',
  imports: [FormsModule, ReactiveFormsModule, RouterLink, AppInputControlComponent, AppPasswordControlComponent, BorsSimpleButtonComponent, PopupComponent],
  templateUrl: './profile-settings.component.html',
  styleUrl: './profile-settings.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ProfileSettingsComponent {
  private readonly authService = inject(AuthService);
  private readonly formBuilder = inject(UntypedFormBuilder);
  private readonly secureApiService = inject(SecureApiService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly router = inject(Router);

  user = this.authService.currentUser;
  isLoading = signal<boolean>(false);
  error = signal<string | null>(null);
  isSuccesModifyData = signal<boolean>(false);
  isShowDeleteProfileConfirm = signal<boolean>(false);
  readonly formGroup = signal<UntypedFormGroup>(this.initForm);

  private get initForm(): UntypedFormGroup {
    return this.formBuilder.group({
      username: [this.user()?.username ?? null, [Validators.required, nonWhitespaceOnlyValidator, Validators.minLength(6), Validators.maxLength(100)]],
      newPassword: [null, [Validators.minLength(6), passwordValidator]],
      oldPassword: [null, [Validators.required]],
    });
  }

  save(): void {
    if (this.formGroup()) {
      markControlsTouched(this.formGroup());
    }

    if (!this.formGroup().valid) {
      return;
    }

    this.error.set(null);
    this.isLoading.set(true);

    this.secureApiService
      .editCurrentUser(this.formGroup().value)
      .pipe(
        catchError((response: HttpErrorResponse) => {
          const backendErrors = response.error as BackendFormErrors;
          let isErrorHandled = false;

          if (backendErrors?.form?.errors?.children) {
            for (const [errorKey, value] of Object.entries(backendErrors.form.errors.children)) {
              // User with the same username is already registered
              if (errorKey === 'userName' && !!value.errors) {
                this.formGroup().get('username')?.setErrors({ usernameInUse: true });
                isErrorHandled = true;
              }
              // User old password is not correct
              if (errorKey === 'oldPassword' && !!value.errors) {
                this.formGroup().get('oldPassword')?.setErrors({ invalidOldPassword: true });
                isErrorHandled = true;
              }
            }
          }
          if (!isErrorHandled) {
            this.error.set('Ismeretlen hiba!');
          }
          this.isLoading.set(false);
          return throwError(() => 'Error');
        }),
        switchMap(() => this.secureApiService.getCurrentUser()),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe((user: User) => {
        this.formGroup().get('oldPassword')?.reset();
        this.authService.setCurrentUser(user);
        this.isLoading.set(false);
        this.isSuccesModifyData.set(true);
      });
  }

  logout(): void {
    this.authService.invalidate().subscribe(() => {
      this.router.navigate(['/', 'bejelentkezes']);
    });
  }
}
