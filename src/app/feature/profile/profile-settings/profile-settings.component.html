<section class="profile">
  <h2 class="profile-title">Profilom</h2>
  @if (formGroup()) {
    <form class="profile-form" (ngSubmit)="save()" [formGroup]="formGroup()">
      <div class="profile-form-row">
        <div class="profile-form-email-label">Email cím</div>
        <span class="profile-form-email-addres">{{ user()?.email }}</span>
      </div>
      <div class="profile-form-row">
        <app-input-control controlName="username" labelText="Felhasználónév (kötelező)" placeholder="Adja meg a felhasználónevét"> </app-input-control>
        <p class="profile-form-row-comment">
          Ez a becenév fog megjelenni a hozzászólásoknál, mely maximum 100 karakter hosszú lehet. Legalább 6 karakterből kell állnia, és a következőket
          tartalmazhatja: kis- és na<PERSON>, s<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON><PERSON><PERSON>, als<PERSON><PERSON>.
        </p>
      </div>
      <div class="profile-form-row">
        <app-password-control [label]="'Új jelsz<PERSON>'" [placeholder]="'Adja meg az új jelszavát'" [formControlNamee]="'newPassword'"></app-password-control>
        <p class="profile-form-row-comment">A választott jelszónak legalább 6 karakterből kell állnia, és tartalmaznia kell kisbetűt, nagybetűt és számot.</p>
      </div>
      <div class="profile-form-row">
        <app-password-control
          [isRequired]="true"
          [label]="'Jelenlegi jelszó'"
          [placeholder]="'Adja meg a jelenlegi jelszavát'"
          [formControlNamee]="'oldPassword'"
        ></app-password-control>
        <p class="profile-form-row-comment">
          A felhasználónév megváltoztatása vagy az új jelszó beállítása előtt biztonsági okokból meg kell adnia a jelenleg használt jelszavát.
        </p>
      </div>
      <div class="profile-form-row">
        <app-simple-button [isSubmit]="true">{{ isLoading() ? 'Kérjük várjon...' : 'Adatok módosítása' }}</app-simple-button>
      </div>
    </form>
  }
  <div class="divider"></div>
  <h2 class="profile-title">Kijelentkezés</h2>
  <div>
    <p class="profile-logout-text">Jelentkezzen ki a profiljából.</p>
    <app-simple-button (click)="logout()">Kijelentkezés</app-simple-button>
  </div>
  <div class="divider"></div>
  <h2 class="profile-title">Profil törlése</h2>
  <div>
    <p class="profile-delete-text">
      FIGYELEM! Bors-profiljával együtt az összes személyes adatát is töröljük. A törlés nem visszavonható! Adatvédelmi jogaival kapcsolatos további
      információkért kérjük, tekintse meg az
      <a class="profile-privacy" routerLink="/adatvedelem">adatvédelmi nyilatkozatunkat</a>
    </p>
    <app-simple-button class="profile-delete-button" routerLink="/profil/torles" [color]="'secondary'">Profil törlése</app-simple-button>
  </div>
</section>

@if (isSuccesModifyData()) {
  <app-popup>
    <div class="success-modal-panel">
      <div class="succes-form-header">
        <h1 class="succes-form-header-title">Sikeres Adatmódosítás</h1>
        <div class="success-modal-text-box">
          <p class="succes-form-header-text top">Kedves Olvasónk!</p>
          <p class="succes-form-header-text">Sikeresen módosította adatait.</p>
          <p class="succes-form-header-text">További kellemes olvasást kívánunk!</p>
        </div>
      </div>
      <app-simple-button class="w-100" (click)="isSuccesModifyData.set(false)">Tovább a személyes profilhoz</app-simple-button>
    </div>
  </app-popup>
}
