@use 'shared' as *;

.profile {
  display: flex;
  flex-direction: column;
  row-gap: 32px;
  max-width: 639px;
  margin-bottom: 32px;

  @include media-breakpoint-down(sm) {
    margin-bottom: 86px;
  }

  &-title {
    font-size: 36px;
    font-weight: 700;

    @include media-breakpoint-down(sm) {
      font-size: 24px;
    }
  }

  &-form {
    display: flex;
    flex-direction: column;
    row-gap: 32px;

    &-email-label {
      font-size: 16px;
      font-weight: 600;
      line-height: 160%;
      margin-bottom: 10px;
    }

    &-email-addres {
      font-size: 14px;
    }

    &-row-comment {
      font-size: 12px;
      line-height: 160%;
      margin-top: 8px;
    }
  }

  &-logout-text {
    font-size: 16px;
    font-weight: 600;
    line-height: 160%;
    margin-bottom: 16px;
  }

  &-delete {
    &-text {
      font-size: 14px;
      line-height: 160%;
      margin-bottom: 16px;
    }

    &-button {
      margin-bottom: 60px;

      @include media-breakpoint-down(sm) {
        margin-bottom: 32px;
      }
    }
  }

  &-privacy {
    color: var(--kui-red-500);
    text-decoration: underline;
    &:hover {
      color: var(--kui-red-550);
      text-decoration: none;
    }
  }
}

app-simple-button {
  margin: 0;
}

.divider {
  height: 1px;
  background: var(--kui-gray-200-o20);
}

.success-modal-panel {
  background-color: var(--kui-gray-100);
  width: 100%;
  max-width: 480px;
  padding: 60px 32px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32px;

  @include media-breakpoint-down(md) {
    padding: 32px 16px;
    margin: 0 16px;
  }
}

.succes-form-header {
  &-title {
    text-align: center;
    margin-bottom: 60px;
  }

  &-text {
    text-align: center;
    font-size: 16px;
    font-weight: 400;
    line-height: 160%;

    &.top {
      margin-bottom: 25px;
    }
  }
}
