import { ChangeDetectionStrategy, Component, inject, signal } from '@angular/core';
import { RouterLink } from '@angular/router';
import { Article } from '@trendency/kesma-ui';
import { BackendCommentWithArticle } from '@trendency/kesma-ui';
import { backendDateToDate, BaseComponent, buildArticleUrl, IconComponent } from '@trendency/kesma-ui';
import { FormatPipeModule } from 'ngx-date-fns';
import { AuthService } from 'src/app/shared';

@Component({
  selector: 'app-comments',
  imports: [IconComponent, FormatPipeModule, RouterLink],
  templateUrl: './comments.component.html',
  styleUrl: './comments.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CommentsComponent extends BaseComponent<BackendCommentWithArticle & { lastReportStatus?: string }> {
  private readonly authService = inject(AuthService);
  user = this.authService.currentUser;

  articleLink = signal<string[]>([]);
  publishDate = signal<Date | null>(null);

  isDate(value: string | Date): value is Date {
    return value instanceof Date;
  }

  override setProperties(): void {
    if (!this.data) return;
    this.articleLink.set(this.data ? buildArticleUrl(this.data.article as unknown as Article) : []);
    const finalDate = this.isDate(this.data.createdAt) ? this.data.createdAt : backendDateToDate(this.data.createdAt as string);
    this.publishDate.set(finalDate);
  }
}
