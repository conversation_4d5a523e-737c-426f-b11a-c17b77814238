@use 'shared' as *;

.comment {
  display: flex;
  flex-direction: column;
  row-gap: 10px;
  border-left: 1px solid var(--kui-black-950);
  padding-left: 32px;

  &-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    &-left {
      display: flex;
      flex-direction: column;
      row-gap: 10px;
    }
  }

  &-username {
    font-size: 20px;
    font-weight: 700;
    line-height: 20px;

    @include media-breakpoint-down(md) {
      font-size: 18px;
    }
  }

  &-date {
    font-size: 14px;
    font-weight: 400;
    line-height: normal;

    @include media-breakpoint-down(md) {
      font-size: 12px;
    }
  }

  &-status {
    background-color: var(--kui-red-300);
    font-size: 12px;
    font-weight: 600;
    line-height: 18px;
    text-transform: uppercase;
    padding: 5px 8px;

    &-approved {
      background-color: var(--kui-green-100);
    }
    &.waiting {
      background-color: var(--kui-yellow-500);
    }
  }

  &-text {
    font-size: 16px;
    font-weight: 400;
    line-height: 22px;

    @include media-breakpoint-down(md) {
      font-size: 14px;
      line-height: 20px;
    }
  }

  &-link {
    display: flex;
    flex-direction: row;
    column-gap: 10px;
    font-size: 16px;
    font-weight: 700;
    line-height: 20px;
    color: var(--kui-red-500);
  }

  .arrow-right {
    fill: var(--kui-red-500);
  }
}
