@use 'shared' as *;

:host ::ng-deep ng-select {
  .ng-value {
    font-size: 20px;
  }

  .ng-select-container {
    height: 50px !important;
  }
}

.profile {
  margin-top: 32px;

  @include media-breakpoint-down(md) {
    margin: 32px 15px 0 15px;
  }
}

.wrapper {
  display: flex;
  flex-direction: row;
  gap: 32px;

  @include media-breakpoint-down(md) {
    flex-direction: column;
  }

  .profile-menu {
    width: 304px;
    display: flex;
    flex-direction: column;

    @include media-breakpoint-down(md) {
      width: 100%;
    }

    &-item {
      color: var(--kui-black-950);
      font-size: 20px;
      font-weight: 700;
      border-bottom: 1px solid var(--kui-gray-200-o20);
      padding: 16px 0;

      @include media-breakpoint-down(md) {
        display: none;
      }

      &.active {
        color: var(--kui-red-500);
      }

      &:first-child {
        border-top: 1px solid var(--kui-gray-200-o20);
      }
    }

    &-select {
      height: 50px;
      font-size: 20px;
      font-weight: 700;
      cursor: pointer;

      @include media-breakpoint-up(lg) {
        padding-left: 16px;
        display: none;
      }

      &-option {
        height: 50px;
        font-size: 20px;
        font-weight: 700;
        background: var(--kui-white);
        padding-top: 8px;
        margin-top: 15px;
        line-height: 40px;
      }
    }
  }
}

.hidden {
  display: none;
}

.wrapper {
  padding: 0;

  @include media-breakpoint-down(md) {
    padding-top: 16px;
  }
}
