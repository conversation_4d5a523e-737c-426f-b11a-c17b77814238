<h2 class="delete-profile-title">PROFIL TÖRLÉSE</h2>
<div class="delete-profile-warning">FIGYELEM! Bors-profilj<PERSON><PERSON> együtt az összes személyes adatát is töröljük. A törlés nem visszavonható!</div>

@if (formGroup()) {
  <form [formGroup]="formGroup()" (ngSubmit)="isShowDeleteProfileConfirm.set(true)">
    <app-password-control
      [isRequired]="true"
      [label]="'Jelenlegi jelszó'"
      [placeholder]="'Adja meg a jelenlegi jelszavát'"
      [formControlNamee]="'oldPassword'"
    ></app-password-control>
    <p class="profile-form-row-comment">A profil törlése előtt biztonsági okokból meg kell adnia a jelenleg használt jelszavát.</p>
    <app-simple-button class="w-100" [isSubmit]="true">{{ isLoading() ? 'Kérjük várjon...' : 'Profil törlése' }}</app-simple-button>
  </form>
}

@if (isShowDeleteProfileConfirm()) {
  <app-popup>
    <div class="success-modal-panel">
      <div class="succes-form-header">
        <h1 class="succes-form-header-title">PROFIL TÖRLÉSE</h1>
        <div class="success-modal-text-box">
          <p class="succes-form-header-text">Biztos benne, hogy törli Borsonline-profilját és az összes személyes adatát?</p>
        </div>
      </div>
      <app-simple-button class="w-100" (click)="deleteAccount()">{{ isLoading() ? 'Kérjük várjon...' : 'Profil törlése' }}</app-simple-button>
      <app-simple-button class="w-100" [color]="'secondary'" (click)="isShowDeleteProfileConfirm.set(false)">Mégse</app-simple-button>
    </div>
  </app-popup>
}
