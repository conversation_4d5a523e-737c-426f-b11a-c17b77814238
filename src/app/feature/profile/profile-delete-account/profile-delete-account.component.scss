@use 'shared' as *;

:host {
  max-width: 384px;
  margin: auto;
  padding: 32px 0;
}

.delete-profile {
  &-title {
    margin-bottom: 20px;
  }

  &-warning {
    color: var(--kui-red-500);
    font-size: 16px;
    margin-bottom: 20px;
  }
}

.profile-form-row-comment {
  margin: 8px 0 16px;
  font-size: 12px;
  line-height: 160%;
}

.success-modal-panel {
  background-color: var(--kui-gray-100);
  width: 100%;
  max-width: 480px;
  padding: 60px 32px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32px;

  @include media-breakpoint-down(md) {
    padding: 32px 16px;
    margin: 0 16px;
  }
}

.succes-form-header {
  &-title {
    text-align: center;
    margin-bottom: 60px;
  }

  &-text {
    text-align: center;
    font-size: 16px;
    font-weight: 400;
    line-height: 160%;

    &.top {
      margin-bottom: 25px;
    }
  }
}
