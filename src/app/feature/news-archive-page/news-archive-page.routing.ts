import { Routes } from '@angular/router';
import { NewsArchiveComponent } from './components/news-archive/news-archive.component';
import { MonthNewsComponent } from './components/month-news/month-news.component';
import { NewsArchiveResolver } from './api/news-archive-page.resolver';
import { MonthNewsResolver } from './api/month-news-page.resolver';

export const NEWS_ARCHIVE_PAGE_ROUTES: Routes = [
  {
    path: '',
    pathMatch: 'full',
    component: NewsArchiveComponent,
    resolve: { data: NewsArchiveResolver },
  },
  {
    path: ':year/:month',
    component: MonthNewsComponent,
    resolve: { data: MonthNewsResolver },
  },
  {
    path: ':year/:month/:day',
    component: MonthNewsComponent,
    resolve: { data: MonthNewsResolver },
  },
];
