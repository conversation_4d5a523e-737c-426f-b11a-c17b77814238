import { ResolveFn, Router } from '@angular/router';
import { catchError, throwError } from 'rxjs';
import { inject } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { NewsArchiveTimeLine } from '../definitions/news-archive-page.definitions';
import { NewsArchiveService } from './news-archive-page.service';

export const NewsArchiveResolver: ResolveFn<NewsArchiveTimeLine[]> = () => {
  const newsArchiveService = inject(NewsArchiveService);
  const router = inject(Router);
  return newsArchiveService.getNewsArchiveMonth$().pipe(
    catchError((error: HttpErrorResponse) => {
      router.navigate(['/', '404'], {
        skipLocationChange: true,
      });
      return throwError(error);
    })
  );
};
