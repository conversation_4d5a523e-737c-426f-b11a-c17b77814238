import { ActivatedRouteSnapshot, ResolveFn } from '@angular/router';
import { inject } from '@angular/core';
import { NewsArchiveService } from './news-archive-page.service';
import { ArchiveResponse, GroupedArchiveArticlesByColumn, NewsArchiveArticleColumns } from '../definitions/news-archive-page.definitions';
import { ApiResponseMetaList } from '@trendency/kesma-ui';

export const MonthNewsResolver: ResolveFn<ArchiveResponse<GroupedArchiveArticlesByColumn[], ApiResponseMetaList, NewsArchiveArticleColumns[]>> = (
  route: ActivatedRouteSnapshot
) => {
  const newsArchiveService = inject(NewsArchiveService);
  const year = route.params['year'];
  const month = route.params['month'];
  const day = route.params['day'];
  return newsArchiveService.getArticlesByDate$(year, month, day);
};
