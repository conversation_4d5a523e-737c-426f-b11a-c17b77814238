import { ArticleSearchResult } from '@trendency/kesma-ui';

export type NewsArchiveTimeLine = Readonly<{
  year?: number;
  months?: NewsArchiveTimeLineMonths[];
}>;

export type NewsArchiveTimeLineMonths = Readonly<{
  month?: string;
  date?: Date;
}>;

export type NewsArchiveArticleColumns = Readonly<{
  columnTitle: string;
  columnSlug: string;
}>;

export type ArchiveResponse<T, M, C> = Readonly<{
  articles: T;
  meta: M;
  columns: C;
}>;

export type GroupedArchiveArticlesByColumn = Readonly<{
  column: NewsArchiveArticleColumns;
  articles: ArticleSearchResult[];
}>;
