import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationStart, Router, RouterLink } from '@angular/router';
import { map, Observable } from 'rxjs';
import { ApiResponseMetaList, ArticleCard, buildArticleUrl, createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';
import { format } from 'date-fns';
import { SeoService, UtilService } from '@trendency/kesma-core';
import { AsyncPipe } from '@angular/common';
import { createBorsOnlineTitle, defaultMetaInfo, FormatArticlePublishDatePipe, ArchiveDatePickerComponent } from '../../../../shared';
import { ArchiveResponse, GroupedArchiveArticlesByColumn, NewsArchiveArticleColumns } from '../../definitions/news-archive-page.definitions';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-month-news',
  templateUrl: './month-news.component.html',
  styleUrls: ['./month-news.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ArchiveDatePickerComponent, RouterLink, AsyncPipe, FormatArticlePublishDatePipe],
})
export class MonthNewsComponent implements OnInit {
  selectedDate: Date;
  private readonly activatedRoute = inject(ActivatedRoute);
  private readonly seo = inject(SeoService);
  private readonly destroyRef = inject(DestroyRef);
  readonly utilService = inject(UtilService);
  private readonly router = inject(Router);

  monthlyArticles$: Observable<ArchiveResponse<GroupedArchiveArticlesByColumn[], ApiResponseMetaList, NewsArchiveArticleColumns[]>> =
    this.activatedRoute.data.pipe(map(({ data }) => data));

  formatArticleDate(articleDate: string): Date {
    return articleDate ? new Date(articleDate) : new Date();
  }

  ngOnInit(): void {
    this.router.events.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((event) => {
      if (event instanceof NavigationStart && event.navigationTrigger === 'popstate') {
        this.router.navigate(['/', 'hirarchivum']);
      }
    });
    const { year, month, day } = this.activatedRoute.snapshot.params;
    this.selectedDate = new Date(`${year}-${month}-${day || '01'}`);
    this.setMetaData(year, month, day);
  }

  onSelectedDateChange(date: Date): void {
    const year = format(date, 'yyyy');
    const month = format(date, 'MM');
    const day = format(date, 'dd');

    this.router.navigate(['hirarchivum', year, month, day]);
  }

  getArticleLink(data: ArticleCard): string | string[] {
    return data && buildArticleUrl(data);
  }

  getCategoryLink(data: NewsArchiveArticleColumns): string | string[] {
    return data && ['/', 'rovat', data?.columnSlug];
  }

  setMetaData(year: string, month: string, day?: string): void {
    const title = createBorsOnlineTitle('Hónap hírei');
    this.seo.setMetaData({
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    });
    const canonicalUrlParts = day ? `${year}/${month}/${day}` : `${year}/${month}`;

    const canonical = createCanonicalUrlForPageablePage(`hirarchivum/${canonicalUrlParts}`, undefined);
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical);
    }
  }
}
