@if (newsArchiveData$ | async; as newsArchiveData) {
  <div class="wrapper content-wrapper">
    <h1 class="archive-title">HÍRARCHÍVUM</h1>
    <div class="years-container">
      @for (years of newsArchiveData; track years.year) {
        <div class="months-container">
          <span class="year-title">{{ years?.year }}</span>
          <div class="months">
            @for (months of years?.months; track months.month) {
              <a [routerLink]="[years?.year, months?.month]"> {{ months?.date | dfnsFormat: 'MMM' }}</a>
            }
          </div>
        </div>
      }
    </div>
  </div>
}
