import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit, Signal, signal } from '@angular/core';
import { ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { createCanonicalUrlForPageablePage, KesmaFormControlComponent, markControlsTouched, PortalConfigSetting } from '@trendency/kesma-ui';
import { IMetaData, SeoService, StorageService } from '@trendency/kesma-core';
import {
  ApiService,
  AppInputControlComponent,
  AppPasswordControlComponent,
  AuthService,
  BackendAllowedLoginMethodsResponse,
  BorsSimpleButtonComponent,
  createBorsOnlineTitle,
  defaultMetaInfo,
  LoginRedirectToken,
  PortalConfigService,
} from '../../shared';
import { environment } from '../../../environments/environment';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { ReCaptchaV3Service } from 'ngx-captcha';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, ReactiveFormsModule, AppInputControlComponent, AppPasswordControlComponent, BorsSimpleButtonComponent, KesmaFormControlComponent],
})
export class LoginComponent implements OnInit {
  private readonly formBuilder = inject(UntypedFormBuilder);
  private readonly seoService = inject(SeoService);
  private readonly reCaptchaV3Service = inject(ReCaptchaV3Service);
  private readonly apiService = inject(ApiService);
  private readonly authService = inject(AuthService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly router = inject(Router);
  private readonly storageService = inject(StorageService);
  private readonly route = inject(ActivatedRoute);
  private readonly portalConfigService = inject(PortalConfigService);

  readonly formGroup = signal<UntypedFormGroup>(this.initForm);
  readonly error = signal<string | null>(null);
  readonly isLoading = signal<boolean>(false);

  readonly allowedLoginMethods: Signal<BackendAllowedLoginMethodsResponse | undefined> = toSignal(this.apiService.getAllowedLoginMethods());
  readonly enableRememberMe: Signal<boolean | undefined> = signal(this.portalConfigService.isConfigSet(PortalConfigSetting.PORTAL_USER_ENABLE_REMEMBER_ME));

  ngOnInit(): void {
    this.setMetaData();
  }

  login(): void {
    if (this.formGroup()) {
      markControlsTouched(this.formGroup());
    }
    if (this.formGroup().invalid) {
      return;
    }
    this.error.set(null);
    this.isLoading.set(true);
    this.reCaptchaV3Service.execute(
      environment.googleSiteKey ?? '',
      'app_publicapi_portal_user_login',
      (recaptchaToken: string) => {
        this.authService
          .authenticate(this.formGroup().value, recaptchaToken)
          .pipe(takeUntilDestroyed(this.destroyRef))
          .subscribe({
            next: () => {
              this.isLoading.set(false);
              const { redirect } = this.route.snapshot.queryParams;
              const redirectUrl = redirect ?? this.storageService.getLocalStorageData(LoginRedirectToken);
              this.storageService.setLocalStorageData(LoginRedirectToken, null);
              if (redirectUrl) {
                if (this.isAbsoluteUrl(redirectUrl)) {
                  document.location.href = redirectUrl;
                } else {
                  this.router.navigate([redirectUrl]).then();
                }
              } else {
                this.router.navigate(['/']).then();
              }
            },
            error: (response: HttpErrorResponse) => {
              if (response?.error?.data?.message === 'Email not verified') {
                this.error.set('Kérjük, erősítse meg regisztrációját az e-mail-ben kapott link segítségével!');
              } else {
                this.error.set('Hibás bejelentkezési adatok, kérem, próbálja újra!');
              }
              this.isLoading.set(false);
            },
          });
      },
      {
        useGlobalDomain: false,
      },
      () => {
        this.error.set('Captcha: Robot ellenőrzés hiba!');
        this.isLoading.set(false);
      }
    );
  }

  private get initForm(): UntypedFormGroup {
    return this.formBuilder.group({
      emailOrUsername: [null, [Validators.required]],
      password: [null, [Validators.required]],
      rememberMe: [false],
    });
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('bejelentkezes');
    if (canonical) this.seoService.updateCanonicalUrl(canonical);
    const title = createBorsOnlineTitle('Bejelentkezés');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seoService.setMetaData(metaData);
  }
  private isAbsoluteUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch (e) {
      return false;
    }
  }
}
