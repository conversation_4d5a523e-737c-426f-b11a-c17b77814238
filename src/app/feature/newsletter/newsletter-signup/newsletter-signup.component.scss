@use 'shared' as *;

:host {
  display: flex;
  justify-content: center;
  margin-block: 64px;
  color: var(--kui-black-950);
  width: 100%;
  @include media-breakpoint-down(md) {
    margin-block: 35px 16px;
  }
  .newsletter {
    background-color: var(--kui-gray-250);
    max-width: 640px;
    margin-inline: 16px;
    padding: 64px 112px;
    @include media-breakpoint-down(md) {
      padding: 32px 16px;
    }
    &-title {
      font-size: 36px;
      font-weight: 800;
      line-height: 42px;
      text-align: center;
      margin-bottom: 16px;
      @include media-breakpoint-down(md) {
        font-size: 26px;
        line-height: 28px;
      }
    }
    &-text {
      font-size: 18px;
      font-weight: 700;
      line-height: 26px;
      text-align: center;
      margin-bottom: 60px;
      @include media-breakpoint-down(md) {
        margin-bottom: 32px;
        font-size: 16px;
        line-height: normal;
      }
    }
  }
  .automizy-form-input-box {
    display: flex;
    flex-direction: column;
    margin-bottom: 32px;
    gap: 8px;
  }
  .automizy-form-input-label {
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
  }
  .automizy-form-input {
    height: 40px;
    padding: 16px;
    border: 1px solid var(--kui-black-950);
    background-color: var(--kui-gray-250);
    font-size: 16px;
    line-height: 24px;
    &::placeholder {
      color: var(--kui-black-950);
    }
  }
  .automizy-form-button {
    background-color: var(--kui-red-500);
    color: var(--kui-white);
    font-size: 16px;
    font-weight: 700;
    line-height: 20px;
    padding: 10px 16px;
    cursor: pointer;
    width: 100%;
    &:hover {
      background-color: var(--kui-red-550);
    }
  }
  .marketing,
  .automizy-form-privacy {
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    &-link {
      color: var(--kui-red-500);
      text-decoration-line: underline;
      text-underline-offset: 2.5px;
      font-weight: 700;

      &:hover {
        text-decoration: none;
        color: var(--kui-red-550);
      }
    }
  }
  .marketing-wrapper {
    flex-direction: row;
    margin-top: 16px;
    gap: 16px;
  }
  .checkbox {
    height: 20px;
    width: 20px;
    flex-shrink: 0;
    margin: 2px;
    accent-color: var(--kui-red-500);
    border-radius: 4px;
  }
}
