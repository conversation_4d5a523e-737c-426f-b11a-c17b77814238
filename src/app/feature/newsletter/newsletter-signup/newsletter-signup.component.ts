import { ChangeDetectionStrategy, Component, inject, OnInit, Renderer2 } from '@angular/core';
import { createBorsOnlineTitle, defaultMetaInfo } from '../../../shared';
import { BypassPipe, IMetaData, SeoService } from '@trendency/kesma-core';
import { createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';
import { DOCUMENT } from '@angular/common';

@Component({
  selector: 'app-newsletter-signup',
  templateUrl: './newsletter-signup.component.html',
  styleUrls: ['./newsletter-signup.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [BypassPipe],
  host: { ngSkipHydration: 'true' },
})
export class NewsletterSignupComponent implements OnInit {
  private readonly renderer2 = inject(Renderer2);
  private readonly seoService = inject(SeoService);
  private readonly document = inject(DOCUMENT);

  readonly recaptcha = 'https://www.google.com/recaptcha/api.js';
  readonly action = 'https://api.automizy.com/v2/forms/submit/ebl32vnlqSiEA4F7VGdDAftIlHGT8-Oxly7wF1gUs2o/kpiN9BYTBFpXUoVusFcv3t2i6GU';

  ngOnInit(): void {
    this.renderRecaptcha();
    this.setMetaData();
  }

  private renderRecaptcha(): void {
    const script = this.renderer2.createElement('script');
    script.type = 'text/javascript';
    script.src = this.recaptcha;
    script.text = ``;
    this.renderer2.appendChild(this.document.body, script);
  }

  private setMetaData(): void {
    const title = createBorsOnlineTitle('Hírlevél-feliratkozás');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seoService.setMetaData(metaData);
    const canonical = createCanonicalUrlForPageablePage('hirlevel-feliratkozas');
    if (canonical) {
      this.seoService.updateCanonicalUrl(canonical);
    }
  }
}
