import { ChangeDetectionStrategy, Component, inject, OnInit } from '@angular/core';
import { SeoService } from '@trendency/kesma-core';
import { createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';
import { createBorsOnlineTitle, defaultMetaInfo } from '../../../shared';
import { toSignal } from '@angular/core/rxjs-interop';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { map } from 'rxjs/operators';

@Component({
  selector: 'app-newsletter-success',
  templateUrl: './newsletter-success.component.html',
  styleUrls: ['./newsletter-success.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink],
})
export class NewsletterSuccessComponent implements OnInit {
  private readonly route = inject(ActivatedRoute);
  private readonly seoService = inject(SeoService);

  pageTextSuffix = toSignal<string>(this.route.data.pipe(map(({ pageTextSuffix }) => pageTextSuffix)));

  ngOnInit(): void {
    let pageTitle = `Hírlevél leiratkozás`;
    if (this.pageTextSuffix() === 'feliratkozás') {
      pageTitle = 'Hírlevél feliratkozás megerősítése sikeres';
    }

    const title = createBorsOnlineTitle(pageTitle);
    this.seoService.setMetaData({
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    });
    let canonical = '';
    switch (this.pageTextSuffix()) {
      case 'feliratkozás':
        canonical = createCanonicalUrlForPageablePage('hirlevel-feliratkozas-megerositese-sikeres');
        break;
      case 'leiratkozás':
        canonical = createCanonicalUrlForPageablePage('hirlevel-leiratkozas');
        break;
    }
    canonical && this.seoService.updateCanonicalUrl(canonical);
  }
}
