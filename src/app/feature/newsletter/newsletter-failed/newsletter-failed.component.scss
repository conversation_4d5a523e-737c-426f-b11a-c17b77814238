@use 'shared' as *;

:host {
  display: flex;
  justify-content: center;
  margin-block: 64px;
  color: var(--kui-black-950);
  text-align: center;
  width: 100%;
  @include media-breakpoint-down(md) {
    margin-block: 35px 16px;
  }
  .newsletter {
    background-color: var(--kui-gray-250);
    max-width: 640px;
    margin-inline: 16px;
    padding: 64px 112px;
    width: 100%;
    @include media-breakpoint-down(md) {
      padding: 32px 16px;
    }
    &-title {
      font-size: 36px;
      font-weight: 800;
      line-height: 42px;
      max-width: 440px;
      margin: 0 auto;
      @include media-breakpoint-down(md) {
        font-size: 26px;
        line-height: 28px;
        max-width: 311px;
      }
    }
    &-button {
      font-size: 16px;
      font-weight: 700;
      line-height: 20px;
      background-color: var(--kui-red-500);
      color: var(--kui-white);
      padding: 10px 16px;
      display: block;
      margin-top: 60px;
      @include media-breakpoint-down(md) {
        margin-top: 32px;
      }
      &:hover {
        background-color: var(--kui-red-550);
      }
    }
  }
  .wrapper {
    padding: 0;
  }
}
