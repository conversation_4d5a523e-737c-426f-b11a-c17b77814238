import { ChangeDetectionStrategy, Component, inject, OnInit } from '@angular/core';
import { SeoService } from '@trendency/kesma-core';
import { createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';
import { createBorsOnlineTitle, defaultMetaInfo } from '../../../shared';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-newsletter-failed',
  templateUrl: './newsletter-failed.component.html',
  styleUrls: ['./newsletter-failed.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink],
})
export class NewsletterFailedComponent implements OnInit {
  private readonly seoService = inject(SeoService);

  ngOnInit(): void {
    const title = createBorsOnlineTitle('Hírlevél feliratkozás sikertelen');
    this.seoService.setMetaData({
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    });
    const canonical = createCanonicalUrlForPageablePage('hirlevel-feliratkozas-sikertelen');
    canonical && this.seoService.updateCanonicalUrl(canonical);
  }
}
