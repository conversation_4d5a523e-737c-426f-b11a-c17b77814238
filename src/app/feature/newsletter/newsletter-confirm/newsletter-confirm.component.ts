import { ChangeDetectionStrategy, Component, inject, OnInit } from '@angular/core';
import { SeoService } from '@trendency/kesma-core';
import { createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';
import { createBorsOnlineTitle, defaultMetaInfo } from '../../../shared';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-newsletter-confirm',
  templateUrl: 'newsletter-confirm.component.html',
  styleUrls: ['newsletter-confirm.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink],
})
export class NewsletterConfirmComponent implements OnInit {
  private readonly seoService = inject(SeoService);

  ngOnInit(): void {
    const title = createBorsOnlineTitle('Hírlevél feliratkozás megerősítése');
    this.seoService.setMetaData({
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    });
    const canonical = createCanonicalUrlForPageablePage('hirlevel-feliratkozas-megerositese');
    canonical && this.seoService.updateCanonicalUrl(canonical);
  }
}
