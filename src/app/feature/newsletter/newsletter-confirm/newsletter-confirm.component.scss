@use 'shared' as *;

:host {
  display: flex;
  justify-content: center;
  margin-block: 64px;
  color: var(--kui-black-950);
  text-align: center;
  width: 100%;
  @include media-breakpoint-down(md) {
    margin-block: 35px 16px;
  }
  .confirm {
    background-color: var(--kui-gray-250);
    max-width: 640px;
    margin-inline: 16px;
    padding: 64px 112px;
    @include media-breakpoint-down(md) {
      padding: 32px 16px;
    }
    &-title {
      font-size: 36px;
      max-width: 416px;
      font-weight: 800;
      line-height: 42px;
      margin-bottom: 60px;
      @include media-breakpoint-down(md) {
        font-size: 26px;
        line-height: 28px;
        margin-bottom: 32px;
      }
    }
    &-text {
      max-width: 416px;
      font-size: 16px;
      line-height: 24px;
    }
  }
  .email {
    font-size: 16px;
    font-weight: 700;
    line-height: 20px;
    color: var(--kui-red-500);
    &:hover {
      color: var(--kui-red-550);
    }
  }
  .margin {
    margin-bottom: 32px;
  }
  .wrapper {
    padding: 0;
  }
}
