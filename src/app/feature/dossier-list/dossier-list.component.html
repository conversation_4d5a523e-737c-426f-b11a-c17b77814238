<section>
  <div class="wrapper meta">
    <app-breadcrumb
      [data]="[
        {
          label: meta()?.title | titlecase,
        },
      ]"
    ></app-breadcrumb>
    <app-page-title class="page-title" [title]="meta()?.title | titlecase" />
    <hr class="divider" />
  </div>
  @if (articles()?.length) {
    <div class="wrapper">
      <app-article-card
        class="article-card"
        [styleId]="ArticleCardType.HighlightedSideImgDateTitleLead"
        [data]="articles()?.[0]"
        [useEagerLoad]="true"
      ></app-article-card>
      <hr class="divider" />
    </div>
  }
  <div class="wrapper with-aside">
    <div class="left-column">
      @if (articles()?.length > 1) {
        @for (articleCard of articles() | slice: 1; track articleCard.id; let i = $index) {
          <app-article-card class="article-card" [styleId]="ArticleCardType.SideImgDateTitleLead" [data]="articleCard"></app-article-card>

          @if (i === 0) {
            @if (adverts()?.desktop?.roadblock_1; as ad) {
              <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
            }
            @if (adverts()?.mobile?.mobilrectangle_1; as ad) {
              <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
            }
          }
          @if (i === 2) {
            @if (adverts()?.desktop?.roadblock_2; as ad) {
              <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
            }
            @if (adverts()?.mobile?.mobilrectangle_2; as ad) {
              <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
            }
          }
        }
      }
      @if (meta()?.limitable?.pageMax) {
        <app-pager class="pager" [rowAllCount]="meta()?.limitable?.rowAllCount!" [rowOnPageCount]="meta()?.limitable?.rowOnPageCount!"></app-pager>
      }
    </div>
    <aside>
      <app-sidebar></app-sidebar>
    </aside>
  </div>
</section>
