import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, ResolveFn, Router } from '@angular/router';
import { throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ApiResult, ArticleCard, BackendArticleSearchResult, mapBackendArticleDataToArticleCard, RedirectService } from '@trendency/kesma-ui';
import { ApiService } from '../../shared';

export const DossierListResolver: ResolveFn<ApiResult<ArticleCard[]>> = (route: ActivatedRouteSnapshot) => {
  const apiService = inject(ApiService);
  const redirectService = inject(RedirectService);
  const router = inject(Router);

  const { dossierSlug } = route.params;
  const { page } = route.queryParams;

  const currentPage = page ? parseInt(page, 10) - 1 : 0;

  return apiService.getDossier(dossierSlug, currentPage).pipe(
    map(({ data, meta }) => {
      if (redirectService.shouldBeRedirect(currentPage, data)) {
        redirectService.redirectOldUrl(`dosszie`, false, 302);
      }
      return {
        data: data.map((article: BackendArticleSearchResult & { recommendedTitle?: string }) => ({
          ...mapBackendArticleDataToArticleCard(article),
          title: article?.recommendedTitle || article.title,
        })),
        meta,
      };
    }),
    catchError((error: Error) => {
      router
        .navigate(['/', '404'], {
          state: { errorResponse: JSON.stringify(error) },
          skipLocationChange: true,
        })
        .then();
      return throwError(() => error);
    })
  );
};
