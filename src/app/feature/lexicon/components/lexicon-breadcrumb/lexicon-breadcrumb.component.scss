@use 'shared' as *;

:host {
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 0 4px 4px;

  @include media-breakpoint-down(md) {
    position: absolute;
    z-index: 1;
  }

  @include media-breakpoint-down(sm) {
    gap: 6px;
  }

  .home-icon {
    width: 16px;
    height: 16px;
    vertical-align: top;
  }

  .arrow-icon {
    width: 16px;
    height: 16px;
  }

  .breadcrumb-item {
    display: flex;
    gap: 8px;
    align-items: center;

    @include media-breakpoint-down(sm) {
      gap: 6px;
    }

    a {
      color: $white;
      text-align: right;
      font-size: 12px;
      font-weight: 400;
      line-height: normal;
      text-transform: uppercase;

      @include media-breakpoint-down(sm) {
        font-size: 10px;
      }
    }
  }
}
