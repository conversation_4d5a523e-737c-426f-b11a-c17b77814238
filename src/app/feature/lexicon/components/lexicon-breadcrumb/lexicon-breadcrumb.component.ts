import { ChangeDetectionStrategy, Component, OnChanges } from '@angular/core';
import { BreadcrumbComponent, BreadcrumbItem } from '@trendency/kesma-ui';
import { NgForOf, NgIf, NgTemplateOutlet } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-lexicon-breadcrumb',
  imports: [NgTemplateOutlet, RouterLink, NgIf, NgForOf],
  templateUrl: './lexicon-breadcrumb.component.html',
  styleUrl: './lexicon-breadcrumb.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LexiconBreadcrumbComponent extends BreadcrumbComponent implements OnChanges {
  breadcrumbItems: (BreadcrumbItem & { queryParams?: Record<string, string> })[];

  ngOnChanges(): void {
    if (this.items?.length) {
      this.breadcrumbItems = this.items;
      this.breadcrumbItems.forEach((item) => {
        // We're separating url and query params
        const [url, queryParam] = item?.url?.toString().split('?') || '';

        item.url = url;
        item.queryParams = Object.fromEntries(new URLSearchParams(queryParam));
      });
    }
  }
}
