<div>
  <a routerLink="/">
    <img alt="" class="home-icon" src="assets/images/lexicon/breadcrumb-home.svg" />
  </a>
</div>

<div class="breadcrumb-item">
  <ng-container [ngTemplateOutlet]="separator"></ng-container>
  <a [routerLink]="['/', 'lexikon', 'sztar']">Bors Lexikon</a>
</div>

<div *ngFor="let item of breadcrumbItems" class="breadcrumb-item">
  <ng-container [ngTemplateOutlet]="separator"></ng-container>
  <a *ngIf="item.url; else current" [queryParams]="item.queryParams" [routerLink]="item.url">{{ item.label }}</a>

  <ng-template #current
    ><span>{{ item.label }}</span></ng-template
  >
</div>

<ng-template #separator>
  <img alt="" class="arrow-icon" src="assets/images/lexicon/breadcrumb-arrow.svg" />
</ng-template>
