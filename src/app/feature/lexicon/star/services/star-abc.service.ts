import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ReqService } from '@trendency/kesma-core';
import { ApiResponseMetaList, ApiResult } from '@trendency/kesma-ui';
import { IStarAbc } from '../definitions/star-abc.definitions';
import { map } from 'rxjs/operators';
import { sortAbcLetters } from '../utils/star-abc.utils';

@Injectable({
  providedIn: 'root',
})
export class StarAbcService {
  private readonly reqService = inject(ReqService);

  getStarAbc(): Observable<IStarAbc[]> {
    return this.reqService
      .get<ApiResult<IStarAbc[], ApiResponseMetaList>>(`source/star-dictionary/initials/available-initials`)
      .pipe(map((res: ApiResult<IStarAbc[], ApiResponseMetaList>) => sortAbcLetters(res.data)));
  }
}
