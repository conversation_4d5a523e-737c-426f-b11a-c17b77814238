import { inject, Injectable } from '@angular/core';
import { IHttpOptions, ReqService } from '@trendency/kesma-core';
import { ApiListR<PERSON>ult, ApiResponseMetaList, ApiResult, ArticleCard, ArticleSearchResult, searchResultToArticleCard } from '@trendency/kesma-ui';
import { Birthplaces, IStarCard, IStarHoroscope, SearchSelectItem } from '../definitions/star.definitions';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { paramsToString } from '../../../../shared';

const STAR_COLUMN_SLUG = 'celeb';
const FALLBACK_DATA = {
  data: [],
  meta: {} as ApiResponseMetaList,
};

@Injectable({
  providedIn: 'root',
})
export class StarService {
  private readonly reqService = inject(ReqService);
  chosenDailyStar?: Partial<IStarCard>;

  starNews(params: object): Observable<ApiResult<ArticleCard[]>> {
    return this.reqService
      .get<ApiResult<ArticleSearchResult[]>>(`/content-page/search`, {
        params: {
          column: STAR_COLUMN_SLUG,
          ...params,
        },
      })
      .pipe(
        catchError(() => of(FALLBACK_DATA)),
        map(({ data, meta }) => ({
          data: data?.map((article: ArticleSearchResult & { preTitleColor?: string }) => ({
            ...searchResultToArticleCard(article),
            preTitleColor: article?.preTitleColor,
          })),
          meta,
        }))
      );
  }

  search(params?: object): Observable<ApiListResult<IStarCard>> {
    // BE throws a validation error for the advert tester queryparam
    const filteredParams = { ...params };
    if ('apptest' in filteredParams) {
      delete filteredParams['apptest'];
    }

    const queryParamsString = filteredParams ? paramsToString(filteredParams as Record<string, string | string[]>) : '';

    return this.reqService
      .get<ApiListResult<IStarCard>>(`/star-dictionary/star/search${queryParamsString ? `?${queryParamsString}` : ''}`)
      .pipe(catchError(() => of(FALLBACK_DATA)));
  }

  dailyStars(options?: IHttpOptions): Observable<ApiResult<IStarCard[]>> {
    return this.search({ ...(options ?? {}), is_today_date_of_birth_filter: 'true' });
  }

  horoscopes(options?: IHttpOptions): Observable<ApiResult<IStarHoroscope[]>> {
    return this.reqService.get<ApiResult<IStarHoroscope[]>>(`/horoscopes/list`, options).pipe(catchError(() => of(FALLBACK_DATA)));
  }

  birthplaces(options: IHttpOptions): Observable<ApiResult<Birthplaces[]>> {
    const queryParamsString = options ? paramsToString(options.params as Record<string, string | string[]>) : '';
    return this.reqService
      .get<ApiResult<Birthplaces[]>>(`/source/star-dictionary/birthplaces${queryParamsString ? `?${queryParamsString}` : ''}`)
      .pipe(catchError(() => of(FALLBACK_DATA)));
  }

  occupations(options: IHttpOptions): Observable<ApiResult<SearchSelectItem[]>> {
    const queryParamsString = options ? paramsToString(options.params as Record<string, string | string[]>) : '';
    return this.reqService
      .get<ApiResult<SearchSelectItem[]>>(`/source/star-dictionary/occupations${queryParamsString ? `?${queryParamsString}` : ''}`)
      .pipe(catchError(() => of(FALLBACK_DATA)));
  }

  awards(options: IHttpOptions): Observable<ApiResult<SearchSelectItem[]>> {
    const queryParamsString = options ? paramsToString(options.params as Record<string, string | string[]>) : '';
    return this.reqService
      .get<ApiResult<SearchSelectItem[]>>(`/source/star-dictionary/awards${queryParamsString ? `?${queryParamsString}` : ''}`)
      .pipe(catchError(() => of(FALLBACK_DATA)));
  }
}
