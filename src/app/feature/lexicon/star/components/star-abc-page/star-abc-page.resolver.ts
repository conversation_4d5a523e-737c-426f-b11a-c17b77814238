import { ResolveFn, Router } from '@angular/router';
import { StarAbcService } from '../../services/star-abc.service';
import { inject } from '@angular/core';
import { catchError } from 'rxjs/operators';
import { forkJoin, Observable, of, throwError } from 'rxjs';
import { StarService } from '../../services/star.service';
import { ApiListResult } from '@trendency/kesma-ui';
import { IStarCard } from '../../definitions/star.definitions';
import { IStarAbc } from '../../definitions/star-abc.definitions';

export type StarAbcResolverData = {
  starAbc: IStarAbc[];
  activeLetter: string | null;
  data: ApiListResult<IStarCard>;
};

export const starAbcPageResolver: ResolveFn<StarAbcResolverData> = (route) => {
  const starAbcService = inject(StarAbcService);
  const starService = inject(StarService);
  const router = inject(Router);

  const { page } = route.queryParams;

  const activeLetter = route.paramMap.get('letter');
  const starAbc$ = starAbcService.getStarAbc();
  const params = {
    display_context_name_initial_filter: activeLetter,
    page_limit: +(page ?? 1) - 1,
  };
  const data$: Observable<ApiListResult<IStarCard>> = starService.search(params);

  return forkJoin({
    starAbc: starAbc$,
    activeLetter: of(activeLetter),
    data: data$,
  }).pipe(
    catchError((err: Error) => {
      router
        .navigate(['/', '404'], {
          skipLocationChange: true,
        })
        .then();
      return throwError(() => err);
    })
  );
};
