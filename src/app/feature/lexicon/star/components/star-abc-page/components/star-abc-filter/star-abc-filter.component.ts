import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { RouterLink } from '@angular/router';
import { NgClass } from '@angular/common';
import { IStarAbc } from '../../../../definitions/star-abc.definitions';
import { toLower } from 'lodash-es';

@Component({
  selector: 'app-star-abc-filter',
  imports: [RouterLink, NgClass],
  templateUrl: './star-abc-filter.component.html',
  styleUrl: './star-abc-filter.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StarAbcFilterComponent {
  @Input() starAbc: IStarAbc[];
  @Input() activeLetter: string;
  protected readonly toLower = toLower;
}
