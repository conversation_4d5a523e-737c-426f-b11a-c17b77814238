<div class="star-abc">
  <div class="star-abc-shortcuts">
    @for (initial of starAbc; track initial.id) {
      <a
        class="star-abc-shortcuts-letter"
        [class.disabled]="!initial?.hasMatchingName"
        [ngClass]="{ active: activeLetter === initial?.id }"
        [routerLink]="initial.hasMatchingName ? ['/lexikon/sztar/abc', toLower(initial.value)] : null"
      >
        {{ initial?.value }}
      </a>
    }
  </div>
</div>
