@use 'shared' as *;

:host {
  display: block;

  .nav {
    margin-bottom: 16px;
  }

  .search-tabs {
    display: flex;
    gap: 24px;
    padding: 3px 0;
    margin-bottom: 16px;

    @include media-breakpoint-down(md) {
      margin-top: 80px;
    }

    &-link {
      display: block;
      padding: 0 8px;
      color: #3d3d3d;
      font-size: 20px;
      font-weight: 700;
      line-height: 29px;
      letter-spacing: 0.6px;
      text-transform: uppercase;

      &.active {
        border-bottom: 3px solid $red-500;
      }
    }
  }

  app-star-abc-filter {
    margin-bottom: 32px;
  }

  .results {
    color: $gray-900;
    font-size: 20px;
    font-weight: 600;
    line-height: 30px;
    margin-bottom: 32px;

    @include media-breakpoint-down(sm) {
      margin-bottom: 8px;
    }
  }

  .active-letter-separator {
    color: #3d3d3d;
    font-size: 20px;
    font-weight: 700;
    line-height: 29px;
    letter-spacing: 0.6px;
    text-transform: uppercase;
    position: relative;
    overflow: hidden;
    margin: 24px 0;

    &:after {
      content: '';
      position: absolute;
      top: 13.5px;
      margin-left: 8px;
      height: 3px;
      width: 100%;
      background: $red-500;
    }
  }

  .star-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;

    @include media-breakpoint-down(sm) {
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
    }

    .advert {
      grid-column: span 4;

      @include media-breakpoint-down(sm) {
        grid-column: span 2;
      }
    }
  }

  .filter-page-end {
    margin-top: 32px;

    @include media-breakpoint-down(sm) {
      margin-top: 20px;
    }
  }

  .pager-page-end {
    margin-top: 32px;

    @include media-breakpoint-down(sm) {
      margin-top: 16px;
    }
  }
}
