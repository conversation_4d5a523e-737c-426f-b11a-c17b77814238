import { Async<PERSON>ip<PERSON>, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { AdvertisementAdoceanComponent, AdvertisementAdoceanStoreService, Limitable } from '@trendency/kesma-ui';
import { map, switchMap } from 'rxjs/operators';
import { PagerComponent } from '../../../../../shared';
import { LexiconMetaService } from '../../../api/lexicon-meta.service';
import { LexiconBreadcrumbComponent } from '../../../components/lexicon-breadcrumb/lexicon-breadcrumb.component';
import { IStarAbc } from '../../definitions/star-abc.definitions';
import { IStarCard, StarCardTypes } from '../../definitions/star.definitions';
import { StarCardComponent } from '../star-card/star-card.component';
import { StarWrapperComponent } from '../star-wrapper/star-wrapper.component';
import { StarAbcFilterComponent } from './components/star-abc-filter/star-abc-filter.component';

@Component({
  selector: 'app-star-abc-page',
  imports: [
    LexiconBreadcrumbComponent,
    StarCardComponent,
    StarWrapperComponent,
    StarAbcFilterComponent,
    PagerComponent,
    AsyncPipe,
    NgIf,
    RouterLink,
    AdvertisementAdoceanComponent,
  ],
  templateUrl: './star-abc-page.component.html',
  styleUrl: './star-abc-page.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StarAbcPageComponent implements OnInit {
  private readonly breadcrumbService = inject(LexiconMetaService);
  private readonly route = inject(ActivatedRoute);
  private readonly destroyRef = inject(DestroyRef);
  private readonly adStore = inject(AdvertisementAdoceanStoreService);

  readonly ads = toSignal(this.adStore.advertisemenets$.pipe(map((ads) => this.adStore.separateAdsByMedium(ads))));

  readonly StarCardTypes = StarCardTypes;

  starAbc: IStarAbc[] = [];
  breadcrumbItems$ = this.route.data.pipe(switchMap((res) => this.breadcrumbService.getStarPageBreadcrumbs(res['data'].activeLetter)));
  activeLetter: string;
  stars: IStarCard[] = [];
  limitable: Limitable;

  ngOnInit(): void {
    this.route.data.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res) => {
      const data = res['data'];
      this.starAbc = data.starAbc;
      this.stars = data.data?.data;
      this.limitable = data.data?.meta?.limitable;
      this.activeLetter = data.activeLetter;
    });
  }
}
