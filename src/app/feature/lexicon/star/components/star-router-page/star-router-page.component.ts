import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { LexiconMetaService } from '../../../api/lexicon-meta.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-star-router-page',
  imports: [RouterOutlet],
  templateUrl: './star-router-page.component.html',
  styleUrl: './star-router-page.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StarRouterPageComponent implements OnInit {
  destroyRef = inject(DestroyRef);
  metaService = inject(LexiconMetaService);

  pageTitle = '';

  ngOnInit(): void {
    this.getPageTitle();
  }

  getPageTitle(): void {
    this.metaService
      .getStarPageTitle()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((title: string) => (this.pageTitle = title));
  }
}
