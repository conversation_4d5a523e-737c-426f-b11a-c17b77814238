@use 'shared' as *;

:host {
  display: block;
  width: 100%;

  .tag {
    color: $blue-dark;
    font-size: 14px;
    font-weight: 500;
    line-height: 15px;
    padding: 5px;
    border-radius: 6px;
    background: $grey-1;
  }

  .star {
    &-name {
      color: $gray-900;
      font-size: 36px;
      font-weight: 700;
      line-height: 37px;
      letter-spacing: 1.08px;
      text-transform: uppercase;
      padding: 8px 0;

      @include media-breakpoint-down(md) {
        margin-top: 80px;
      }
    }

    &-details {
      display: flex;
      gap: 30px;
      margin: 30px 0 46px;

      @include media-breakpoint-down(sm) {
        flex-direction: column;
      }

      .left {
        flex: 1;
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        @include media-breakpoint-down(sm) {
          gap: 16px;
        }

        &:after {
          content: '';
          position: absolute;
          top: 0;
          right: 0;
          width: 6px;
          height: 100%;
          background: $red-500;

          @include media-breakpoint-down(sm) {
            content: none;
          }
        }

        .profile-img-wrapper {
          position: relative;
          cursor: pointer;

          img {
            width: 100%;
            aspect-ratio: 4/5;
            object-fit: cover;
            clip-path: polygon(0 0, 100% 0, 100% 100%, 0 calc(100% - 26px), 0 50%);
          }
        }

        .gallery {
          display: flex;
          gap: 1px;

          & > * {
            flex-basis: 25%;
          }

          &-img {
            aspect-ratio: 1;
            object-fit: cover;
            height: 100%;
            width: 100%;
          }

          &-wrapper {
            cursor: pointer;
          }

          &-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 5px;
            background: $red-500;

            span {
              color: $white;
              text-align: center;
              font-size: 12px;
              font-weight: 700;
              line-height: normal;
            }
          }
        }
      }

      .right {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 30px;

        &-block {
          & > .star-detail-label {
            line-height: normal;
            margin-bottom: 8px;
          }

          &.social {
            display: flex;
            gap: 16px;

            img {
              flex: 0 0 auto;
            }
          }

          .tags {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
          }
        }
      }
    }

    &-detail {
      display: flex;
      padding: 10px 0;
      gap: 8px;
      align-items: center;
      border-bottom: 1px solid $grey-1;

      &-label {
        color: $gray-900;
        font-size: 14px;
        font-weight: 600;
        line-height: 15px;
        min-width: 160px;
      }

      &-value {
        color: $gray-900;
        font-size: 14px;
        font-weight: 400;
        line-height: 15px;
      }
    }
  }

  .wysiwyg-content {
    app-star-link {
      margin: 8px 0;
    }
  }

  .recommendations {
    margin-bottom: 16px;

    &-wrapper {
      margin-bottom: 40px;

      app-star-link {
        margin-bottom: 24px;
      }
    }
  }

  .articles {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;

    .advert {
      grid-column: span 2;

      @include media-breakpoint-down(xs) {
        grid-column: span 1;
      }
    }

    @include media-breakpoint-down(xs) {
      grid-template-columns: 1fr;
    }

    &-wrapper {
      margin-bottom: 32px;

      app-star-link {
        padding: 8px;
        margin-bottom: 16px;
      }
    }
  }

  .article {
    display: flex;
    gap: 16px;
    &-thumbnail {
      width: 100px;
      height: 100px;
      object-fit: cover;
      &-box {
        flex-shrink: 0;
      }
    }
    &-data {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    &-badge {
      background-color: var(--kui-red-500);
      color: var(--kui-white);
      font-size: 12px;
      font-weight: 700;
      line-height: 14px;
      padding: 5px;
      border-radius: 6px;
      text-transform: uppercase;
      &-box {
        display: flex;
        gap: 8px;
      }
    }
    &-title {
      color: var(--kui-gray-900);
      font-size: 18px;
      font-weight: 600;
      line-height: 21px;
      &:hover {
        color: var(--kui-yellow-500);
      }
    }
  }
}
