<app-star-wrapper [leftContent]="leftContent" [isStarProfilePage]="true"></app-star-wrapper>
<app-star-lightbox
  (closeEvent)="onCloseGallery()"
  *ngIf="isLightboxVisible"
  [data]="lightboxImages()"
  [selectedImgIndex]="selectedImgIndex"
></app-star-lightbox>

<ng-template #leftContent>
  <app-lexicon-breadcrumb [items]="breadcrumbItems()"></app-lexicon-breadcrumb>
  <h1 class="star-name">
    {{ starProfile?.name }}
    @if (starProfile?.artistName) {
      - {{ starProfile.artistName }}
    }
  </h1>

  <div class="star-details">
    <div class="left">
      <div (click)="onOpenGallery()" class="profile-img-wrapper">
        <img [alt]="starProfile?.artistName" [src]="starProfile?.profileImage?.focusedImages?.['4:5'] ?? starProfile?.profileImage?.detail" />
      </div>

      <div *ngIf="lightboxImages()?.length > 1" class="gallery">
        @for (image of lightboxImages() | slice: 1 : 4; track image; let i = $index) {
          <div class="gallery-wrapper" (click)="onOpenGallery(i + 1)">
            <img [src]="image?.thumbnailUrlFocusedImages?.['1:1'] ?? image?.url?.thumbnail" loading="lazy" class="gallery-img" alt="" />
          </div>
        }

        <div (click)="onOpenGallery()" class="gallery-placeholder gallery-wrapper">
          <img alt="" src="assets/images/icons/photo.svg" />
          <span>Galéria megnyitása</span>
        </div>
      </div>
    </div>

    <div class="right">
      <div class="right-block">
        <ng-container [ngTemplateOutletContext]="{ label: 'Születési név', value: starProfile?.birthName }" [ngTemplateOutlet]="starDetail"></ng-container>
        <ng-container [ngTemplateOutletContext]="{ label: 'Művésznév/becenév', value: starProfile?.artistName }" [ngTemplateOutlet]="starDetail"></ng-container>
        <ng-container
          [ngTemplateOutletContext]="{ label: 'Születési idő', value: getDateOfBirth($any(starProfile?.dateOfBirth)) }"
          [ngTemplateOutlet]="starDetail"
        ></ng-container>
        <ng-container [ngTemplateOutletContext]="{ label: 'Születési hely', value: starProfile?.birthplace }" [ngTemplateOutlet]="starDetail"></ng-container>
        <div class="star-detail">
          <div class="star-detail-label">Horoszkóp</div>
          <a
            [routerLink]="['/', 'lexikon', 'sztar', 'kereso']"
            [queryParams]="{ 'horoscopes_filter[]': starProfile?.horoscope?.key }"
            class="star-detail-value tag"
            >{{ starProfile?.horoscope?.title | titlecase }}</a
          >
        </div>
        <ng-container
          [ngTemplateOutletContext]="{ label: 'Magasság', value: starProfile?.height ? starProfile?.height + ' cm' : '' }"
          [ngTemplateOutlet]="starDetail"
        ></ng-container>
        <ng-container
          [ngTemplateOutletContext]="{ label: 'Eredeti hajszín', value: starProfile?.originalHairColor }"
          [ngTemplateOutlet]="starDetail"
        ></ng-container>
        <ng-container
          [ngTemplateOutletContext]="{ label: 'Családi állapot', value: starProfile?.maritalStatus }"
          [ngTemplateOutlet]="starDetail"
        ></ng-container>
        <ng-container [ngTemplateOutletContext]="{ label: 'Párja', value: starProfile?.partner }" [ngTemplateOutlet]="starDetail"></ng-container>
        <ng-container [ngTemplateOutletContext]="{ label: 'Gyermekei', value: starProfile?.children }" [ngTemplateOutlet]="starDetail"></ng-container>
        <ng-container [ngTemplateOutletContext]="{ label: 'Háziállat', value: starProfile?.pet }" [ngTemplateOutlet]="starDetail"></ng-container>
        <ng-container
          *ngIf="starProfile?.dateOfDeath"
          [ngTemplateOutletContext]="{ label: 'Halál időpontja', value: getDateOfDeath($any(starProfile.dateOfDeath)) }"
          [ngTemplateOutlet]="starDetail"
        ></ng-container>
      </div>

      <div class="right-block">
        <div class="star-detail-label">Foglalkozás</div>
        <div class="tags">
          @for (tag of starProfile?.occupations; track tag.slug) {
            <a [routerLink]="['/', 'lexikon', 'sztar', 'kereso']" [queryParams]="{ 'occupations_filter[]': tag.slug }" class="tag">{{ tag.title }}</a>
          }
        </div>
      </div>

      @if (starProfile?.awards?.length) {
        <div class="right-block">
          <div class="star-detail-label">Díjak, elismerések</div>
          <div class="tags">
            @for (award of starProfile?.awards; track award.slug) {
              <a [routerLink]="['/', 'lexikon', 'sztar', 'kereso']" [queryParams]="{ 'awards_filter[]': award.slug }" class="tag">{{ award.title }}</a>
            }
          </div>
        </div>
      }

      <div class="right-block social">
        <a *ngIf="starProfile?.socialMediaContactDetails?.instagramUrl" [href]="starProfile.socialMediaContactDetails.instagramUrl" target="_blank">
          <img alt="Instagram" src="assets/images/icons/instagram.svg" />
        </a>
        <a *ngIf="starProfile?.socialMediaContactDetails?.facebookUrl" [href]="starProfile.socialMediaContactDetails.facebookUrl" target="_blank">
          <img alt="Facebook" src="assets/images/icons/facebook.svg" />
        </a>
        <a *ngIf="starProfile?.socialMediaContactDetails?.tiktokUrl" [href]="starProfile.socialMediaContactDetails.tiktokUrl" target="_blank">
          <img alt="TikTok" src="assets/images/icons/tiktok.svg" />
        </a>
        <a *ngIf="starProfile?.socialMediaContactDetails?.twitterUrl" [href]="starProfile.socialMediaContactDetails.twitterUrl" target="_blank">
          <img alt="X" src="assets/images/icons/icon-social-x.svg" />
        </a>
      </div>
    </div>
  </div>

  <!-- ROADBLOCK 1, MEDIUM RECTANGLE 1 -->
  <kesma-advertisement-adocean [hasNoParentHeight]="true" *ngIf="adverts()?.desktop?.roadblock_1 as ad" [ad]="ad"></kesma-advertisement-adocean>
  <kesma-advertisement-adocean [hasNoParentHeight]="true" *ngIf="adverts()?.mobile?.mobilrectangle_1 as ad" [ad]="ad"></kesma-advertisement-adocean>
  <!-- ROADBLOCK 1, MEDIUM RECTANGLE 1 -->

  <div class="wysiwyg-content">
    <app-star-link [data]="{ text: 'Életrajz' }" [styleId]="StarLinkTypes.LINE_RIGHT"></app-star-link>
    <ng-container *ngFor="let element of starProfile?.biographyContent">
      <ng-container *ngFor="let detail of element?.details">
        <app-wysiwyg-box [html]="detail?.value"></app-wysiwyg-box>
      </ng-container>
    </ng-container>
  </div>

  <!-- ROADBLOCK 2, , MEDIUM RECTANGLE 2 -->
  <kesma-advertisement-adocean [hasNoParentHeight]="true" *ngIf="adverts()?.desktop?.roadblock_2 as ad" [ad]="ad"></kesma-advertisement-adocean>
  <kesma-advertisement-adocean [hasNoParentHeight]="true" *ngIf="adverts()?.mobile?.mobilrectangle_2 as ad" [ad]="ad"> </kesma-advertisement-adocean>
  <!-- ROADBLOCK 2, , MEDIUM RECTANGLE 2 -->

  @if (recommendationStars()?.length) {
    <div class="recommendations-wrapper">
      <app-star-link [data]="{ text: 'Ők is érdekelhetnek', url: '/lexikon/sztar/kapcsolodo-sztarok' }" [styleId]="StarLinkTypes.LINE_RIGHT"></app-star-link>

      <div class="recommendations">
        <app-star-box [data]="recommendationStars()"></app-star-box>
      </div>

      <app-star-link [data]="{ text: 'További sztárok', url: relatedStarsUrl }"></app-star-link>

      <!-- ROADBLOCK 3, , MEDIUM RECTANGLE 3 -->
      <kesma-advertisement-adocean [hasNoParentHeight]="true" *ngIf="adverts()?.desktop?.roadblock_3 as ad" [ad]="ad"></kesma-advertisement-adocean>
      <kesma-advertisement-adocean [hasNoParentHeight]="true" *ngIf="adverts()?.mobile?.mobilrectangle_3 as ad" [ad]="ad"> </kesma-advertisement-adocean>
      <!-- ROADBLOCK 3, , MEDIUM RECTANGLE 3 -->
    </div>
  }

  @if (articles()?.length) {
    <div class="articles-wrapper">
      <app-star-link [data]="{ text: 'Cikkek' }" [styleId]="StarLinkTypes.LINE_RIGHT"></app-star-link>

      <div class="articles">
        @for (article of articles(); track article.id) {
          <article class="article">
            <div class="article-thumbnail-box">
              <img
                class="article-thumbnail"
                withFocusPoint
                [displayedAspectRatio]="{ desktop: '1:1' }"
                [data]="article?.thumbnailFocusedImages"
                [displayedUrl]="article?.thumbnail?.url || PlaceholderImg"
                [alt]="article?.thumbnail?.alt || article.title"
              />
            </div>
            <div class="article-data">
              <div class="article-badge-box">
                @if (!article?.isAdultsOnly) {
                  <div class="article-badge adult">18+</div>
                }
                <a class="article-badge column" [routerLink]="['/rovat', article.columnSlug]">{{ article.columnTitle }}</a>
              </div>
              <a [routerLink]="buildArticleUrl(article)">
                <h2 class="article-title">{{ article.title }}</h2>
              </a>
            </div>
          </article>

          @if ($index === 3) {
            <div class="advert">
              <kesma-advertisement-adocean *ngIf="adverts()?.desktop?.roadblock_ottboxextra as ad" [ad]="ad"></kesma-advertisement-adocean>
              <kesma-advertisement-adocean *ngIf="adverts()?.mobile?.mobilrectangle_ottboxextra as ad" [ad]="ad"></kesma-advertisement-adocean>
            </div>
          }
        }
      </div>
    </div>
  }
</ng-template>

<ng-template #starDetail let-label="label" let-value="value">
  <div class="star-detail">
    <div class="star-detail-label">{{ label }}</div>
    <div class="star-detail-value">{{ value || 'Nincs adat' }}</div>
  </div>
</ng-template>
