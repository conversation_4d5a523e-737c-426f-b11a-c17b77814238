import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>Outlet, <PERSON>lice<PERSON>ipe, TitleCasePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, DestroyRef, effect, inject, OnDestroy, signal } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { ActivatedRoute, RouterLink } from '@angular/router';
import {
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  ArticleCard,
  buildArticleUrl,
  FocusPointDirective,
  GalleryImage,
  searchResultToArticleCard,
} from '@trendency/kesma-ui';
import { differenceInYears, format } from 'date-fns';
import { hu } from 'date-fns/locale';
import { Subject } from 'rxjs';
import { map, share, switchMap, takeUntil } from 'rxjs/operators';
import { ApiService, PlaceholderImg, WysiwygBoxComponent } from 'src/app/shared';
import { LexiconMetaService } from '../../../api/lexicon-meta.service';
import { LexiconBreadcrumbComponent } from '../../../components/lexicon-breadcrumb/lexicon-breadcrumb.component';
import { IStarCard, IStarProfile, StarLinkTypes } from '../../definitions/star.definitions';
import { StarService } from '../../services/star.service';
import { getRandomCelebs } from '../../utils/star.utils';
import { StarBoxComponent } from '../star-box/star-box.component';
import { StarLightboxComponent } from '../star-lightbox/star-lightbox.component';
import { StarLinkComponent } from '../star-link/star-link.component';
import { StarWrapperComponent } from '../star-wrapper/star-wrapper.component';

@Component({
  selector: 'app-star-profile-page',
  imports: [
    LexiconBreadcrumbComponent,
    StarBoxComponent,
    StarLinkComponent,
    StarWrapperComponent,
    NgTemplateOutlet,
    NgIf,
    RouterLink,
    TitleCasePipe,
    SlicePipe,
    NgForOf,
    StarLightboxComponent,
    AdvertisementAdoceanComponent,
    WysiwygBoxComponent,
    FocusPointDirective,
  ],

  templateUrl: './star-profile-page.component.html',
  styleUrl: './star-profile-page.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StarProfilePageComponent implements OnDestroy {
  articles = signal<ArticleCard[]>([]);
  lightboxImages = signal<Partial<GalleryImage>[]>([]);
  recommendationStars = signal<IStarCard[]>([]);
  starProfile: IStarProfile;
  isLightboxVisible = false;
  selectedImgIndex = 0;
  protected readonly StarLinkTypes = StarLinkTypes;
  protected readonly PlaceholderImg = PlaceholderImg;
  protected readonly buildArticleUrl = buildArticleUrl;
  private readonly route = inject(ActivatedRoute);
  private readonly breadcrumbService = inject(LexiconMetaService);
  private readonly apiService = inject(ApiService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly starService = inject(StarService);
  private readonly adStoreAdo = inject(AdvertisementAdoceanStoreService);
  private readonly destroy$: Subject<boolean> = new Subject<boolean>();
  readonly adverts = toSignal(this.adStoreAdo.advertisemenets$.pipe(map((adsCollection) => this.adStoreAdo.separateAdsByMedium(adsCollection))));
  profileData = toSignal(
    this.route.data.pipe(
      map(({ profile }) => profile),
      share(),
      takeUntil(this.destroy$)
    )
  );
  breadcrumbItems = toSignal(
    this.route.data.pipe(
      switchMap(({ profile }) => {
        return this.breadcrumbService.getStarPageBreadcrumbs(profile.displayContextNameInitial);
      }),
      takeUntil(this.destroy$)
    )
  );
  relatedStarsUrl = ['/', 'lexikon', 'sztar', 'kapcsolodo-sztarok'];

  constructor() {
    effect(
      () => {
        this.starProfile = this.profileData();
        this.starService.chosenDailyStar = {
          name: this.starProfile?.name,
          slug: this.starProfile?.slug,
        };
        const starDisplayedName = this.starProfile?.[this.starProfile.displayContextName];
        if (starDisplayedName) {
          this.getStarArticles(starDisplayedName);
        }
        this.getGallery(this.starProfile.gallery.slug);
        this.getRecommendations();
      },
      { allowSignalWrites: true }
    );
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  getStarArticles(starName: string): void {
    this.apiService
      .searchByKeyword(starName, 0, 6)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((res) => this.articles.set(res.data?.map(searchResultToArticleCard) || []));
  }

  getGallery(slug: string): void {
    this.apiService
      .getGalleryDetails(slug)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((res) => {
        const profileImage = this.starProfile.profileImage;
        const profileGalleryImage: Partial<GalleryImage> = {
          url: { fullSize: profileImage.detail, thumbnail: profileImage.thumbnail },
          caption: profileImage?.caption,
        };
        const galleryImages = res.images;

        if (profileGalleryImage) {
          this.lightboxImages.set([profileGalleryImage]);
        }

        if (galleryImages?.length) {
          this.lightboxImages.update((values) => [...values, ...galleryImages]);
        }
      });
  }

  getDateOfBirth(date: Date): string {
    return date ? `${format(date, 'yyyy. LLLL. dd.', { locale: hu })} (${differenceInYears(new Date(), date)} éves)` : '';
  }

  getDateOfDeath(date: Date): string {
    return date ? `${format(date, 'yyyy. LL. dd.', { locale: hu })}` : '';
  }

  onOpenGallery(imgIndex = 0): void {
    if (!this.lightboxImages().length) return;

    this.selectedImgIndex = imgIndex;
    this.isLightboxVisible = true;
  }

  onCloseGallery(): void {
    this.isLightboxVisible = false;
  }

  getRecommendations(): void {
    this.starService
      .search({ related_star_slug_filter: this.starProfile.slug })
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(({ data }) => {
        const randomSelection = getRandomCelebs(data).map((item) => ({
          ...item,
          star_profile_image: item?.profileImage,
        }));
        this.recommendationStars.set(randomSelection);
      });
  }
}
