import { ResolveFn } from '@angular/router';
import { IStarProfile } from '../../../definitions/star.definitions';
import { StarProfileService } from './star-profile.service';
import { inject } from '@angular/core';
import { map } from 'rxjs/operators';

export const starProfileResolver: ResolveFn<IStarProfile> = (route) => {
  const profileService = inject(StarProfileService);
  const starSlug = route.params['starSlug'];

  return profileService.getStarDetails(starSlug).pipe(map((res) => res.data));
};
