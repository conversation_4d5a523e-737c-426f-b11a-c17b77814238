import { inject, Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { Observable } from 'rxjs';
import { ApiResponseMetaList, ApiResult } from '@trendency/kesma-ui';
import { IStarProfile } from '../../../definitions/star.definitions';

@Injectable({
  providedIn: 'root',
})
export class StarProfileService {
  private readonly reqService = inject(ReqService);

  getStarDetails(slug: string): Observable<ApiResult<IStarProfile, ApiResponseMetaList>> {
    return this.reqService.get<ApiResult<IStarProfile, ApiResponseMetaList>>(`star-dictionary/star/details/${slug}`);
  }
}
