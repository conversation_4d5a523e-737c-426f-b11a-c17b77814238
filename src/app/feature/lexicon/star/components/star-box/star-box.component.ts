import { ChangeDetectionStrategy, Component } from '@angular/core';
import { BaseComponent } from '@trendency/kesma-ui';
import { StarCardComponent } from '../star-card/star-card.component';
import { IStarCard, StarCardTypes } from '../../definitions/star.definitions';

@Component({
  selector: 'app-star-box',
  imports: [StarCardComponent],
  templateUrl: './star-box.component.html',
  styleUrl: './star-box.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StarBoxComponent extends BaseComponent<IStarCard[]> {
  protected readonly StarCardTypes = StarCardTypes;
}
