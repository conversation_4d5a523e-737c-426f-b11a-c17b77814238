@use 'shared' as *;

:host {
  display: block;
  width: 100%;

  .star-card-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 0 0 auto;

    &-wrapper {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    &-divider {
      width: 100%;
      height: 3px;
    }

    span {
      font-size: 16px;
      font-weight: 700;
      line-height: normal;
      text-transform: uppercase;
    }

    kesma-icon {
      width: 30px;
      height: 30px;
      color: $red-500;
      flex: 0 0 auto;
    }
  }

  &.style-line-right {
    .star-card-btn {
      &-wrapper {
        gap: 8px;
        flex-direction: row-reverse;
        pointer-events: none;

        &.clickable {
          pointer-events: unset;
        }
      }

      span {
        width: max-content;
        font-size: 22px;
        line-height: 29px;
        letter-spacing: 0.66px;
      }

      kesma-icon {
        display: none;
      }
    }
  }

  &.style-star-main-page:not(.style-line-left) {
    @include media-breakpoint-down(xs) {
      .star-card-btn {
        max-width: 50%;
        span {
          font-size: 20px;
        }
      }
    }
  }
}
