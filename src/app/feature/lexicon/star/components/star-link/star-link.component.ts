import { ChangeDetectionStrategy, Component, HostBinding, inject, Input, OnInit } from '@angular/core';
import { BaseComponent, IconComponent } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { IStarLink, StarLinkTypes } from '../../definitions/star.definitions';
import { UtilService } from '@trendency/kesma-core';

@Component({
  selector: 'app-star-link',
  imports: [RouterLink, IconComponent],
  templateUrl: './star-link.component.html',
  styleUrl: './star-link.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StarLinkComponent extends BaseComponent<IStarLink> implements OnInit {
  private readonly utilService = inject(UtilService);

  @HostBinding('class') hostClass: string;
  @Input() isStarMainPage = false;
  @Input() set styleId(styleId: StarLinkTypes) {
    this._styleId = styleId;
    this.hostClass = `style-${styleId}`;
  }
  get styleId(): StarLinkTypes {
    return this._styleId;
  }
  @Input() lineBgColor = '#e2003b';
  @Input() textColor = '#000';
  @Input() textColorOnMobile?: string;

  readonly isMobile: boolean = this.utilService.isBrowser() ? window.innerWidth <= 991 : false;

  readonly StarLinkTypes = StarLinkTypes;

  _styleId: StarLinkTypes;

  override ngOnInit(): void {
    super.ngOnInit();
    this.hostClass = this.isStarMainPage ? `${this.hostClass} style-star-main-page` : this.hostClass;
    if (!this.textColorOnMobile) {
      this.textColorOnMobile = this.textColor;
    }
  }
}
