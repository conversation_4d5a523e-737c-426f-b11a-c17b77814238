import { ChangeDetectionStrategy, Component, Input, OnDestroy, OnInit } from '@angular/core';
import { BehaviorSubject, combineLatest, combineLatestWith, distinctUntilChanged, Observable, of, Subject, take } from 'rxjs';
import { IHttpOptions } from '@trendency/kesma-core';
import { ApiListResult, ApiResult } from '@trendency/kesma-ui';
import { catchError, debounceTime, map, startWith, takeUntil } from 'rxjs/operators';
import { ControlValueAccessor, FormsModule, NG_VALUE_ACCESSOR } from '@angular/forms';
import { SelectPageableHttpLoader } from '../../../../utils/select-pageable-http-loader';
import { NgSelectModule } from '@ng-select/ng-select';
import { AsyncPipe } from '@angular/common';

@Component({
  selector: 'app-star-search-filter-api-select',
  templateUrl: './star-search-filter-api-select.component.html',
  styleUrls: ['./star-search-filter-api-select.component.scss'],
  imports: [NgSelectModule, AsyncPipe, FormsModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: StarSearchFilterApiSelectComponent,
      multi: true,
    },
  ],
})
export class StarSearchFilterApiSelectComponent<T extends Record<string, unknown>> implements OnInit, OnDestroy, ControlValueAccessor {
  destroy$ = new Subject<void>();
  typeahead$: Subject<string> = new Subject<string>();
  @Input() bindLabel = 'title';
  @Input() bindValue = 'slug';
  @Input() placeholder = '';
  @Input() searchable = true;
  @Input() sourceRequest: (options: IHttpOptions) => Observable<ApiListResult<T>>;
  @Input() singleItemRequest?: (slug: string) => Observable<ApiResult<T[]>>;
  @Input() isBackendSearch = true;
  @Input() set selectedItem(identifier: string) {
    this.selectedItem$.next(identifier?.length > 0 ? identifier : null);
  }

  onTouched?: () => void;
  onChange?: (value: string) => void;

  selectedItem$ = new BehaviorSubject<string | null>(null);
  items$ = new BehaviorSubject<T[]>([]);
  loader?: SelectPageableHttpLoader<T>;

  singleRequestedItem$ = new Subject<T | null>();
  isSingleRequestInProgress = new BehaviorSubject<boolean>(false);

  isLoading$: Observable<boolean>;

  writeValue(value: string): void {
    this.selectedItem$.next(value?.length > 0 ? value : null);
  }
  registerOnChange(fn: any): void {
    this.onChange = fn;
  }
  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  ngOnInit(): void {
    if (!this.sourceRequest) {
      console.error('StarSearchFilterApiSelectComponent > Source request is not provided. Please provide one, otherwise this component will be unusable.');
      return;
    }
    // Handle search
    this.typeahead$.pipe(debounceTime(300), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe((term: string | null) => {
      this.loader?.search(term || '');
    });

    this.loader = new SelectPageableHttpLoader<T>(this.sourceRequest);
    this.isLoading$ = combineLatest([this.isSingleRequestInProgress, this.loader.isLoading$.pipe(startWith(false))]).pipe(
      map((states) => states.some((s: boolean) => s)),
      startWith(true)
    );

    combineLatest([this.singleRequestedItem$, this.loader.data$])
      .pipe(
        map(([singleRequestedItem, items]) => {
          return singleRequestedItem && items.length > 0 && !this.loader?.searchTerm()
            ? [singleRequestedItem, ...items.filter((item) => item[this.bindValue] !== singleRequestedItem[this.bindValue])]
            : items;
        }),
        map((sourceResponse) =>
          (sourceResponse as any).map((item: any) => {
            if (!item[this.bindLabel] || item[this.bindLabel].length === 0) {
              item[this.bindLabel] = 'Nincs cím';
            }
            return item;
          })
        )
      )
      .subscribe((data) => {
        this.items$.next(data);
      });
    this.loader.init();
    this.initSourceAndCheckInitialValue();
  }

  loadMore(): void {
    this.loader?.loadMore();
  }

  private initSourceAndCheckInitialValue(): void {
    /*
      We need to check if there is an initial value.
      In that case we need to get information about what is being associated to a given
      value identifier, as we need additional information in order to display it in the select.
      For example an id value is not enough, we need the title too, so we have to get it from the backend.
    */
    if (this.selectedItem$.value) {
      const isValueInSource = !(this.items$.value.filter((item) => item[this.bindValue] === this.selectedItem$.value).length < 1);
      if (this.loader && !isValueInSource) {
        if (this.singleItemRequest) {
          const id = this.selectedItem$.value;
          this.isSingleRequestInProgress.next(true);
          this.singleItemRequest(id)
            .pipe(
              catchError(() => of(undefined)),
              combineLatestWith(this.loader.data$),
              take(1)
            )
            .subscribe({
              error: () => this.isSingleRequestInProgress.next(false),
              complete: () => this.isSingleRequestInProgress.next(false),
              next: ([singleResponse]) => {
                const item = singleResponse?.data?.[0];
                if (!item) {
                  return;
                }
                this.singleRequestedItem$.next(item);
                this.selectedItem$.next((item[this.bindValue] as string) ?? '');
              },
            });
          return;
        }
      }
    }
    this.singleRequestedItem$.next(null);
  }

  handleClick(): void {
    this.onTouched && this.onTouched();
  }

  selectionChanged(value: string): void {
    this.handleClick();
    this.selectedItem$.next(value);
    this.onChange && this.onChange(value);
  }

  ngOnDestroy(): void {
    this.loader?.close();
    this.destroy$.next();
    this.destroy$.complete();
  }
}
