import { ChangeDetectionStrategy, ChangeDetectorRef, Component, DestroyRef, HostBinding, inject, Input, OnInit, signal } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NgSelectModule } from '@ng-select/ng-select';
import { StarSearchFilterApiSelectComponent } from '../star-search-filter-api-select/star-search-filter-api-select.component';
import { FormsModule } from '@angular/forms';
import { IHttpOptions } from '@trendency/kesma-core';
import { StarService } from '../../../../services/star.service';
import { Observable, of } from 'rxjs';
import { ApiResult } from '@trendency/kesma-ui';
import { Birthplaces, IStarHoroscope, SearchSelectItem, StarSearchFilterParams } from '../../../../definitions/star.definitions';
import { ApiResponseMeta } from '@trendency/kesma-ui/lib/definitions/api-result';
import { OnlyNumberDirective } from '../../../../../../../shared';

@Component({
  selector: 'app-star-search-filter',
  templateUrl: 'star-search-filter.component.html',
  styleUrls: ['star-search-filter.component.scss'],
  imports: [NgSelectModule, StarSearchFilterApiSelectComponent, FormsModule, OnlyNumberDirective],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StarSearchFilterComponent implements OnInit {
  @Input() @HostBinding('class.sidebar') isSidebar = false;
  searchFilter: StarSearchFilterParams = {
    name_filter: '',
    from_age_filter: '',
    to_age_filter: '',
    'birthplaces_filter[]': '',
    'horoscopes_filter[]': '',
    'occupations_filter[]': '',
    'awards_filter[]': '',
    is_hungarian_filter: '',
  };
  selectedOrigin = '';
  selectedBirthplace = '';
  selectedHoroscope = '';
  selectedOccupation = '';
  selectedAward = '';
  private readonly destroyRef = inject(DestroyRef);
  private readonly router = inject(Router);
  private readonly route = inject(ActivatedRoute);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly starService = inject(StarService);

  isCollapsed = signal<boolean>(false);

  originsSourceRequest = (): Observable<ApiResult<object[]>> => {
    return of({
      data: [
        { title: 'Hazai sztárok', value: '1' },
        { title: 'Külföldi sztárok', value: '0' },
      ],
      meta: {} as ApiResponseMeta,
    });
  };

  birthplacesSourceRequest = (options: IHttpOptions): Observable<ApiResult<Birthplaces[]>> => {
    return this.starService.birthplaces(options);
  };

  birthplaceSingleItemRequest = (birthplace: string): Observable<ApiResult<Birthplaces[]>> => {
    return this.starService.birthplaces({
      params: {
        global_filter: birthplace,
      },
    });
  };

  horoscopesSourceRequest = (options: IHttpOptions): Observable<ApiResult<IStarHoroscope[]>> => {
    return this.starService.horoscopes(options);
  };

  occupationsSourceRequest = (options: IHttpOptions): Observable<ApiResult<SearchSelectItem[]>> => {
    return this.starService.occupations(options);
  };

  occupationSingleItemRequest = (slug: string): Observable<ApiResult<SearchSelectItem[]>> => {
    return this.starService.occupations({
      params: {
        'slugs_filter[]': slug,
      },
    });
  };

  awardsSourceRequest = (options: IHttpOptions): Observable<ApiResult<SearchSelectItem[]>> => {
    return this.starService.awards(options);
  };

  awardSingleItemRequest = (slug: string): Observable<ApiResult<SearchSelectItem[]>> => {
    return this.starService.awards({
      params: {
        'slugs_filter[]': slug,
      },
    });
  };

  ngOnInit(): void {
    this.getRouteParams();
  }

  onSearch(): void {
    this.setRouteParams();
  }

  onClearSearch(): void {
    this.searchFilter['name_filter'] = '';
  }

  getRouteParams(): void {
    this.route.data.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((data) => {
      const params = {
        ...this.route.snapshot.queryParams,
        ...((data['searchParams'] ?? {}) as StarSearchFilterParams),
      };
      if (data['searchParams']?.is_hungarian_filter === '1') {
        // hungarian_filter=1 -> 'Hazai sztárok'
        this.selectedOrigin = '1';
      } else if (data['searchParams']?.is_hungarian_filter === '0') {
        //hungarian_filter=0 -> 'Külföldi sztárok'
        this.selectedOrigin = '0';
      } else if ('is_hungarian_filter' in params) {
        this.selectedOrigin = params['is_hungarian_filter'] as string;
      }
      if ('birthplaces_filter[]' in params) {
        this.selectedBirthplace = params['birthplaces_filter[]'] as string;
      }
      if ('awards_filter[]' in params) {
        this.selectedAward = params['awards_filter[]'] as string;
      }
      if ('horoscopes_filter[]' in params) {
        this.selectedHoroscope = params['horoscopes_filter[]'] as string;
      }
      if ('occupations_filter[]' in params) {
        this.selectedOccupation = params['occupations_filter[]'] as string;
      }
      //hungarian_filter=undefined -> not filtered
      this.searchFilter = { ...this.searchFilter, ...params };
      this.cdr.markForCheck();
    });
  }

  setRouteParams(): void {
    const queryParams = this.searchFilter;

    // Remove empty props
    Object.entries(queryParams).forEach(([key, value]) => {
      if (!value || key === 'page') {
        delete queryParams[key as keyof StarSearchFilterParams];
      }
    });

    this.router
      .navigate(['/lexikon/sztar/kereso'], {
        queryParams,
      })
      .then();
  }
}
