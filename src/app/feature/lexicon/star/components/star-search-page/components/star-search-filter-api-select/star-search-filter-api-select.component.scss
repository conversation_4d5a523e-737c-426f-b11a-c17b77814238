@use 'shared' as *;

:host {
  ::ng-deep {
    .ng-input {
      top: 50% !important;
      transform: translateY(-50%);
      padding-left: 15px !important;
      input {
        width: calc(100% - 60px) !important;
      }
    }

    .ng-option {
      &-marked {
        background: transparent !important;
      }
    }

    .ng-value-label,
    .ng-placeholder {
      color: $gray-900 !important;
      text-align: center;
      font-size: 14px;
      font-weight: 600;
      line-height: 22px;
      font-family: var(--kui-font-primary);
    }

    ng-dropdown-panel {
      border: 1px solid $grey-100;
      border-top: none;
      z-index: 999;
    }
  }
}
