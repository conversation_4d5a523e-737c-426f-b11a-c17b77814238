@use 'shared' as *;

:host {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;

  &.sidebar {
    gap: 32px;

    .search-filters {
      display: grid;
      grid-template-columns: repeat(2, minmax(calc(50% - 16px), 1fr));
      gap: 16px;

      > .search-filters-item {
        grid-column: unset;

        &.awards {
          grid-column: 1/-1;
        }

        &.input {
          max-width: none;

          &:nth-child(2) {
            margin-left: 0;
          }
        }
      }
    }

    button {
      min-width: 56px;

      span {
        display: none;
      }
    }
  }

  .search {
    &-by-keyword {
      display: flex;

      &-input {
        width: 100%;
        padding: 10px 16px;
        border-radius: 24px 0 0 24px;
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        background: $grey-1;
        color: #3d3d3d;

        &::placeholder {
          color: #6d6d6d;
          font-weight: 400;
        }

        &-clear {
          position: absolute;
          right: 17px;
          top: 50%;
          transform: translateY(-50%);
          cursor: pointer;
          height: 20px;
        }

        &-wrapper {
          display: flex;
          flex-basis: 100%;
          position: relative;
        }
      }
    }

    &-filters {
      display: grid;
      grid-template-columns: repeat(6, 1fr);
      gap: 8px;
      flex-wrap: wrap;

      @include media-breakpoint-down(sm) {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
      }

      @include media-breakpoint-down(md) {
        &.hidden {
          display: none;
        }
      }

      &-item {
        &.select {
          min-width: 0;
          grid-column: span 2;

          @include media-breakpoint-down(sm) {
            &.awards {
              grid-column: 1/-1;
            }

            grid-column: unset;
          }
        }

        &.input {
          background: $grey-1;
          border-radius: 24px;
          padding: 10px 16px;
          width: 100%;
          display: flex;
          align-items: center;
          gap: 8px;

          input {
            color: #6d6d6d;
            font-size: 14px;
            font-weight: 400;
            line-height: 22px;
            background: transparent;
            width: inherit;

            &::placeholder {
              color: #6d6d6d;
              font-weight: 400;
            }
          }
        }
      }
    }
  }

  button {
    color: $white;
    font-size: 14px;
    font-weight: 700;
    line-height: 22px;
    letter-spacing: 0.28px;
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: center;
    background: $red-500;
    border-radius: 0 24px 24px 0;
    min-width: 148px;
    text-transform: uppercase;

    @include media-breakpoint-down(sm) {
      min-width: 56px;
    }

    span {
      @include media-breakpoint-down(sm) {
        display: none;
      }
    }
  }

  ::ng-deep {
    .search-filters {
      .ng-select {
        &-container {
          cursor: pointer !important;
          background-color: $white !important;
          padding: 10px 16px;
          border-radius: 24px;
          border: 1px solid #d1d1d1;
          height: 44px;

          * {
            cursor: pointer !important;
          }

          &:hover {
            box-shadow: none;
          }

          &.ng-has-value {
            .ng-placeholder {
              display: none;
            }
          }

          .ng-clear {
            &-wrapper {
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }

          .ng-arrow-wrapper {
            background: url('/assets/images/lexicon/caret-down.svg') no-repeat;
            height: 24px;
            width: 24px;
            transition: 0.3s;

            .ng-arrow {
              display: none !important;
            }
          }
        }

        &-opened {
          .ng-dropdown-panel {
            background: $white;
          }

          .ng-arrow-wrapper {
            transform: rotate(-180deg);
            background: url('/assets/images/lexicon/caret-down.svg') no-repeat;
          }
        }

        &-focused:not(.ng-select-opened) > .ng-select-container {
          border-color: $red-500 !important;
        }

        .ng-value {
          &-container {
            padding: 0;
          }
        }
      }

      .ng-option {
        padding: 5px 8px;

        &-selected,
        &:hover {
          color: $white !important;
          background: $red-500 !important;
        }
      }
    }
  }
  .search-icon {
    width: 18px;
    height: 18px;
  }

  .collapse {
    display: flex;
    cursor: pointer;
    justify-content: space-between;
    gap: 16px;
    color: $gray-900;
    font-size: 14px;
    font-weight: 600;
    line-height: 22px;
    padding-inline: 16px;

    @include media-breakpoint-up(lg) {
      display: none;
    }

    .arrow-wrapper {
      width: 24px;
      height: 24px;
      @include icon('lexicon/caret-down.svg');
      &.collapsed {
        rotate: 180deg;
      }
    }
  }
}
