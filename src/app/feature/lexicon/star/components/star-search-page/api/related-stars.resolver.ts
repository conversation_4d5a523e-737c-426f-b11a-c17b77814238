import { ResolveFn } from '@angular/router';
import { StarService } from '../../../services/star.service';
import { inject } from '@angular/core';
import { map, switchMap } from 'rxjs/operators';
import { StarSearchResolverData } from '../../../definitions/star.definitions';
import { of } from 'rxjs';

export const relatedStarsResolver: ResolveFn<StarSearchResolverData> = (route) => {
  const starService = inject(StarService);
  const { page, ...queryParamsWithoutPage } = route.queryParams;
  const params = {
    ...queryParamsWithoutPage,
    ...(route.data?.['searchParams'] ?? {}),
    page_limit: +(page ?? 1) - 1,
    rowCount_limit: 10,
  };

  const dailyStarSlug$ = starService.chosenDailyStar
    ? of(starService.chosenDailyStar) // We have the chosen daily star from homepage, use it
    : starService.dailyStars().pipe(
        // We don't have the chosen daily star, get all of them
        map(({ data }) => {
          const randomDailyStar = data[Math.floor(Math.random() * data?.length)]; // Choose a random one
          starService.chosenDailyStar = randomDailyStar; // Save it
          return randomDailyStar;
        })
      );

  return dailyStarSlug$.pipe(
    switchMap((dailyStar) =>
      starService
        .search({
          ...params,
          related_star_slug_filter: dailyStar?.slug || '',
        })
        .pipe(map((response) => ({ response })))
    )
  );
};
