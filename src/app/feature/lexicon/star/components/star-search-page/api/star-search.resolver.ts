import { ResolveFn } from '@angular/router';
import { StarService } from '../../../services/star.service';
import { inject } from '@angular/core';
import { map } from 'rxjs/operators';
import { StarSearchResolverData } from '../../../definitions/star.definitions';

export const starSearchResolver: ResolveFn<StarSearchResolverData> = (route) => {
  const starService = inject(StarService);

  const { page, ...queryParamsWithoutPage } = route.queryParams;

  /**
   * Search params might be included in route.queryParams, and we have to transform page property to page_limit so backend can process it
   */

  const params = {
    ...queryParamsWithoutPage,
    ...(route.data?.['searchParams'] ?? {}),
    page_limit: +(page ?? 1) - 1,
    rowCount_limit: 10,
  };

  return starService.search(params).pipe(map((response) => ({ response, query: params.name_filter })));
};
