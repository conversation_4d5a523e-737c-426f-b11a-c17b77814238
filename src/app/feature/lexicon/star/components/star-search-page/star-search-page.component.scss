@use 'shared' as *;

:host {
  display: block;

  .search {
    display: flex;
    flex-direction: column;
    gap: 8px;

    @include media-breakpoint-up(md) {
      gap: 32px;
      min-width: 630px;
    }

    &-tabs {
      display: flex;
      gap: 24px;
      padding: 3px 0;
      @include media-breakpoint-up(md) {
        margin-bottom: -16px;
      }

      &-link {
        display: block;
        padding: 0 8px;
        color: #3d3d3d;
        font-size: 20px;
        font-weight: 700;
        line-height: 29px;
        letter-spacing: 0.6px;
        text-transform: uppercase;

        &.active {
          border-bottom: 3px solid $red-500;
        }
      }
    }

    &-result {
      &-count {
        color: #1a1a1a;
        font-size: 20px;
        font-weight: 600;
        line-height: 30px;

        &-query {
          color: $red-500;
        }
      }

      &-list {
        display: flex;
        flex-direction: column;
        gap: 30px;

        @include media-breakpoint-up(lg) {
          gap: 32px;
        }

        app-star-card.style-list {
          width: 100%;

          @include media-breakpoint-up(xl) {
            margin-left: 108px;
            max-width: calc(100% - 108px);
          }
        }

        .ad {
          margin: auto;
        }
      }
    }
  }
}
