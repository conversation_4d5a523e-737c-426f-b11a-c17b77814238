@use 'shared' as *;

:host {
  display: block;
  width: 100%;

  .star-card {
    &-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }

    &-details {
      width: 100%;
    }

    &-tag {
      color: $blue-dark;
      background: $grey-1;
      font-size: 14px;
      font-weight: 500;
      line-height: 19px;
      padding: 5px;
      display: block;
      border-radius: 6px;
    }

    &-name {
      color: $gray-900;
      font-weight: 700;
      text-transform: uppercase;
      display: block;
    }
  }

  &.style-featured {
    display: flex;
    gap: 30px;

    @include media-breakpoint-down(md) {
      flex-direction: column;
      gap: 0;
    }

    .star-card {
      &-img {
        aspect-ratio: 4 / 3;
        object-fit: cover;

        &-wrapper {
          position: relative;
          flex: 0 0 auto;
          max-width: 403px;

          img {
            clip-path: polygon(0 0, 100% 0, 100% 100%, 0 calc(100% - 26px), 0 50%);
          }

          @include media-breakpoint-down(md) {
            max-width: none;
          }
        }
      }

      &-name {
        font-size: 32px;
        line-height: 37px;
        letter-spacing: 0.96px;
        padding: 8px;
        margin-bottom: 8px;

        @include media-breakpoint-down(md) {
          font-size: 28px;
          letter-spacing: 0.84px;
        }
      }

      &-tags {
        margin-bottom: 32px;
      }

      &-tag {
        @include media-breakpoint-down(md) {
          font-weight: 700;
        }
      }

      &-description {
        color: $gray-900;
        font-size: 16px;
        font-weight: 400;
        line-height: 26px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 4;
        overflow: hidden;
        margin-bottom: 32px;
      }
    }
  }

  &.style-list {
    display: flex;
    gap: 30px;

    @include media-breakpoint-down(md) {
      gap: 16px;
    }

    .mobile-only {
      display: flex;
      flex-direction: column;
      gap: 10px;
      width: 100%;

      @include media-breakpoint-up(lg) {
        display: none;
      }

      &-header {
        display: flex;
        gap: 16px;
      }
    }

    & > *:not(.mobile-only) {
      @include media-breakpoint-down(md) {
        display: none;
      }
    }

    .star-card {
      &-img {
        aspect-ratio: 4 / 3;
        object-fit: cover;
        max-width: 295px;

        @include media-breakpoint-down(md) {
          aspect-ratio: 1 / 1;
          max-width: 100px;
        }

        &-wrapper {
          flex: 0 0 auto;
          max-width: 403px;
          text-align: right;

          @include media-breakpoint-down(md) {
            max-width: 100px;
          }
        }
      }

      &-name {
        font-size: 24px;
        line-height: 27px;
        margin-bottom: 8px;

        @include media-breakpoint-down(md) {
          font-size: 20px;
        }
      }

      &-tags {
        margin-bottom: 16px;

        @include media-breakpoint-down(md) {
          margin-bottom: 0;
        }
      }

      &-description {
        color: $gray-900;
        font-size: 14px;
        font-weight: 400;
        line-height: normal;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 4;
        overflow: hidden;
        margin-bottom: 16px;

        @include media-breakpoint-down(md) {
          margin-bottom: 0;
        }
      }
    }
  }

  &.style-simple,
  &.style-simple-tag {
    .star-card {
      &-img {
        aspect-ratio: 1 / 1;
        object-fit: cover;
        width: 100%;
        margin-bottom: 16px;

        &-bottom {
          display: none;
        }
      }

      &-name {
        font-size: 20px;
        line-height: 27px;
      }
    }
  }

  &.style-simple-tag {
    .star-card {
      &-name {
        margin-bottom: 5px;
      }

      &-tag:not(:first-child) {
        @include media-breakpoint-down(md) {
          display: none;
        }
      }
    }
  }
}
