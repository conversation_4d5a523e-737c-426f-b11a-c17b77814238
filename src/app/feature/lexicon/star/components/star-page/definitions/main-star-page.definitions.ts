import { IStarCard } from '../../../definitions/star.definitions';

export interface IMainPageStarBoxTypes {
  title: string;
  url?: string[];
  starBoxType?: MainPageStarBoxTypesEnum;
  starData: IStarCard[];
}

export interface IMainPageData {
  [starBoxType: string]: IStarCard[];
}

export interface IMainPageDailyStars {
  dailyStar?: IStarCard;
  relatedStars: IStarCard[];
}

export enum MainPageStarBoxTypesEnum {
  DAILY_STAR = 'dailyStar',
  BORN_THIS_WEEK_STARS = 'bornThisWeekStars',
  HUNGARIAN_STARS = 'hungarianStars',
  INTERNATIONAL_STARS = 'internationalStars',
  RELATED_STARS = 'relatedStars',
}
