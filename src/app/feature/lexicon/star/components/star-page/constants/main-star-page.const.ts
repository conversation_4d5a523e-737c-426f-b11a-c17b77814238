import { IMainPageStarBoxTypes, MainPageStarBoxTypesEnum } from '../definitions/main-star-page.definitions';

export const MainPageStarBoxTypes: IMainPageStarBoxTypes[] = [
  { title: 'A nap sztárja', starBoxType: MainPageStarBoxTypesEnum.DAILY_STAR, starData: [] },
  {
    title: 'Ezen a héten született sztárok',
    url: ['/', 'lexikon', 'sztar', 'ezen-a-heten-szuletett-sztarok'],
    starBoxType: MainPageStarBoxTypesEnum.BORN_THIS_WEEK_STARS,
    starData: [],
  },
  {
    title: 'Legnépszerűbb magyar sztárok',
    url: ['/', 'lexikon', 'sztar', 'legnepszerubb-magyar-sztarok'],
    starBoxType: MainPageStarBoxTypesEnum.HUNGARIAN_STARS,
    starData: [],
  },
  {
    title: 'Legnépszerűbb külföldi szt<PERSON>',
    url: ['/', 'lexikon', 'sztar', 'legnepszerubb-kulfoldi-sztarok'],
    starBoxType: MainPageStarBoxTypesEnum.INTERNATIONAL_STARS,
    starData: [],
  },
  {
    title: 'Ők is érdekelhetnek',
    url: ['/', 'lexikon', 'sztar', 'kapcsolodo-sztarok'],
    starBoxType: MainPageStarBoxTypesEnum.RELATED_STARS,
    starData: [],
  },
];
