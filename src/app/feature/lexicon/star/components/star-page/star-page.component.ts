import { ChangeDetectionStrategy, Component, computed, inject, Signal } from '@angular/core';
import { StarCardComponent } from '../star-card/star-card.component';
import { StarBoxComponent } from '../star-box/star-box.component';
import { StarLinkComponent } from '../star-link/star-link.component';
import { IStarCard, IStarLink, StarCardTypes, StarLinkTypes } from '../../definitions/star.definitions';
import { StarWrapperComponent } from '../star-wrapper/star-wrapper.component';
import { LexiconBreadcrumbComponent } from '../../../components/lexicon-breadcrumb/lexicon-breadcrumb.component';
import { toSignal } from '@angular/core/rxjs-interop';
import { LexiconMetaService } from '../../../api/lexicon-meta.service';
import { ActivatedRoute } from '@angular/router';
import { IMainPageStarBoxTypes, MainPageStarBoxTypesEnum } from './definitions/main-star-page.definitions';
import { NgIf, NgTemplateOutlet } from '@angular/common';
import { StarSearchComponent } from './components/star-search/star-search.component';
import { AdvertisementAdoceanComponent, AdvertisementAdoceanStoreService } from '@trendency/kesma-ui';
import { map } from 'rxjs';

@Component({
  selector: 'app-star-page',
  imports: [
    StarCardComponent,
    StarBoxComponent,
    StarLinkComponent,
    StarWrapperComponent,
    LexiconBreadcrumbComponent,
    NgTemplateOutlet,
    StarSearchComponent,
    AdvertisementAdoceanComponent,
    NgIf,
  ],
  templateUrl: './star-page.component.html',
  styleUrl: './star-page.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StarPageComponent {
  private readonly breadcrumbService = inject(LexiconMetaService);
  private readonly activatedRoute = inject(ActivatedRoute);

  adStoreAdo = inject(AdvertisementAdoceanStoreService);

  breadcrumbItems = toSignal(this.breadcrumbService.getStarPageBreadcrumbs());
  mainPageData = toSignal(this.activatedRoute.data);
  stars: Signal<IMainPageStarBoxTypes[]> = computed(() => this.mainPageData()?.['data']);
  randomDailyStar: Signal<IStarCard | undefined> = computed(
    () => this.stars().find((value) => value.starBoxType === MainPageStarBoxTypesEnum.DAILY_STAR)?.starData?.[0]
  );

  recentNewsStarLink: IStarLink = { text: 'Legfrissebb sztárhírek' };

  protected readonly StarLinkTypes = StarLinkTypes;
  protected readonly StarCardTypes = StarCardTypes;
  protected readonly MainPageStarBoxTypesEnum = MainPageStarBoxTypesEnum;

  readonly adverts = toSignal(this.adStoreAdo.advertisemenets$.pipe(map((adsCollection) => this.adStoreAdo.separateAdsByMedium(adsCollection))));
}
