import { ChangeDetectionStrategy, Component } from '@angular/core';
import { StarSearchFilterComponent } from '../../../star-search-page/components/star-search-filter/star-search-filter.component';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-star-search',
  imports: [StarSearchFilterComponent, RouterLink],
  templateUrl: './star-search.component.html',
  styleUrl: './star-search.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StarSearchComponent {}
