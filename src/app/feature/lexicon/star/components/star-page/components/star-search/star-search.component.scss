@use 'shared' as *;

:host {
  display: block;
}

.star-search-header {
  display: flex;
  align-items: flex-start;
  gap: 24px;
  margin-bottom: 16px;

  &-link {
    font-size: 20px;
    font-weight: 700;
    line-height: 29px;
    letter-spacing: 0.6px;
    text-transform: uppercase;
    padding-inline: 8px;
    color: var(--kui-black);

    &.active {
      border-bottom: 3px solid $red-500;
    }
  }
}
