import { ResolveFn, Router } from '@angular/router';
import { inject } from '@angular/core';
import { catchError, delay, forkJoin, map, Observable, of, share, switchMap, tap, throwError } from 'rxjs';
import { IStarCard } from '../../definitions/star.definitions';
import { IMainPageDailyStars, IMainPageData, IMainPageStarBoxTypes } from './definitions/main-star-page.definitions';
import { StarService } from '../../services/star.service';
import { MainPageStarBoxTypes } from './constants/main-star-page.const';
import { getRandomCelebs } from '../../utils/star.utils';

let excludedStarSlugs: string[] = [];
const populateExcludedStarSlugs = (data: IStarCard[]): void => {
  data?.map((star) => {
    if (!excludedStarSlugs.includes(star?.slug)) {
      excludedStarSlugs.push(star.slug);
    }
  });
};

export const starMainPageResolver: ResolveFn<Observable<IMainPageStarBoxTypes[]>> = () => {
  const starService = inject(StarService);
  const router = inject(Router);

  const dailyStar$: Observable<IMainPageDailyStars> = starService.dailyStars().pipe(
    delay(200),
    map(({ data }) => data),
    switchMap((dailyStars: IStarCard[]) => {
      if (!dailyStars?.length) {
        return of({
          relatedStars: [],
        });
      }
      const randomDailyStar = dailyStars[Math.floor(Math.random() * dailyStars?.length)];
      if (randomDailyStar) {
        starService.chosenDailyStar = randomDailyStar;
      }
      return starService
        .search({
          related_star_slug_filter: randomDailyStar?.slug,
          'exclude_star_slugs_filter[]': randomDailyStar?.slug ? [...excludedStarSlugs, randomDailyStar?.slug] : excludedStarSlugs,
        })
        .pipe(
          map(({ data }) => {
            return {
              dailyStar: randomDailyStar,
              relatedStars: getRandomCelebs(data),
            };
          })
        );
    }),
    share()
  );

  excludedStarSlugs = [];

  const bornThisWeekStars$: Observable<IStarCard[]> = starService.search({ is_born_this_week_filter: 'true' }).pipe(
    map(({ data }) => getRandomCelebs(data)),
    tap(populateExcludedStarSlugs)
  );
  const internationalStar$: Observable<IStarCard[]> = starService.search({ is_hungarian_filter: 'false', 'popularity_order[]': 'desc' }).pipe(
    map(({ data }) => data?.slice(0, 4)),
    tap(populateExcludedStarSlugs)
  );
  const hungarianStar$: Observable<IStarCard[]> = starService.search({ is_hungarian_filter: 'true', 'popularity_order[]': 'desc' }).pipe(
    map(({ data }) => data?.slice(0, 4)),
    tap(populateExcludedStarSlugs)
  );

  return forkJoin({
    internationalStars: internationalStar$,
    bornThisWeekStars: bornThisWeekStars$,
    hungarianStars: hungarianStar$,
    dailyStar: dailyStar$.pipe(map(({ dailyStar }) => (dailyStar ? [dailyStar] : []))),
    relatedStars: dailyStar$.pipe(map(({ relatedStars }) => relatedStars)),
  }).pipe(
    map((data: IMainPageData) => {
      return MainPageStarBoxTypes.map((starBoxType: IMainPageStarBoxTypes) => {
        starBoxType.starData = data?.[`${starBoxType.starBoxType}`];
        return starBoxType;
      });
    }),
    catchError((err: Error) => {
      router
        .navigate(['/', '404'], {
          skipLocationChange: true,
        })
        .then();
      return throwError(() => err);
    })
  );
};
