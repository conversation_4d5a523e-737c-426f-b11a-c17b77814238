@use 'shared' as *;

:host {
  display: block;
  width: 100%;
}

.daily-star {
  &-title {
    font-size: 20px;
    font-weight: 700;
    line-height: 29px;
    letter-spacing: 0.6px;
    text-transform: uppercase;
    padding-top: 8px;
  }
}

app-star-card.daily-star-card {
  margin: 40px 0px;

  @include media-breakpoint-down(sm) {
    margin-top: 24px;
    margin-bottom: 40px;
  }
}

app-star-search {
  margin: 40px 0px;

  @include media-breakpoint-down(md) {
    margin-top: 70px;
  }

  @include media-breakpoint-down(sm) {
    margin-bottom: 30px;
  }
}

.star-box {
  &-header-link {
    margin-top: 40px;
    margin-bottom: 16px;

    @include media-breakpoint-down(sm) {
      margin-top: 30px;
    }
  }

  &-more-stars-link {
    margin-top: 16px;

    &:last-child {
      margin-bottom: 40px;
      @include media-breakpoint-down(sm) {
        margin-bottom: 30px;
      }
    }
  }
}
