<div (click)="onClose()" class="close"></div>

<div (click)="onPrevImg()" *ngIf="activeIndex > 0" class="prev nav-icon">
  <img class="rotate" alt="" src="assets/images/icons/arrow-right.svg" />
</div>

<div (click)="onNextImg()" *ngIf="activeIndex < data?.length - 1" class="next nav-icon">
  <img alt="" src="assets/images/icons/arrow-right.svg" />
</div>

<div>
  <div class="img-wrapper">
    <div *ngIf="data?.[activeIndex]?.caption as caption" class="caption">{{ caption }}</div>
    <img [src]="data?.[activeIndex]?.url?.fullSize" alt="" />
  </div>
</div>
