import { ChangeDetectionStrategy, Component, Input, output } from '@angular/core';
import { BaseComponent, GalleryImage } from '@trendency/kesma-ui';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-star-lightbox',
  imports: [NgIf],
  templateUrl: './star-lightbox.component.html',
  styleUrl: './star-lightbox.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StarLightboxComponent extends BaseComponent<GalleryImage[]> {
  closeEvent = output<void>();
  activeIndex = 0;

  @Input() set selectedImgIndex(index: number) {
    this.activeIndex = index;
  }

  onClose(): void {
    this.closeEvent.emit();
  }

  onNextImg(): void {
    if (this.activeIndex < (this.data?.length ?? 0) - 1) {
      this.activeIndex++;
    }
  }

  onPrevImg(): void {
    if (this.activeIndex > 0) {
      this.activeIndex--;
    }
  }
}
