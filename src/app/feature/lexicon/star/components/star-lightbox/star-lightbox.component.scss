@use 'shared' as *;

:host {
  position: fixed;
  z-index: 1500;
  display: flex;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #000c;
  justify-content: center;
  align-items: center;
  overflow-y: auto;

  .close {
    position: absolute;
    cursor: pointer;
    background-color: $white;
    top: 32px;
    right: 32px;
    width: 40px;
    height: 40px;
    background-image: url('/assets/images/lexicon/x.svg');
    background-repeat: no-repeat;
    background-position: center;
  }

  .nav-icon {
    position: absolute;
    cursor: pointer;
    background-color: $white;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
  }

  .prev {
    top: 50%;
    transform: translateY(50%);
    left: 20px;
  }

  .next {
    top: 50%;
    transform: translateY(50%);
    right: 20px;
  }

  .caption {
    color: #fff;
    margin-bottom: 20px;
    margin-top: 3px;
    text-align: center;
    padding: 0 32px;
    @include media-breakpoint-down(sm) {
      margin-bottom: 5px;
    }
  }

  .img-wrapper {
    max-width: 100%;
    max-height: 100%;
    width: 100%;
    height: 100%;
    margin: 0 auto;
    top: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    img {
      max-height: 70vh;
    }
  }

  .rotate {
    rotate: -180deg;
  }
}
