import { ChangeDetectionStrategy, Component, input, Input, InputSignal, TemplateRef } from '@angular/core';
import { NgTemplateOutlet } from '@angular/common';
import { StarSidebarComponent } from '../star-sidebar/star-sidebar.component';

@Component({
  selector: 'app-star-wrapper',
  imports: [NgTemplateOutlet, StarSidebarComponent],
  templateUrl: './star-wrapper.component.html',
  styleUrl: './star-wrapper.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StarWrapperComponent {
  @Input() leftContent: TemplateRef<unknown>;

  isStarProfilePage: InputSignal<boolean> = input<boolean>(false);
  isAbcSearchPage: InputSignal<boolean> = input<boolean>(false);
  isStarMainPage: InputSignal<boolean> = input<boolean>(false);
}
