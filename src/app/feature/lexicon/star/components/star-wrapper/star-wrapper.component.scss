@use 'shared' as *;

:host {
  display: block;
  position: relative;

  @include media-breakpoint-down(md) {
    margin-left: 0;
    width: 100%;
  }

  section {
    .wrapper {
      margin-top: -15px;
      display: flex;
      gap: 30px;
      margin-bottom: 80px;

      @include media-breakpoint-down(md) {
        flex-direction: column;
        margin-bottom: 94px;
      }

      & > * {
        margin-top: -30px;
      }
    }

    .left-content {
      flex-basis: 100%;

      @include media-breakpoint-down(sm) {
        margin-bottom: 24px;
      }

      ::ng-deep {
        app-lexicon-breadcrumb {
          margin-bottom: 100px;

          @include media-breakpoint-down(lg) {
            margin-bottom: 40px;
          }
        }
      }
    }

    .right-content {
      width: 100%;
      @include media-breakpoint-up(lg) {
        max-width: 400px;
      }
    }
  }
}
