<div class="star-sidebar">
  @if (isStarProfilePage()) {
    <div class="search">
      <div class="search-switcher">
        <div class="search-link active">SZTÁRKERESŐ</div>
        <a class="search-link" [routerLink]="['/lexikon', 'sztar', 'abc']">SZTÁR ABC</a>
      </div>
      <app-star-search-filter [isSidebar]="true"></app-star-search-filter>
    </div>
  } @else {
    @if (starNews().latest?.length) {
      <div class="content">
        <app-star-link
          [styleId]="StarLinkTypes.LINE_RIGHT"
          [data]="{ text: 'Legfrissebb sztárhírek' }"
          textColor="white"
          textColorOnMobile="#1A1A1A"
          lineBgColor="#FEBC51"
        >
        </app-star-link>

        <section class="articles latest-news">
          <ng-container *ngTemplateOutlet="ArticleListTemplate; context: { $implicit: starNews().latest }"></ng-container>
          <app-star-link class="latest-news-link" [data]="{ text: 'Tov<PERSON><PERSON><PERSON>', url: '/rovat/celeb' }" lineBgColor="#eee"> </app-star-link>
        </section>
      </div>
    }
  }
  <kesma-advertisement-adocean [hasNoParentHeight]="true" *ngIf="adverts()?.desktop?.box_1 as ad" [ad]="ad"></kesma-advertisement-adocean>
  <kesma-advertisement-adocean [hasNoParentHeight]="true" *ngIf="adverts()?.desktop?.prcikkfix_1 as ad" [ad]="ad"></kesma-advertisement-adocean>
  <kesma-advertisement-adocean [hasNoParentHeight]="true" *ngIf="adverts()?.desktop?.prcikkfix_2 as ad" [ad]="ad"></kesma-advertisement-adocean>
  <kesma-advertisement-adocean [hasNoParentHeight]="true" *ngIf="adverts()?.desktop?.prcikkfix_3 as ad" [ad]="ad"></kesma-advertisement-adocean>
  <kesma-advertisement-adocean [hasNoParentHeight]="true" *ngIf="adverts()?.desktop?.prcikkfix_4 as ad" [ad]="ad"></kesma-advertisement-adocean>
  <kesma-advertisement-adocean [hasNoParentHeight]="true" *ngIf="adverts()?.desktop?.prcikkfix_5 as ad" [ad]="ad"></kesma-advertisement-adocean>
  <kesma-advertisement-adocean [hasNoParentHeight]="true" *ngIf="adverts()?.mobile?.prcikkfix_1 as ad" [ad]="ad"></kesma-advertisement-adocean>
  <kesma-advertisement-adocean [hasNoParentHeight]="true" *ngIf="adverts()?.mobile?.prcikkfix_2 as ad" [ad]="ad"></kesma-advertisement-adocean>
  <kesma-advertisement-adocean [hasNoParentHeight]="true" *ngIf="adverts()?.mobile?.prcikkfix_3 as ad" [ad]="ad"></kesma-advertisement-adocean>
  <kesma-advertisement-adocean [hasNoParentHeight]="true" *ngIf="adverts()?.mobile?.prcikkfix_4 as ad" [ad]="ad"></kesma-advertisement-adocean>
  <kesma-advertisement-adocean [hasNoParentHeight]="true" *ngIf="adverts()?.mobile?.prcikkfix_5 as ad" [ad]="ad"></kesma-advertisement-adocean>

  @if (isStarMainPage()) {
    <kesma-advertisement-adocean [hasNoParentHeight]="true" *ngIf="adverts()?.mobile?.mobilrectangle_4 as ad" [ad]="ad"></kesma-advertisement-adocean>
  }

  @if (!isAbcSearchPage() && (starBornToday() || starByDailyHoroscope())) {
    <div class="content">
      <app-star-link class="latest-news-link" [styleId]="StarLinkTypes.LINE_RIGHT" [data]="{ text: 'Bors lexikon' }"> </app-star-link>
      @if (starBornToday()) {
        <div class="lexicon">
          <strong class="block-title">EZEN A NAPON SZÜLETETT</strong>
          <ng-container *ngTemplateOutlet="StarCardTemplate; context: { $implicit: starBornToday() }"></ng-container>
        </div>
      }
      @if (starByDailyHoroscope()) {
        <div class="lexicon horoscope">
          <strong class="block-title">NEKI IS EZ A HOROSZKÓPJA</strong>
          <ng-container *ngTemplateOutlet="StarCardTemplate; context: { $implicit: starByDailyHoroscope() }"> </ng-container>
        </div>
      }
    </div>
  }

  <kesma-advertisement-adocean [hasNoParentHeight]="true" *ngIf="adverts()?.desktop?.box_2 as ad" [ad]="ad"></kesma-advertisement-adocean>
  <kesma-advertisement-adocean [hasNoParentHeight]="true" *ngIf="adverts()?.desktop?.box_3 as ad" [ad]="ad"></kesma-advertisement-adocean>

  @if (starNews().other?.length) {
    <div class="content">
      <app-star-link [styleId]="StarLinkTypes.LINE_RIGHT" [data]="{ text: 'Bors sztárhírek' }" lineBgColor="#E2003B"> </app-star-link>

      <section class="articles other-news">
        <ng-container *ngTemplateOutlet="ArticleListTemplate; context: { $implicit: starNews().other }"></ng-container>
      </section>
    </div>
  }

  <kesma-advertisement-adocean [hasNoParentHeight]="true" *ngIf="adverts()?.mobile?.mobilrectangle_5 as ad" [ad]="ad"></kesma-advertisement-adocean>
  <kesma-advertisement-adocean [hasNoParentHeight]="true" *ngIf="adverts()?.desktop?.box_4 as ad" [ad]="ad"></kesma-advertisement-adocean>
  <kesma-advertisement-adocean [hasNoParentHeight]="true" *ngIf="adverts()?.desktop?.box_5 as ad" [ad]="ad"></kesma-advertisement-adocean>
</div>

<ng-template #StarCardTemplate let-star>
  <div class="lexicon-star">
    @if (star?.profileImage?.thumbnail; as thumbnail) {
      <a class="lexicon-star-thumbnail-box" [routerLink]="['/lexikon', 'sztar', star.slug]">
        <img class="lexicon-star-thumbnail" [src]="thumbnail" [alt]="star[star.displayContextName]" loading="lazy" />
      </a>
    }
    <div class="lexicon-star-details">
      <a [routerLink]="['/lexikon', 'sztar', star.slug]">
        <h2 class="lexicon-star-name">{{ star[star.displayContextName] }}</h2>
      </a>
      @if (star?.occupations?.[0]?.title; as occupationTitle) {
        <a class="occupation" [routerLink]="['/lexikon', 'sztar', 'kereso']" [queryParams]="{ 'occupations_filter[]': star?.occupations?.[0]?.slug }">
          {{ occupationTitle }}
        </a>
      }
    </div>
  </div>
</ng-template>

<ng-template #ArticleListTemplate let-articles>
  @for (article of articles; track article.id) {
    <article class="article">
      <div class="article-date">{{ article.publishDate | publishDate }}</div>
      @if ([article?.isAdultsOnly, article?.preTitle].filter(Boolean)?.length) {
        <div class="article-badges">
          @if (article?.isAdultsOnly) {
            <div class="badge">18+</div>
          }
          @if (article?.preTitle) {
            <div class="badge" [style.background-color]="article?.preTitleColor">{{ article?.preTitle }}</div>
          }
        </div>
      }
      <a [routerLink]="buildArticleUrl(article)">
        <h2 class="article-title">{{ article.title }}</h2>
      </a>
    </article>
  }
</ng-template>
