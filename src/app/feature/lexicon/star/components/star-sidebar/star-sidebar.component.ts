import { CommonModule, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, input, InputSignal, Signal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { RouterLink } from '@angular/router';
import { PublishDatePipe } from '@trendency/kesma-core';
import { AdvertisementAdoceanComponent, AdvertisementAdoceanStoreService, ArticleCard, buildArticleUrl } from '@trendency/kesma-ui';
import { of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { IStarCard, IStarHoroscope, StarLinkTypes } from '../../definitions/star.definitions';
import { StarService } from '../../services/star.service';
import { StarLinkComponent } from '../star-link/star-link.component';
import { StarSearchFilterComponent } from '../star-search-page/components/star-search-filter/star-search-filter.component';
import { randomize } from '../../../../../shared';

@Component({
  selector: 'app-star-sidebar',
  templateUrl: './star-sidebar.component.html',
  styleUrl: './star-sidebar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [StarLinkComponent, RouterLink, PublishDatePipe, NgTemplateOutlet, StarSearchFilterComponent, AdvertisementAdoceanComponent, CommonModule],
})
export class StarSidebarComponent {
  private readonly numberOfArticlesPerBox = 4;
  private readonly starService = inject(StarService);

  readonly StarLinkTypes = StarLinkTypes;
  readonly buildArticleUrl = buildArticleUrl;
  readonly Boolean = Boolean;

  isStarProfilePage: InputSignal<boolean> = input<boolean>(false);
  isAbcSearchPage: InputSignal<boolean> = input<boolean>(false);
  isStarMainPage: InputSignal<boolean> = input<boolean>(false);

  constructor(private readonly adStoreAdo: AdvertisementAdoceanStoreService) {}

  readonly starNews: Signal<{ latest: ArticleCard[]; other: ArticleCard[] }> = toSignal(
    this.starService
      .starNews({
        rowCount_limit: this.numberOfArticlesPerBox * 2,
      })
      .pipe(
        map(({ data: articles }) => ({
          latest: articles.slice(0, this.numberOfArticlesPerBox),
          other: articles.slice(this.numberOfArticlesPerBox),
        }))
      ),
    {
      initialValue: {
        latest: [],
        other: [],
      },
    }
  );

  readonly adverts = toSignal(this.adStoreAdo.advertisemenets$.pipe(map((adsCollection) => this.adStoreAdo.separateAdsByMedium(adsCollection))));

  readonly starBornToday: Signal<IStarCard | null> = toSignal(
    this.starService.search({ is_today_date_of_birth_filter: 1 }).pipe(map(({ data }) => randomize(data))),
    {
      initialValue: null,
    }
  );

  readonly starsByDailyHoroscope: Signal<IStarCard[] | null> = toSignal(
    this.starService.horoscopes().pipe(
      map(({ data }) => data.find(({ thisDayActual }) => thisDayActual) as IStarHoroscope),
      catchError(() => of(null)),
      switchMap((actualHoroscope) => {
        if (!actualHoroscope) {
          return of(null);
        }
        return this.starService
          .search({
            'horoscopes_filter[]': actualHoroscope.key,
          })
          .pipe(map(({ data }) => data));
      })
    ),
    {
      initialValue: null,
    }
  );

  readonly starByDailyHoroscope: Signal<IStarCard | null> = computed(() => {
    const starBornToday = this.starBornToday();
    const starsByDailyHoroscope = this.starsByDailyHoroscope();

    if (!starsByDailyHoroscope) {
      return null;
    }

    return randomize(starsByDailyHoroscope.filter((star) => star.id !== starBornToday?.id));
  });
}
