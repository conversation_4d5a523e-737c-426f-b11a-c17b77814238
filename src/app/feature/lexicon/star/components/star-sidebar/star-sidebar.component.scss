@use 'shared' as *;

:host {
  display: block;
  .star-sidebar {
    display: flex;
    flex-direction: column;
    gap: 40px;
    @include media-breakpoint-down(md) {
      gap: 30px;
    }
  }
  .articles {
    margin-top: 10px;
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
  .latest-news {
    background-color: $grey-1;
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 calc(100% - 26px), 0 50%);
    padding: 34px 32px 24px 32px;
    @include media-breakpoint-down(md) {
      margin-inline: -15px;
      padding: 24px;
    }
    &-link {
      margin-top: 16px;
      margin-bottom: 24px;
    }
  }
  .other-news {
    padding-block: 16px;
  }
  .article {
    display: flex;
    flex-direction: column;
    gap: 7px;
    &-title {
      overflow-wrap: break-word;
      word-break: break-word;
      line-height: 29px;
      font-weight: 600;
      color: $black;

      &:hover {
        color: $yellow;
      }
    }
    &-date {
      font-size: 14px;
      &::before {
        @include icon('icons/time.png');
        width: 14px;
        margin: 0 5px -2px 0;
        height: 14px;
        content: '';
      }
    }
    &-badges {
      display: flex;
      gap: 10px;
      .badge {
        border-radius: 6px;
        padding: 4.5px 5px;
        color: $white;
        font-weight: bold;
        font-size: 12px;
        line-height: 15px;
        text-transform: uppercase;
        background-color: $red-500;
      }
    }
  }
  .lexicon {
    display: flex;
    flex-direction: column;
    gap: 16px;
    background-color: $red-500;
    color: $white;
    padding: 24px;
    @include media-breakpoint-down(md) {
      margin-inline: -15px;
      padding: 24px 15px;
    }
    .block-title {
      font-size: 14px;
      display: block;
      margin-top: -8px;
    }
    &-star {
      display: flex;
      align-items: flex-start;
      gap: 16px;
      &-thumbnail {
        aspect-ratio: 1/1;
        object-fit: cover;
        width: 100px;
        &-box {
          flex-shrink: 0;
        }
      }
      &-name {
        color: $white;
        line-height: 27px;
        text-transform: uppercase;
        &:hover {
          color: $yellow;
        }
      }
      &-details {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
        .occupation {
          background-color: $grey-1;
          color: $blue-dark;
          font-weight: 500;
          line-height: 19px;
          font-size: 14px;
          padding: 2.5px 5px;
          border-radius: 6px;
        }
      }
    }
  }
  .horoscope {
    margin-top: 3px;
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 calc(100% - 22px), 0 50%);
    .lexicon-star {
      padding-bottom: 16px;
    }
  }
  .search {
    display: flex;
    flex-direction: column;
    gap: 8px;
    &-switcher {
      display: flex;
      gap: 24px;
      height: 48px;
      align-items: center;
    }
    &-link {
      font-size: 20px;
      font-weight: 700;
      line-height: 29px;
      letter-spacing: 0.6px;
      padding-inline: 8px;
      flex: 1;
      text-align: center;
      color: $blue-dark;
      &.active {
        color: $grey-8;
        border-bottom: 3px solid $red-500;
      }
    }
  }
}
