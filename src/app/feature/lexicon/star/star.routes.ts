import { Routes } from '@angular/router';
import { starProfileResolver } from './components/star-profile-page/api/star-profile.resolver';
import { starAbcPageResolver } from './components/star-abc-page/star-abc-page.resolver';
import { starMainPageResolver } from './components/star-page/star-page.resolver';
import { starSearchResolver } from './components/star-search-page/api/star-search.resolver';
import { relatedStarsResolver } from './components/star-search-page/api/related-stars.resolver';

export const STAR_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => import('./components/star-router-page/star-router-page.component').then((m) => m.StarRouterPageComponent),
    children: [
      {
        path: '',
        loadComponent: () => import('./components/star-page/star-page.component').then((m) => m.StarPageComponent),
        resolve: {
          data: starMainPageResolver,
        },
        data: {
          title: 'Bors lexikon',
        },
      },
      {
        path: 'kereso',
        loadComponent: () => import('./components/star-search-page/star-search-page.component').then((m) => m.StarSearchPageComponent),
        runGuardsAndResolvers: 'always',
        resolve: {
          data: starSearchResolver,
        },
        data: {
          title: 'Sztárkereső: :query',
          breadcrumb: [
            {
              label: 'Kereső',
              url: '/lexikon/sztar/kereso',
            },
          ],
        },
      },
      {
        path: 'abc',
        children: [
          {
            path: '',
            redirectTo: 'a',
            pathMatch: 'full',
          },
          {
            path: ':letter',
            loadComponent: () => import('./components/star-abc-page/star-abc-page.component').then((m) => m.StarAbcPageComponent),
            resolve: {
              data: starAbcPageResolver,
            },
            runGuardsAndResolvers: 'always',
            data: {
              title: 'Sztár ABC',
              breadcrumb: [
                {
                  label: 'Sztár ABC',
                  url: '/lexikon/sztar/abc',
                },
              ],
            },
          },
        ],
      },
      {
        path: 'legnepszerubb-magyar-sztarok',
        loadComponent: () => import('./components/star-search-page/star-search-page.component').then((m) => m.StarSearchPageComponent),
        runGuardsAndResolvers: 'always',
        resolve: {
          data: starSearchResolver,
        },
        data: {
          title: 'Legnépszerűbb magyar sztárok',
          breadcrumb: [
            {
              label: 'Legnépszerűbb magyar sztárok',
              url: '/lexikon/sztar/legnepszerubb-magyar-sztarok',
            },
          ],
          searchParams: {
            is_hungarian_filter: '1',
          },
        },
      },
      {
        path: 'ezen-a-heten-szuletett-sztarok',
        loadComponent: () => import('./components/star-search-page/star-search-page.component').then((m) => m.StarSearchPageComponent),
        runGuardsAndResolvers: 'always',
        resolve: {
          data: starSearchResolver,
        },
        data: {
          title: 'Ezen a héten született sztárok',
          breadcrumb: [
            {
              label: 'Ezen a héten született sztárok',
              url: '/lexikon/sztar/ezen-a-heten-szuletett-sztarok',
            },
          ],
          searchParams: {
            is_born_this_week_filter: '1',
          },
        },
      },
      {
        path: 'legnepszerubb-kulfoldi-sztarok',
        loadComponent: () => import('./components/star-search-page/star-search-page.component').then((m) => m.StarSearchPageComponent),
        runGuardsAndResolvers: 'always',
        resolve: {
          data: starSearchResolver,
        },
        data: {
          title: 'Legnépszerűbb külföldi sztárok',
          breadcrumb: [
            {
              label: 'Legnépszerűbb külföldi sztárok',
              url: '/lexikon/sztar/legnepszerubb-kulfoldi-sztarok',
            },
          ],
          searchParams: {
            is_hungarian_filter: '0',
          },
        },
      },
      {
        path: 'kapcsolodo-sztarok',
        loadComponent: () => import('./components/star-search-page/star-search-page.component').then((m) => m.StarSearchPageComponent),
        resolve: {
          data: relatedStarsResolver,
        },
        runGuardsAndResolvers: 'always',
        data: {
          title: 'Ők is érdekelhetnek',
          breadcrumb: [
            {
              label: ':dailyStarName',
              url: '/lexikon/sztar/:dailyStarSlug',
            },
            {
              label: 'Kapcsolódó sztárok',
              url: '/lexikon/sztar/kapcsolodo-sztarok',
            },
          ],
        },
      },
      {
        path: ':starSlug',
        loadComponent: () => import('./components/star-profile-page/star-profile-page.component').then((m) => m.StarProfilePageComponent),
        runGuardsAndResolvers: 'paramsOrQueryParamsChange',
        resolve: {
          profile: starProfileResolver,
        },
        data: {
          breadcrumb: [
            {
              label: 'Sztár ABC',
              url: '/lexikon/sztar/abc',
            },
          ],
        },
      },
    ],
  },
];
