import { BehaviorSubject, EMPTY, Observable, Subject, take, tap } from 'rxjs';
import { debounceTime, filter, map, switchMap } from 'rxjs/operators';
import { IHttpOptions } from '@trendency/kesma-core';

export class SelectPageableHttpLoader<T> {
  private readonly _currentPage$ = new BehaviorSubject<number>(0);
  private readonly _data$ = new BehaviorSubject<T[]>([]);
  private readonly _isLoading$ = new Subject<boolean>();
  private _pageLimit = 20;
  private _pageMax = 0;
  private _rowAllCount = 0;
  private previousSearchTerm?: string;
  private readonly _res$ = new BehaviorSubject<any>(null);
  private readonly _searchTerm$ = new BehaviorSubject<string>('');

  currentPage$ = this._currentPage$.asObservable();
  /**
   * Contains all the data items for the select. You can use this observable to show select options.
   */
  data$: Observable<T[]> = this._data$.asObservable();

  /**
   * Shows whether we are currently waiting for a request to finish, or not.
   */
  isLoading$ = this._isLoading$.asObservable();

  /**
   * Contains the meta property from the last received API response.
   * @private
   */
  latestMeta$ = this._res$.asObservable().pipe(
    filter((res) => !!res),
    map((res) => res.meta)
  );

  searchTerm$ = this._searchTerm$.asObservable();

  constructor(private readonly sourceRequest: (options: IHttpOptions) => Observable<any>) {
    this._searchTerm$
      ?.pipe(
        debounceTime(500),
        switchMap((searchValue: string) => {
          if (searchValue.length === 0 && !this.previousSearchTerm) {
            return EMPTY;
          }

          if (!searchValue) {
            this.init();
            return EMPTY;
          }
          this.previousSearchTerm = searchValue;
          this._currentPage$.next(0);

          return this.fetchSource({ params: this.getHttpParams() });
        })
      )
      ?.subscribe((items: T[]) => {
        this._data$.next([...items]);
      });
  }

  /**
   * Closes all observables to also trigger automatic unsubscription inside the current class instance.
   * It is recommended to use this function to prevent memory leaks.
   */
  close(): void {
    this._data$.complete();
    this._searchTerm$.complete();
    this._currentPage$.complete();
    this._res$.complete();
    this._isLoading$.complete();
  }

  /**
   * Returns the zero-based index of the current page that will be requested.
   */
  currentPage(): number {
    return this._currentPage$.value;
  }

  /**
   * Fetches the supplied API endpoint for items.
   * @param params The used HTTP params for making the request (for example: page limit, global_filter, etc)
   * @private
   */
  private fetchSource(params: IHttpOptions | Record<string, string | string[]> = {}): Observable<T[]> {
    this._isLoading$.next(true);
    return this.sourceRequest(params)?.pipe(
      take(1),
      tap((res) => {
        this._res$.next(res);
        const pageMax = res.meta?.limitable?.pageMax;
        const rowAllCount = res.meta?.limitable?.rowAllCount;
        if (pageMax !== null && pageMax !== undefined) {
          this._pageMax = res.meta.limitable.pageMax;
        }
        if (rowAllCount !== null && rowAllCount !== undefined) {
          this._rowAllCount = res.meta.limitable.rowAllCount;
        }
      }),
      map((res) => res.data),
      tap(() => {
        this._isLoading$.next(false);
        this._currentPage$.next(this.currentPage() + 1);
      })
    );
  }

  /**
   * Initializes the loader and makes the first request for the supplied API endpoint.
   */
  init(): void {
    this._currentPage$.next(0);
    this.fetchSource({ params: this.getHttpParams() })?.subscribe((source) => {
      this._data$.next(source);
    });
  }

  /**
   * Triggers the loader to load more items if it is possible.
   */
  loadMore(): void {
    if (this.currentPage() > this.pageMax()) {
      // Cannot extend source anymore...
      return;
    }

    this._isLoading$.next(true);

    this.fetchSource({ params: this.getHttpParams() })?.subscribe((source) => {
      this._data$.next([...this._data$.value, ...source]);
    });
  }

  /**
   * Return the number of items that are on a given page.
   */
  pageLimit(): number {
    return this._pageLimit;
  }

  /**
   * Returns how many pages can be requested for the given API endpoint, considering the page size.
   * This can be only used after initializing and making the first request, as this is supplied by the API meta.
   * Note that after changing the pageLimit, this value won't be changed until you make a new request with the loader.
   */
  pageMax(): number {
    return this._pageMax;
  }

  rowAllCount(): number {
    return this._rowAllCount;
  }

  /**
   * Triggers a new search for the supplied search term.
   * The search happens asynchronously with a 500ms debounce time.
   * Refer the ngOnInit function to see how the observable is being handled.
   * @param searchValue
   */
  search(searchValue: string): void {
    this._searchTerm$.next(searchValue);
  }

  /**
   * Returns the search term that was used for the last request.
   */
  searchTerm(): string {
    return this._searchTerm$.value;
  }

  /**
   * Sets the page limit for the subsequential requests. You can also use this function to set the initial page limit
   * before calling the init() method.
   * @param limit
   */
  setPageLimit(limit: number): number {
    this._pageLimit = limit;
    return this._pageLimit;
  }

  private getHttpParams(): Record<string, string> {
    return {
      page_limit: this.currentPage().toString(),
      rowCount_limit: this.pageLimit().toString(),
      ...(this.searchTerm() ? { global_filter: this.searchTerm().toString() } : {}),
    };
  }
}
