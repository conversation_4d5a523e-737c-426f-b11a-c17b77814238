import { ApiList<PERSON><PERSON><PERSON>, ComponentData, FocusPointUrlWithAspectRatio } from '@trendency/kesma-ui';

export interface IStarCard {
  id: string;
  name: string;
  birthName: string;
  artistName: string;
  displayContextName: string;
  occupations: IStarOccupation[];
  awards?: IStarAward[];
  lead: string;
  slug: string;
  profileImage: IStarProfileImage;
  [key: string]: any;
}

export interface IStarOccupation {
  title: string;
  slug: string;
}

export interface IStarProfileImage {
  detail: string;
  thumbnail: string;
  list: string;
}

export interface IStarLink {
  text: string;
  url?: string;
}

export interface IStarAward {
  title: string;
  slug: string;
}

export interface IStarSocialMedia {
  instagramUrl: string;
  facebookUrl: string;
  tiktokUrl: string;
  twitterUrl: string;
}

export interface IStarGallery {
  id: string;
  slug: string;
  title: string;
  highlightedImage: IStarHighlightedImage;
}

export interface IStarHighlightedImage {
  thumbnailUrl: string;
  createdAt: string;
  thumbnailUrlFocusedImages: string[];
}

export interface IStarProfileImage {
  detail: string;
  thumbnail: string;
  list: string;
  caption?: string;
  focusedImages?: FocusPointUrlWithAspectRatio;
}

export interface IStarBiographyContent {
  type: string;
  id: string;
  details: IStarBiographyContentDetail[];
  subComponents: ComponentData[];
}

export interface IStarBiographyContentDetail {
  type: string;
  key: string;
  value: string;
}

export interface IStarHoroscope {
  key: string;
  title: string;
  thisWeekActual: boolean;
  thisDayActual: boolean;
  dateRange: {
    from: string;
    to: string;
  };
}

export interface IStarProfile {
  id: string;
  name: string;
  birthName: string;
  artistName: string;
  displayContextName: 'name' | 'birthName' | 'artistName';
  dateOfBirth: string | Date;
  birthplace: string;
  horoscope: IStarHoroscope;
  displayContextNameInitial: string;
  height: number;
  originalHairColor: string;
  maritalStatus: string;
  children: string;
  partner: string;
  dateOfDeath: string | Date;
  occupations: IStarOccupation[];
  awards: IStarAward[];
  socialMediaContactDetails: IStarSocialMedia;
  gallery: IStarGallery;
  pet: string;
  lead: string;
  slug: string;
  profileImage: IStarProfileImage;
  biographyContent: IStarBiographyContent[];
}

export interface IStarSearchParams {
  [paramName: string]: string | string[];
}

export enum StarLinkTypes {
  LINE_LEFT = 'line-left',
  LINE_RIGHT = 'line-right',
}

export enum StarCardTypes {
  FEATURED = 'featured',
  LIST = 'list',
  SIMPLE = 'simple',
  SIMPLE_TAG = 'simple-tag',
}

export interface Birthplaces {
  birthplace: string;
  id: string;
}

export interface SearchSelectItem {
  id: string;
  title: string;
  slug: string;
}

export interface StarSearchFilterParams {
  name_filter?: string;
  from_age_filter?: string;
  to_age_filter?: string;
  'birthplaces_filter[]'?: string;
  'horoscopes_filter[]'?: string;
  'occupations_filter[]'?: string;
  'awards_filter[]'?: string;
  is_hungarian_filter?: string;
  related_star_slug_filter?: string;
  page?: number;
  display_context_name_initial_filter?: string;
}

export interface StarSearchResolverData {
  response: ApiListResult<IStarCard>;
  query?: string;
}
