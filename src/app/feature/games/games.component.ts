import { AfterViewInit, ChangeDetectionStrategy, Component, inject, OnInit, signal } from '@angular/core';
import { Advertisement, AdvertisementAdoceanComponent, createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';
import { defaultMetaInfo } from '../../shared/constants';
import { SeoService } from '@trendency/kesma-core';
import { BreadcrumbComponent, PageTitleComponent } from 'src/app/shared';

@Component({
  selector: 'app-games',
  templateUrl: './games.component.html',
  styleUrl: './games.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [AdvertisementAdoceanComponent, BreadcrumbComponent, PageTitleComponent],
})
export class GamesComponent implements OnInit, AfterViewInit {
  readonly mainGame: Advertisement = {
    zonaId: 'adoceanhumoofjrkmia',
    medium: 'all',
    bannerName: 'games_page',
    pageType: 'games',
    // priority: 10,
    masterId: 'gbNMrY9UPPm6p94.yZRBu7Jk0Ge.0ML8fqYQ76AcivP.q7',
    isAdultAd: false,
  };

  readonly gameAdDesktop: Advertisement = {
    zonaId: 'ado-xXKX3oAuNy7_dmGKKJe93BZJfT_FNAcHehylxfj0U6j.w7',
    medium: 'desktop',
    bannerName: 'games_page',
    pageType: 'games',
    masterId: '',
    isAdultAd: false,
  };

  readonly gameAdMobile: Advertisement = {
    zonaId: 'ado-LkqWLRaAkiOC66rPyP53wDE8PJLn8XB0A8usJdVbvEH.57',
    medium: 'mobile',
    bannerName: 'games_page',
    pageType: 'games',
    masterId: '',
    isAdultAd: false,
  };

  gamesIds: string[] = [
    'adoceanhuukflhuonpo',
    'adoceanhurlmrkriszj',
    'adoceanhuomdioosgei',
    'adoceanhulnkobmmlzd',
    'adoceanhuynrefjgqjy',
    'adoceanhuvoiliwpuit',
    'adoceanhuspprltjjcs',
    'adoceanhupahipqdotn',
    'adoceanhumbooconsdi',
    'adoceanhuzbffglhhih',
    'adoceanhuwcmljirlhc',
    'adoceanhutddsmvkqrx',
    'adoceanhuqekiqsefww',
    'adoceanhunfrodqojgr',
    'adoceanhukgifhniomm',
    'adoceanhuxgplkkssah',
    'adoceanhuuhgsnxlhug',
    'adoceanhurinibvfmab',
    'adoceanhuojepespqkw',
    'adoceanhulklfipjfpv',
    'adoceanhuykcmlmdkzq',
    'adoceanhuvljsojnoyl',
    'adoceanhusmqicxgttg',
    'adoceanhupnhpfuqhnf',
  ];

  readonly games = this.makeGamesAds();
  readonly canShowGames = signal<boolean>(false);

  private readonly seoService = inject(SeoService);

  ngOnInit(): void {
    const canonical = createCanonicalUrlForPageablePage('jatekok');
    canonical && this.seoService.updateCanonicalUrl(canonical);
    this.seoService.setMetaData({
      ...defaultMetaInfo,
      description: `A Bors összegyűjtötte a legjobb böngészőben játszható online játékokat.
        Válogass kedvenc HTML5 játékaid között, gyerekeknek és felnőtteknek egyaránt.`,
      title: `${defaultMetaInfo.ogSiteName} - Játékok`,
      ogTitle: `${defaultMetaInfo.ogSiteName} - Játékok`,
    });
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.canShowGames.set(true); //Force delay for the game cards to not prevent other advertisements from loading.
    });
  }

  private makeGamesAds(): Advertisement[] {
    return this.gamesIds.map((id) => {
      return {
        zonaId: id,
        medium: 'all',
        bannerName: 'games_page',
        pageType: 'games',
        // priority: 1,
        masterId: 'gbNMrY9UPPm6p94.yZRBu7Jk0Ge.0ML8fqYQ76AcivP.q7',
        isAdultAd: false,
      };
    });
  }
}
