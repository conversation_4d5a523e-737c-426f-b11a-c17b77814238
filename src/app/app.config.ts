import {
  Application<PERSON>onfig,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  importProvidersFrom,
  inject,
  mergeApplicationConfig,
  provideAppInitializer,
  provideZoneChangeDetection,
} from '@angular/core';
import { provideRouter, Router, withEnabledBlockingInitialNavigation, withInMemoryScrolling } from '@angular/router';
import { routes } from './app.routes';
import { provideClientHydration } from '@angular/platform-browser';
import { AppEnvironment, provideEncodedTransferState } from '@trendency/kesma-core';
import { provideHttpClient, withInterceptors, withInterceptorsFromDi } from '@angular/common/http';
import { GoogleTagManagerModule } from 'angular-google-tag-manager';
import { environment } from '../environments/environment';
import { provideAnimations } from '@angular/platform-browser/animations';
import { authInterceptor, portalHeaderHttpInterceptor } from './shared';
import { DateFnsConfigurationService } from 'ngx-date-fns';
import { hu } from 'date-fns/locale';
import { register as registerSwiperElement } from 'swiper/element/bundle';
import { adDebugFactory, DEV_AD_DEBUG } from '@trendency/kesma-ui';
import * as Sentry from '@sentry/angular';
import { TraceService } from '@sentry/angular';

const GTAG_PROVIDER = [{ provide: 'googleTagManagerId', useValue: environment.googleTagManager }];

const huConfig = new DateFnsConfigurationService();
huConfig.setLocale(hu);

registerSwiperElement();

export const appConfig: ApplicationConfig = {
  providers: [
    provideEncodedTransferState(),
    provideAnimations(),
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes, withEnabledBlockingInitialNavigation(), withInMemoryScrolling({ scrollPositionRestoration: 'top', anchorScrolling: 'enabled' })),
    provideClientHydration(),
    provideHttpClient(withInterceptorsFromDi(), withInterceptors([portalHeaderHttpInterceptor, authInterceptor])),
    importProvidersFrom(GoogleTagManagerModule),
    {
      provide: AppEnvironment,
      useValue: environment,
    },
    {
      provide: DateFnsConfigurationService,
      useValue: huConfig,
    },
    {
      provide: DEV_AD_DEBUG,
      useFactory: adDebugFactory,
      deps: [AppEnvironment],
    },
    ...GTAG_PROVIDER,
  ],
};

// Make sure that the Sentry providers are not accidentally passed to the SSR config (app.config.server.ts).
// The Sentry Angular SDK can only be used in the browser.
export const sentryConfig: ApplicationConfig = {
  providers: [
    {
      provide: ErrorHandler,
      useValue: Sentry.createErrorHandler(),
    },
    {
      provide: Sentry.TraceService,
      deps: [Router],
    },
    provideAppInitializer(() => {
      inject(TraceService);
    }),
  ],
};

export const appConfigWithSentry = mergeApplicationConfig(appConfig, sentryConfig);
