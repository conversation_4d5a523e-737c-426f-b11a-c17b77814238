import { ChangeDetectionStrategy, Component, inject, OnInit } from '@angular/core';
import { ChildActivationEnd, Data, NavigationEnd, Router, RouterOutlet } from '@angular/router';
import { SchemaOrgService, SchemaOrgWebpageDataTemplate, SeoService, UtilService } from '@trendency/kesma-core';
import { AnalyticsService, ScrollPositionService } from '@trendency/kesma-ui';
import { GoogleTagManagerService } from 'angular-google-tag-manager';
import { Observable } from 'rxjs';
import { buffer, filter, map, tap } from 'rxjs/operators';
import { environment } from '../environments/environment';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet],
  template: '<router-outlet></router-outlet>',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AppComponent implements OnInit {
  private readonly seoService = inject(SeoService);
  private readonly utilsService = inject(UtilService);
  private readonly schemaService = inject(SchemaOrgService);
  private readonly router = inject(Router);
  private readonly analyticsService = inject(AnalyticsService);
  private readonly gtmService = inject(GoogleTagManagerService);
  private readonly scrollPositionService = inject(ScrollPositionService);

  private isFirstNavigation = true;

  ngOnInit(): void {
    SchemaOrgWebpageDataTemplate.url = this.seoService.hostUrl;
    this.schemaService.insertSchema(SchemaOrgWebpageDataTemplate);
    if (this.utilsService.isBrowser()) {
      this.setupAnalyticsTracking();
      this.gtmService.addGtmToDom();
      this.scrollPositionService.setupScrollPositionListener();
    }
  }

  private setupAnalyticsTracking(): void {
    // Navigation end used to trigger gemius and gtag
    const navigationEnd$: Observable<NavigationEnd> = (this.router.events.pipe(filter((e) => e instanceof NavigationEnd)) as Observable<NavigationEnd>).pipe(
      tap((event: NavigationEnd) => {
        if (!this.isFirstNavigation && typeof pp_gemius_hit !== 'undefined') {
          pp_gemius_hit(environment.gemiusId, `page=${event.urlAfterRedirects}`);
        }
        // wrapped in a setTimeout because the router event fires before we can grab <title> from the page's <head>.
        setTimeout(() => {
          this.isFirstNavigation = false;
        }, 0);
      })
    );

    // Child activationEnd to get the leaf route data in order to see if we send pageViews there.
    (this.router.events.pipe(filter((e) => e instanceof ChildActivationEnd)) as Observable<ChildActivationEnd>)
      .pipe(
        // ChildActivationEnd triggers for every path activation, we need only the leaf so after navigation end
        // we get all of the childActivationEnd events and we only need the first one.
        buffer(navigationEnd$),
        map(([leafNode]: ChildActivationEnd[]) => (leafNode as ChildActivationEnd)?.snapshot?.firstChild?.data),
        map((data?: Data & { omitGlobalPageView?: boolean }) => data?.omitGlobalPageView)
      )
      .subscribe((shouldNotSendGlobalAnalytics?: boolean) => {
        if (shouldNotSendGlobalAnalytics) {
          return;
        }
        setTimeout(() => {
          this.analyticsService.sendPageView();
        }, 100);
      });
  }
}
