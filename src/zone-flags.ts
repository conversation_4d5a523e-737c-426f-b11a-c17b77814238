/* eslint-disable  @typescript-eslint/no-explicit-any */
(window as any).__zone_symbol__BLACK_LISTED_EVENTS = ['mousemove', 'pointermove', 'scroll', 'wheel', 'unload'];
(window as any).__Zone_disable_requestAnimationFrame = true;
(window as any).__Zone_disable_canvas = true;
(window as any).__Zone_disable_geolocation = true;
(window as any).__Zone_disable_XHR = true;
//(window as any).__Zone_disable_timers = true; interferes with router navigation
(window as any).__Zone_disable_on_property = true;
(window as any).__Zone_disable_ZoneAwarePromise = false;
(window as any).__Zone_disable_EventTarget = false;
