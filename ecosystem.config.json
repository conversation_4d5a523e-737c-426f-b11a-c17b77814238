{"apps": [{"name": "bors-dev", "script": "dist/server/server.mjs", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 4000}}, {"name": "bors-test", "script": "dist/server/server.mjs", "max_memory_restart": "500M", "max_restarts": 10, "exec_mode": "cluster", "instances": "4", "cwd": "/content/apps/borsfe/app", "env": {"PORT": 4000, "HTTPS_PROXY": "http://pkg-trendency:<EMAIL>:3128"}, "out_file": "/content/logs/borsfe/out.log", "err_file": "/content/logs/borsfe/err.log", "time": true, "merge_logs": true}, {"name": "bors-prod", "script": "dist/server/server.mjs", "max_memory_restart": "500M", "exec_mode": "cluster", "instances": "4", "cwd": "/content/apps/borsfe/app", "max_restarts": 10, "env": {"PORT": 30053, "HTTPS_PROXY": "http://trendency-prd:<EMAIL>:3128"}, "out_file": "/content/logs/borsfe/out.log", "err_file": "/content/logs/borsfe/err.log", "time": true, "merge_logs": true}]}